<template>
  <div :class="['rxscRmPrintReport','main-page',`${printFlag ?'print-status':''}`,`${viewFlag==='1' ?'view-status':''}`]" v-loading="createPdfFlag">
    <disease-layout :localQueryParams="localQueryParams" 
      :patientInfo="patientInfo" 
      class="printContent" 
      :showFooter="!printFlag && !sourceType && !isFromYunPacs" 
      pageTitle="乳腺超声结构化报告单"
      :readonly="true" 
      footTitle="">
      <div :id="domId" slot="body">
        <div class="print-page">
          <!-- 头部区域 -->
          <!-- <div class="rxscRm-logo">
            <img src="@/assets/images/sr/rxsc-logo.png" ref="logoWrap">
          </div> -->
          <div class="rx-print-header">
            <div class="rxscRm-header">
              <img class="header-logo" :src="xaHospital" alt="厦门大学附属翔安医院">
              <div class="rxscRm-rpt-title">乳腺超声结构化报告</div>
              <!-- <div v-if="!printFlag" @click="printFlag=true">打印</div> -->
            </div>
            <!-- 患者信息 -->
            <div class="patient-info">
              <div class="pat-item">
                <span class="light-gray-txt">姓名：</span>
                <span class="pat-item-val">{{totalRptInfo.name || patientInfo.name}}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">性别：</span>
                <span class="pat-item-val">{{totalRptInfo.sex || patientInfo.sex}}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">年龄：</span>
                <span class="pat-item-val">{{totalRptInfo.age || patientInfo.age}}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">检查号：</span>
                <span class="pat-item-val">{{ totalRptInfo.patLocalId || patientInfo.patLocalId }}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">申请科室：</span>
                <span class="pat-item-val">{{totalRptInfo.reqDeptName || patientInfo.reqDeptName }}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">病案号：</span>
                <span class="pat-item-val">{{ totalRptInfo.caseNo || patientInfo.caseNo }}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">床号：</span>
                <span class="pat-item-val">{{ totalRptInfo.bedNo || patientInfo.bedNo }}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">登记号：</span>
                <span class="pat-item-val">{{ totalRptInfo.sickId || patientInfo.sickId }}</span>
              </div>

              <div class="pat-item pat-item-col-3">
                <span class="light-gray-txt">临床诊断：</span>
                <span class="pat-item-val">{{ totalRptInfo.clinDiag || patientInfo.clinDiag }}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">申请医生：</span>
                <span class="pat-item-val">{{ totalRptInfo.reqPhysician || patientInfo.reqPhysician }}</span>
              </div>

              <div class="pat-item pat-item-col-3">
                <span class="light-gray-txt">检查项目：</span>
                <span class="pat-item-val">{{ totalRptInfo.itemName || patientInfo.itemName }}</span>
              </div>

              <div class="pat-item">
                <span class="light-gray-txt">检查设备：</span>
                <span class="pat-item-val">{{ totalRptInfo.device || patientInfo.device }}</span>
              </div>

            </div>
          </div>
          <div class="print-body rx-print-body">
            <!-- 异常且有病灶的区域 -->
            <div class="rx-abnormal">
              <!-- 病灶示意图 -->
              <div class="bz-flag-img" v-if="showBzImgFlag && bzForamtList && bzForamtList.length">
                <div>
                  <span class="bold-txt">病灶示意图：</span>
                  <!-- <span class="light-gray-txt-sm">(患者为{{examTwName}}检查{{!isNormal ? '，病灶位置随体位改变略有变化，仅供参考' : ''}})</span> -->
                  <span class="light-gray-txt-sm">(病灶位置随体位改变略有变化，仅供参考)</span>
                </div>
                <div class="bz-img">
                  <div class="map-wrap">
                    <div class="map-img" style="text-align:center;">
                      <img :src="`${publicPath}diseaseFileStore/images/rx-map.png`" alt="">
                      <template v-for="(bzPotItem, bz) in bzPotList">
                        <span v-for="(styleItem, sIndex) in bzPotItem.style" 
                          :class="{'c-pot': true, 'abs': true, 'c-angle': bzPotItem.level === '0'}" 
                          :style="styleItem"
                          :data-style="styleItem.bzStyle"
                        >
                          <span class="ft">{{bzPotItem.bzIndex}}</span>
                        </span>
                      </template>
                    </div>
                    <div class="map-color">
                      <div class="map-tl" v-for="(bz, bI) in bzTl" v-if="!bz.isHide">
                        <span :class="{'c-pot': true, 'c-angle': bz.levelText==='0'}" :style="{background: bz.color}"></span>
                        <span class="num-flag" v-for="(same, txt) in bz.sameColor" v-if="bz.sameColor">{{same}}类</span>
                        <span class="num-flag" v-if="!bz.sameColor" >{{bz.levelText}}类</span>
                      </div>
                    </div>
                  </div>
                  <div class="bz-img-tip">
                    <span class="light-gray-txt-sm">注：颜色代表BI-RADS分类；数字代表病灶编号；</span>  
                  </div>
                </div>
              </div>

              <!-- 超声描述 病灶列表-->
              <div class="bz-list" :style="{'marginBottom': bzForamtList.length ? '10px' : '0px'}">
                <div class="bold-txt" style="margin-bottom:8px;">超声描述：</div>
                <div class="rxscRm-descript" style="margin-bottom: 0;">
                  <!-- 大体描述的内容，包括基本情况，术后，假体-->
                  <div class="description" v-if="dtDescription" v-html="dtDescription"></div>
                </div>
                <div class="bz-table" v-if="bzForamtList && bzForamtList.length">
                  <div class="table-tip" v-if="showBzImgFlag">
                    <span style="font-size:14px;">乳腺病灶情况见示意图及表格：</span>
                    <span>(提示：示意图及表格仅展示典型病灶而非全部病灶)</span>
                  </div>
                  <div class="table-tip" v-else>
                    <span style="font-size:14px;">乳腺病灶情况见表格：</span>
                    <span>(提示：表格仅展示典型病灶而非全部病灶)</span>
                  </div>
                  <table border="1">
                    <thead>
                      <tr>
                        <th rowspan="2" width="50">病灶编号</th>
                        <th rowspan="2" width="50">BI-RADS</th>
                        <th colspan="3">位置</th>
                        <th rowspan="2" width="83">大小(cm)</th>
                        <th rowspan="2">回声</th>
                        <th rowspan="2">形状</th>
                        <th rowspan="2">边缘</th>
                        <th rowspan="2">血流</th>
                        <th rowspan="2">钙化</th>
                        <th rowspan="2">方向</th>
                        <th rowspan="2" width="50">后方回声</th>
                      </tr>
                      <tr>
                        <th colspan="3">方向、距乳头(cm)</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(bzItem, i) in bzForamtList">
                        <td>
                          <span :class="['bz-circle',bzItem.level==='0'?'bz-angle':'']" 
                            :style="{'background':bzItem.bztl.color,'color':bzItem.bztl.fontColor}">
                            <span :class="[bzItem.level==='0'?'ft':'']">{{i+1}}</span>
                          </span>
                        </td>
                        <td>{{bzItem.level || '-'}}类</td>
                        <td width="35">{{bzItem.side}}</td>
                        <template v-if="bzItem.posWay !== '象限定位法'">
                          <td width="45">{{bzItem.clockPos}}点</td>
                          <td width="55">
                            <span v-if="bzItem.diffRt === '99' || bzItem.diffRt === '0'">
                              {{bzItem.diffRt === '99' ? '腺体边缘' : '乳晕处'}}
                            </span>
                            <span v-else>{{bzItem.diffRt || '-'}}</span>
                          </td>
                        </template>
                        <template v-else>
                          <td width="90" colspan="2">{{bzItem.xxPos}}</td>
                        </template>
                        <td width="80">{{bzItem.bzSide}}</td>
                        <td>{{bzItem.hsType}}</td>
                        <td>{{bzItem.shape}}</td>
                        <td>{{bzItem.by}}</td>
                        <td>{{bzItem.isXl === '有血流' ? '有' : '未见'}}</td>
                        <td>{{bzItem.isGh === '有钙化' ? '有' : '未见'}}</td>
                        <td>{{bzItem.fx ? (bzItem.fx) : ''}}</td>
                        <td>{{bzItem.hfhs}}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!-- 乳腺描述和诊断 -->
            <div class="rx-content">
              <!-- 超声描述 具体文本 -->
              <div class="rxscRm-descript">
                <!-- <div class="bold-txt" v-if="isNormal || !bzForamtList || !bzForamtList.length">超声描述：</div> -->
                <!-- 除了大体描述外的内容 -->
                <div class="description" v-if="description" v-html="description"></div>
              </div>
              <!-- 超声意见 -->
              <div class="rxscRm-impress">
                <div class="bold-txt avoid-break">超声提示：</div>
                <div class="impression" v-if="impression" v-html="impression"></div>
              </div>
              <!-- 超声图像 -->
              <div class="rxscRm-rpt-img" v-if="imgList && imgList.length">
                <div class="bold-txt avoid-break">超声图像：</div>
                <div class="rpt-img-list">
                  <div class="rpt-img-item load-img" v-for="(img, i) in imgList">
                    <!-- <div class="light-gray-txt-sm">病灶{{i+1}}：</div> -->
                    <div class="rpt-img follow-break">
                      <img :src="img.srcBase64 || img.src" alt="" class="follow-break">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 报告日期和医生 -->
          <div class="rpt-date-and-name rx-print-footer">
            <div class="rpt-doctor-wrap">
              <div class="rpt-doctor">
                <span class="light-gray-txt">报告录入：{{ entryPeopleUserName }}</span>
                <img
                  v-if="false" class="sign-img"
                  :src="`${getSignImageUrl}?userId=${entryPeopleUserId}`"
                  alt=""
                  @error="imgErrorHandler"
                >
              </div>

              <div class="rpt-doctor">
                <span class="light-gray-txt">诊断医生：{{ totalRptInfo.reporter }}</span>
                <img
                  v-if="totalRptInfo.reporterUserId"
                  class="sign-img"
                  :src="`${getSignImageUrl}?userId=${totalRptInfo.reporterUserId}`"
                  alt=""
                  @error="imgErrorHandler"
                >
              </div>

              <div class="rpt-doctor" >
                <span class="light-gray-txt">审核医生：{{ totalRptInfo.affirmReporter }}</span>
                <img
                  v-if="totalRptInfo.affirmReporterUserId"
                  class="sign-img"
                  :src="`${getSignImageUrl}?userId=${totalRptInfo.affirmReporterUserId}`"
                  alt=""
                  @error="imgErrorHandler"
                >
              </div>
            </div>
            <div class="rpt-data-wrap">
              <div>本报告仅供临床医师参考，不作证明之用，签字生效。</div>
              <div class="rpt-date">
                <span class="light-gray-txt">检查时间：</span>
                <span class="black-txt">{{ totalRptInfo.examDate }} {{ totalRptInfo.examTime }}</span>
              </div>
              <div class="rpt-date">
                <span class="light-gray-txt">报告时间：</span>
                <span class="black-txt">{{totalRptInfo.reportDate}} {{ totalRptInfo.reportTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer-btn" v-if="!createPdfFlag && !isFromYunPacs && !sourceType">
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue ml-12 allow-et"
          icon="el-icon-position"
          @click="submitForm"
        >确认</el-button>
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue allow-et"
          icon="el-icon-edit"
          @click="editForm"
        >编写</el-button>
      </div>
    </disease-layout>
  </div>
</template>

<script>
import '@/assets/css/diseaseReport.scss'
import api from '@/config/api.js';
import diseaseLayout from '@/components/diseaseLayout.vue';
import diseaseReportMixin from '@/mixins/newDiseaseReport.js'
import '../../../public/template-lib/utils/structFun.js'
import * as constant from '@/config/constant.js'
import { getLocalStorage } from '@/utils/common.js';
import $ from 'jquery'
export default {
  name: "rxscRmPrintReport",
  components: {diseaseLayout},
  mixins: [diseaseReportMixin],
  data() {
    return {
      xaHospital: require('@/assets/images/sr/xa-hospital.png'),
      domId: 'rxscRmPrintReport',
      prefixName: "rxscRm-",
      createPdfFlag: false,
      description: '',
      impression: '',
      isNormal: false,  //是否正常
      resultFormData: [],
      patientInfo: {
        sex: ''
      },
      imgList: [],
      // bzImage: {},
      bzData: [],  //病灶原始数据
      bzForamtList: [],  //处理后的数据
      bzPotList: [],
      bzTl: [  //病灶图例
          { color: '#303133', levelText: '0', fontColor: '#FFF'},
          { color: '#40C71A', levelText: '2', fontColor: '#000', sameColor: ['2', '3']},
          { color: '#40C71A', levelText: '3', fontColor: '#000', isHide: true},
          { color: '#F3F055', levelText: '4A', fontColor: '#000'},
          { color: '#F5222D', levelText: '4B', fontColor: '#000', sameColor: ['4B', '4C', '5', '6']},
          { color: '#F5222D', levelText: '4C', fontColor: '#000', isHide: true},
          { color: '#F5222D', levelText: '5', fontColor: '#000', isHide: true},
          { color: '#F5222D', levelText: '6', fontColor: '#000', isHide: true},
        ],
      printFlag: false,  //是否打印
      viewFlag: 0,  //是否只显示报告模板部分
      examTwName: '',
      getSignImageUrl: api.getSignImage,
      publicPath: process.env.BASE_URL,
      print: '',
      dtDescription: '',  //大体描述
      showBzImgFlag: false,  //是否显示病灶图
      //录入人员
      entryPeopleUserId: '',
      //录入人员
      entryPeopleUserName: '',
    }
  },
  watch: {
    $route() {
      window.location.reload();
    },
    printFlag: {
      async handler(newVal) {
        if(newVal) {
          if(['1', '3'].includes(this.print)) {
            return;
          }
          this.$nextTick(async() => {
            this.printPageBreakHandler();
            await this.loadAllImages('#'+this.domId + ' img');
            window.print();
            // this.toPDF($('#' + this.domId)[0], true);
            setTimeout(() => {
              this.resetDocStyle();
              this.printFlag = false;
              if(this.isFromYunPacs) {
                window.top.postMessage({
                  message: 'srPrintFinished',
                  data: {printFlag:false}
                }, '*');
              }
            }, 50);
          });
        }
      },
      immediate: true
    },
    rptImageList: {
      handler(newVal) {
        this.imgList = [];
        // this.bzImage = {};
        newVal.forEach(async(img) => {
          this.imgToBase64(img)
          if(!img.fileName.includes('_bz')) {
            this.imgList.push(img);
          } else {
            // this.bzImage = img;
          }
        })
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    token() {
      return this.$store.state.token || getLocalStorage(constant.RT_SESSION_TOKEN) || '';
    },
    rptImageList() {
      return this.$store.state.report.rptImageList;
    },
    ypacsRptInfo() {
      return this.$store.state.report.ypacsRptInfo || {};
    },
    baseRptInfo() {
      return this.$store.state.report.baseRptInfo || {};
    },
    totalRptInfo() {
      return {...this.baseRptInfo, ...this.ypacsRptInfo}
    },
    isFromYunPacs() {
      return this.$store.state.isFromYunPacs || this.localQueryParams.fromYunPacs==='1' || this.$route.query.fromYunPacs==='1';
    },
    // 诊断医生
    diagnosticDoctor() {
      if (!this.totalRptInfo) return '';
      const { examRpt } = this.totalRptInfo;
      if (!examRpt) return '';
      const { reporter } = examRpt;
      return reporter || '';
    },
    // 审核医生
    auditDoctor() {
      if (!this.totalRptInfo) return '';
      const { examRpt } = this.totalRptInfo;
      if (!examRpt) return '';
      const { affirmReporter } = examRpt;
      return affirmReporter || '';
    }
  },
  created() {
    this.createPdfFlag = true;
    this.viewFlag = this.localQueryParams.viewRpt || this.localQueryParams.pv || 0;
    this.print = this.localQueryParams.print;
  },
  async mounted() {
    await this.getPatientInfo();
    setTimeout(() => {
      this.pageInit();
    }, 100);
  },
  methods: {
    async pageInit() {
      if(!this.patientInfo.examNo || this.patientInfo.examNo==='undefined') {
        this.$message.error('examNo必传');
        this.createPdfFlag = false;
        return;
      }
      this.resultFormData = await this.getData(this.patientInfo.examNo, 'view');
      if(!this.resultFormData || this.resultFormData.length === 0) {
        this.createPdfFlag = false;
        return;
      }
      // 动态加载病灶rxscRm-0003作为标识
      if(this.resultFormData && this.resultFormData.length) {
        let csStatus = this.resultFormData.filter(item => item.id === 'rxscRm-0001');  //超声描述情况
        // if(csStatus && csStatus.length) {
        //   let statusName = csStatus[0].child && csStatus[0].child.length ? csStatus[0].child[0].val : '';
        //   this.isNormal = statusName === '未见异常';
        // }
        let bzData = this.resultFormData.filter(item => item.id && item.id.startsWith('rxscRm-0003'));
        if(bzData && bzData.length) {
          this.bzData = bzData[0].child;
          if(this.bzData && this.bzData.length) {
            this.formatBzData(this.bzData);
          }
        }
        // let key = this.isNormal ? 'rxscRm-0021' : 'rxscRm-0020';
        // let examTwArr = this.resultFormData.filter(item => item.id === key);
        // this.examTwName = '平卧位';
        // if(examTwArr && examTwArr.length) {
        //   this.examTwName = examTwArr[0].child && examTwArr[0].child.length ? examTwArr[0].child[0].val : '';
        // }
      }

      // 判断是否要显示病灶图
      let showBzImg = this.resultFormData.filter(item => item.id === 'rxscRm-0006');
      if(showBzImg && showBzImg.length) {
        let showVal = this.findValById(showBzImg[0].child || [], 'rxscRm-0006-1-cItem--1');
        this.showBzImgFlag = !!showVal;
      } else {
        this.showBzImgFlag = true;
      }

      this.detailDataHandler();
      let params = { examNo: this.patientInfo.examNo, fileType: 'RptImg' };
      await this.getExamInfoForSR();
      await this.getReportDoc();
      this.getEnrtyUser();
      await this.$store.dispatch('report/getReportImageList', { params, vm: this });
      if(this.isFromYunPacs) {
        window.top.postMessage({
          message: 'srLoadSuccess',
          data: {}
        }, '*');
      } 
      if(this.print !== '1') {
        this.createPdfFlag = false;
      }
      setTimeout(() => {
        if(['1', '3'].includes(this.print)) {  //上传pdf
          this.submitForm();
        }
        if(this.print === '2') {   //打印
          this.printFlag = true;
        }
      }, 500)
    },
    // 提取填过的录入员
    getEnrtyUser() {
      let lryStr = this.findValByIdCommon(this.resultFormData, 'rxrm-lry');
      if(lryStr) {
        let [id, name] = lryStr.split('/');
        this.entryPeopleUserId = id;
        this.entryPeopleUserName = name;
      } else {
        this.entryPeopleUserId = this.totalRptInfo.otherInfo ? this.totalRptInfo.otherInfo.entryPeopleUserId : '';
        this.entryPeopleUserName = this.totalRptInfo.otherInfo ? this.totalRptInfo.otherInfo.entryPeople : '';
      }
    },

    // 处理详情数据,超声描述、诊断
    detailDataHandler() {
      if(this.allContent && this.allContent.docContent) {
        let {impression = '', description = ''} = this.allContent.docContent;
        this.impression = impression;
        // if(this.isNormal) {
        //   this.description = description;
        // } else {
        // }
        // console.log(description);
        let descData = this.resultFormData.filter(item => item.id && item.id === 'rxscRm-rt-100');
        let lbjDesc = [];
        let dtDesc = [];
        if(descData && descData.length) {
          let child = descData[0].child;
          let dtDescArr = ['rxscRm-rt-200', 'rxscRm-rt-300', 'rxscRm-rt-600'];  //基本情况、术后、假体
          let lbjDescArr = ['rxscRm-rt-500'];  //淋巴
          for(let dItem of child) {
            if(dtDescArr.includes(dItem.id) && dItem.val) {
              dtDesc.push(dItem.val);
              continue;
            }
            if(lbjDescArr.includes(dItem.id) && dItem.val) {
              lbjDesc.push(dItem.val);
              continue;
            }
          }
          this.dtDescription = dtDesc.join('\n');
        }

        let bzDetailDesc = this.getBzDetailDesc(this.bzForamtList);
        this.description = bzDetailDesc;

        // 特殊情况描述
        let specData = this.resultFormData.filter(item => item.id && item.id === 'rxscRm-0014');
        if(specData.length) {
          let spec = specData[0].child;
          if(spec.length && spec[0] && spec[0].val) {
            this.description += spec[0].val + '\n';
          }
        }

        // 淋巴结描述
        this.description += lbjDesc.join('');
      }
    },
    imgToBase64(image) {
      this.blobToBase64(image.blob).then(res => {
        // 转化后的base64
        this.$set(image, 'srcBase64', res)
      })
    },
    // 整合病灶各个字段的数据
    formatBzData(data) {
      this.bzForamtList = [];
      this.bzPotList = [];
      data.forEach((item, idx) => {
        let curChild = item.child || [];
        curChild = this.treeToArr(JSON.parse(JSON.stringify(curChild)))
        let posWay = this.findValById(curChild, '.001.03.00');
        let byTypes = [];
        for(let i = 1; i < 5; i++) {
          if(this.findValById(curChild, `.************-cItem--${i}`)) {
            byTypes.push(this.findValById(curChild, `.************-cItem--${i}`));
          }
        }
        // 大小
        let bzSide = [];
        let decimLen = 0;
        var startIdx = posWay === '象限定位法' ? 11 : 4;
        var endIdx = posWay === '象限定位法' ? 14 : 7;
        for(let i = startIdx; i < endIdx; i++) {
          let bI = i < 9 ? `0${i}` : i;
          let sizeVal = this.findValById(curChild, `.001.03.${bI}`);
          if(sizeVal) {
            let [num, decim = ''] = sizeVal.split('.');
            if(decim && decimLen < decim.length) {
              decimLen = decim.length;
            }
          }
        }
        for(let i = startIdx; i < endIdx; i++) {
          let bI = i < 9 ? `0${i}` : i;
          let sizeVal = this.findValById(curChild, `.001.03.${bI}`);
          if(sizeVal) {
            let value = sizeVal;
            if(decimLen > 0) {
              let [num, decim = ''] = sizeVal.split('.');
              let len = decimLen - decim.length;
              if(len > 0) {
                let fillText = new Array(len).fill('0').join('');
                value = num + '.' + decim + fillText;
              }
            }
            bzSide.push(value);
          }
        }

        let level = '';
        for(let i = 1; i < 9; i++) {
          if(this.findValById(curChild, `.001.14.01-rItem--${i}`)) {
            level = this.findValById(curChild, `.001.14.01-rItem--${i}`);
            break;
          }
        }
        let obj = {
          bzIndex: idx + 1,
          clockPos: this.findValById(curChild, '.001.03.02'),  //位于几点方向
          diffRt: this.findValById(curChild, '.001.03.03'),  //距乳头距离
          posOtherDesc: this.findValById(curChild, '.001.03.07'),  //位于-其他情况
          bzSide: bzSide.length ? bzSide.join(' × ') : '',
          maxSize: bzSide.length ? Math.max.apply(null, bzSide) : 0,
          shape: this.findValById(curChild, '.001.04.01'),  //形状
          fx: this.findValById(curChild, '.001.05.01'),  //方向
          by: this.findValById(curChild, '.001.06.01'),  //边缘
          byType: byTypes.length ? byTypes.join('、') : '',  //边缘情况
          hsType: this.findValById(curChild, '.001.07.01'),  //回声类型
          hsBjVal: this.findValById(curChild, '.001.07.01.01') || this.findValById(curChild, '.001.07.01.03'),  //回声类型-边界
          hsTsVal: this.findValById(curChild, '.001.07.01.02'),  //回声类型-透声
          hfhs: this.findValById(curChild, '.001.08.01'),  //后方回声
          isGh: this.findValById(curChild, '.001.09.01'),  //是否钙化
          ghCount: this.findValById(curChild, '.001.09.01.01'),  //钙化数量
          isXl: this.findValById(curChild, '.001.10.01'),  //是否有血流
          xlCount: this.findValById(curChild, '.001.10.01.01'),  //血流量
          otherDesc: this.findValById(curChild, '.001.16.01'),  //其他征象
          moreType: this.findValById(curChild, '.001.13.01'),  //性质
          type: this.findValById(curChild, '.001.15.01'),  //单多发
          level: level || this.findValById(curChild, '.001.14.02'),  //级别
          side: this.findValById(curChild, '.001.02.01') === '左侧' ? 'L' : 'R',  //级别
          pos: this.findValById(curChild, '.001.02.01'),
          posWay: posWay,  //定位法
          xxPos: this.findValById(curChild, '.001.03.10'),  //象限位置
        }
        obj.xxPosArr = obj.xxPos.split('、');
        obj.bztl = this.bzTl.filter(bItem => bItem.levelText === obj.level)[0] ? 
          this.bzTl.filter(bItem => bItem.levelText === obj.level)[0] : {};

        // 相关特征
        let relateDescList = [];
        if(this.findValById(curChild, '.001.11.01') === '有') {
          relateDescList.push('结构扭曲');
        }
        if(this.findValById(curChild, '.001.11.02') === '有') {
          if(this.findValById(curChild, '.001.11.02.01')) {
            relateDescList.push('导管改变(' + this.findValById(curChild, '.001.11.02.01') + ')');
          } else {
            relateDescList.push('导管改变');
          }
        }
        if(this.findValById(curChild, '.001.11.03') === '有') {
          if(this.findValById(curChild, '.001.11.03.01-cItem--1') || this.findValById(curChild, '.001.11.03.01-cItem--2')) {
            let pf = [];
            if(this.findValById(curChild, '.001.11.03.01-cItem--1')) {
              pf.push('皮肤增厚');
            }
            if(this.findValById(curChild, '.001.11.03.01-cItem--2')) {
              pf.push('皮肤回缩');
            }
            relateDescList.push(pf.join('、'))
          } else {
            relateDescList.push('皮肤改变');
          }
        }
        if(this.findValById(curChild, '.001.11.04') === '有') {
          relateDescList.push('水肿');
        }
        if(this.findValById(curChild, '.001.11.05') || this.findValById(curChild, '.001.11.06')) {
          let str = '应变弹性评估：';
          let arr = [];
          if(this.findValById(curChild, '.001.11.05')) {
            arr.push(this.findValById(curChild, '.001.11.05') + '分');
          }
          if(this.findValById(curChild, '.001.11.06')) {
            arr.push('应变比：' + this.findValById(curChild, '.001.11.06'));
          }
          relateDescList.push(str + arr.join('，'));
        }
        if(this.findValById(curChild, '.001.11.07')) {
          relateDescList.push('剪切波弹性成像：' + this.findValById(curChild, '.001.11.07'));
        }
        obj.relateText = relateDescList.join('；');  //相关特征

        // 钙化位置
        let ghPos = [];
        for(let i = 1; i < 4; i++) {
          if(this.findValById(curChild, `.001.09.01.02-cItem--${i}`)) {
            ghPos.push(this.findValById(curChild, `.001.09.01.02-cItem--${i}`));
          }
        }
        obj.ghPos = ghPos.join('、');

        // 钙化形态
        let ghType = [];
        for(let i = 1; i < 5; i++) {
          if(this.findValById(curChild, `.001.09.01.03-cItem--${i}`)) {
            ghType.push(this.findValById(curChild, `.001.09.01.03-cItem--${i}`));
          }
        }
        obj.ghType = ghType.join('、');

        // 血流位置
        let xlPos = [];
        for(let i = 1; i < 4; i++) {
          if(this.findValById(curChild, `.001.10.01.02-cItem--${i}`)) {
            xlPos.push(this.findValById(curChild, `.001.10.01.02-cItem--${i}`));
          }
        }
        if(this.findValById(curChild, `.001.10.01.03`)) {
          xlPos.push(this.findValById(curChild, `.001.10.01.03`))
        }
        obj.xlPos = xlPos.join('、');

        let potDetail = JSON.parse(JSON.stringify(obj));
        let curTlDetal = this.bzTl.filter(bItem => bItem.levelText === potDetail.level) ? 
          this.bzTl.filter(bItem => bItem.levelText === potDetail.level)[0] : null;
        // potDetail.style = {
        //   background: curTlDetal ? curTlDetal.color : '#F5F7FA',
        //   color: curTlDetal ? curTlDetal.fontColor : '#FFF',
        //   top: '',
        //   left: '',
        // }
        potDetail.style = [];
        if(!potDetail.xxPosArr.includes('腺体内')) {
          this.setPointPosition2(potDetail, curTlDetal);
          this.bzPotList.push(potDetail);
        }

        this.bzForamtList.push(obj);
      })
    },
    // 确定点的位置
    setPointPosition(item, curTlDetal) {
      let xxObj = {
        '左侧': {
          '外上象限': {clockPos: 1.5, diffRt: 3.5},
          '外下象限': {clockPos: 4.5, diffRt: 3.5},
          '内上象限': {clockPos: 10.5, diffRt: 3.5},
          '内下象限': {clockPos: 7.5, diffRt: 3.5},
          '上象限': {clockPos: 12, diffRt: 3.5},
          '下象限': {clockPos: 6, diffRt: 3.5},
          '内象限': {clockPos: 9, diffRt: 3.5},
          '外象限': {clockPos: 3, diffRt: 3.5},
          '乳头区': {clockPos: 12, diffRt: 0},
          '中间区': {clockPos: 6, diffRt: -1.6},
        },
        '右侧': {
          '外上象限': {clockPos: 10.5, diffRt: 3.5},
          '外下象限': {clockPos: 7.5, diffRt: 3.5},
          '内上象限': {clockPos: 1.5, diffRt: 3.5},
          '内下象限': {clockPos: 4.5, diffRt: 3.5},
          '上象限': {clockPos: 12, diffRt: 3.5},
          '下象限': {clockPos: 6, diffRt: 3.5},
          '内象限': {clockPos: 3, diffRt: 3.5},
          '外象限': {clockPos: 9, diffRt: 3.5},
          '乳头区': {clockPos: 12, diffRt: 0},
          '中间区': {clockPos: 6, diffRt: -1.6},
        }
      }
      //clockPos点的方向, diffRt距离乳头的距离即半径
      let {pos, clockPos, diffRt, posWay = '时钟定位法', xxPos, xxPosArr, maxSize} = item;
      if(Number(diffRt) !== 99 && Number(diffRt) !== 0) {
        // mm换成cm
        diffRt = diffRt/10;
      }
      maxSize > 0 && (maxSize = Number((maxSize/10).toFixed(2)));
      if(posWay === '时钟定位法' && (!diffRt || Number(diffRt) === 99)) {
        diffRt = !diffRt ? 3.5 : 7;
      }
      let background = curTlDetal ? curTlDetal.color : '#F5F7FA';
      let color = curTlDetal ? curTlDetal.fontColor : '#FFF';

      let dataArr = posWay === '象限定位法' ? xxPosArr : [diffRt];
      if(posWay === '象限定位法' && dataArr.length >= 2) {
        dataArr = this.rebuildDataArr(dataArr, xxObj, pos);
      }
      if(dataArr && dataArr.length) {
        dataArr.forEach(xxItem => {
          if(posWay === '象限定位法') {
            if(pos && xxItem && xxObj[pos] && xxObj[pos][xxItem]) {
              clockPos = xxObj[pos][xxItem]['clockPos'];
              diffRt = xxObj[pos][xxItem]['diffRt'];
            }
          } else {
            diffRt = xxItem;
          }
          if(String(pos) && String(clockPos) && String(diffRt)) {
            let Ox = pos === '左侧' ? 225 : 81;
            let Oy = 64;  //圆心x和y
            let degreesM = 360/(12*60); //每分钟占的度数
            let totalDegree = degreesM * clockPos * 60;  //总的度数，点(时)->分
            let angle = (2*Math.PI / 360) *(totalDegree);
            // let x = Ox + Math.sin(angle) * (diffRt * 7+11);
            // let y = Oy - Math.cos(angle) * (diffRt * 7+11);
            // 判断大小范围，扩大病灶半径确定圆心
            let defaultX = Math.sin(angle) * (diffRt * 7+11);  //默认圆心
            let defaultY = Math.cos(angle) * (diffRt * 7+11);  //默认圆心
            let bzStyle = '';
            if(maxSize >= 5) {  //大号
              if(posWay === '象限定位法' && xxObj[pos][xxItem]['rtFlag']) {
                diffRt = 2;
              }
              defaultX = Math.sin(angle) * (diffRt * 7 + 11) - 12;
              defaultY = Math.cos(angle) * (diffRt * 7 + 11) + 12;
              bzStyle = 'large';
            } else if(maxSize >=2 && maxSize < 5) {  //中号
              defaultX = Math.sin(angle) * (diffRt * 7 + 14) - 7;
              defaultY = Math.cos(angle) * (diffRt * 7 + 14) + 7;
              bzStyle = 'mediumn'
            }
            
            let x = Ox + defaultX;
            let y = Oy - defaultY;
            item.style.push({
              background,
              color,
              left: x.toFixed(2) + 'px',
              top: y.toFixed(2) + 'px',
              bzStyle: bzStyle
            })
          }
        })
      }
    },
    // 确定点的位置
    setPointPosition2(item, curTlDetal) {
      let xxObj = {
        '左侧': {
          '外上象限': {clockPos: 1.5, diffRt: 3.5},
          '外下象限': {clockPos: 4.5, diffRt: 3.5},
          '内上象限': {clockPos: 10.5, diffRt: 3.5},
          '内下象限': {clockPos: 7.5, diffRt: 3.5},
          '上象限': {clockPos: 12, diffRt: 3.5},
          '下象限': {clockPos: 6, diffRt: 3.5},
          '内象限': {clockPos: 9, diffRt: 3.5},
          '外象限': {clockPos: 3, diffRt: 3.5},
          '乳头区': {clockPos: 12, diffRt: 0},
          '中间区': {clockPos: 6, diffRt: -1.6},
        },
        '右侧': {
          '外上象限': {clockPos: 10.5, diffRt: 3.5},
          '外下象限': {clockPos: 7.5, diffRt: 3.5},
          '内上象限': {clockPos: 1.5, diffRt: 3.5},
          '内下象限': {clockPos: 4.5, diffRt: 3.5},
          '上象限': {clockPos: 12, diffRt: 3.5},
          '下象限': {clockPos: 6, diffRt: 3.5},
          '内象限': {clockPos: 3, diffRt: 3.5},
          '外象限': {clockPos: 9, diffRt: 3.5},
          '乳头区': {clockPos: 12, diffRt: 0},
          '中间区': {clockPos: 6, diffRt: -1.6},
        }
      }
      //clockPos点的方向, diffRt距离乳头的距离即半径
      let {pos, clockPos, diffRt, posWay = '时钟定位法', xxPos, xxPosArr, maxSize} = item;
      if(Number(diffRt) !== 99 && Number(diffRt) !== 0) {
        // 已经是cm单位，不再需要转换
        // diffRt = diffRt/10;  // 删除这行，因为输入已经是cm
      }
      // maxSize已经是以cm为单位，不需要转换
      // maxSize > 0 && (maxSize = Number((maxSize/10).toFixed(2))); // 删除这行

      if(posWay === '时钟定位法' && (!diffRt || Number(diffRt) === 99)) {
        diffRt = !diffRt ? 3.5 : 7;
      }
      let background = curTlDetal ? curTlDetal.color : '#F5F7FA';
      let color = curTlDetal ? curTlDetal.fontColor : '#FFF';

      let dataArr = posWay === '象限定位法' ? xxPosArr : [diffRt];
      if(posWay === '象限定位法' && dataArr.length >= 2) {
        dataArr = this.rebuildDataArr(dataArr, xxObj, pos);
      }
      if(dataArr && dataArr.length) {
        dataArr.forEach(xxItem => {
          if(posWay === '象限定位法') {
            if(pos && xxItem && xxObj[pos] && xxObj[pos][xxItem]) {
              clockPos = xxObj[pos][xxItem]['clockPos'];
              diffRt = xxObj[pos][xxItem]['diffRt'];
            }
          } else {
            diffRt = xxItem;
          }
          if(String(pos) && String(clockPos) && String(diffRt)) {
            let Ox = pos === '左侧' ? 225 : 81;
            let Oy = 64;  //圆心x和y
            let degreesM = 360/(12*60); //每分钟占的度数
            let totalDegree = degreesM * clockPos * 60;  //总的度数，点(时)->分
            let angle = (2*Math.PI / 360) *(totalDegree);
            // let x = Ox + Math.sin(angle) * (diffRt * 7+11);
            // let y = Oy - Math.cos(angle) * (diffRt * 7+11);
            // 判断大小范围，扩大病灶半径确定圆心
            let defaultX = Math.sin(angle) * (diffRt * 7+11);  //默认圆心
            let defaultY = Math.cos(angle) * (diffRt * 7+11);  //默认圆心
            let bzStyle = '';
            if(maxSize >= 5) {  //大号
              if(posWay === '象限定位法' && xxObj[pos][xxItem]['rtFlag']) {
                diffRt = 2;
              }
              defaultX = Math.sin(angle) * (diffRt * 7 + 11) - 12;
              defaultY = Math.cos(angle) * (diffRt * 7 + 11) + 12;
              bzStyle = 'large';
            } else if(maxSize >=2 && maxSize < 5) {  //中号
              defaultX = Math.sin(angle) * (diffRt * 7 + 14) - 7;
              defaultY = Math.cos(angle) * (diffRt * 7 + 14) + 7;
              bzStyle = 'mediumn'
            }

            let x = Ox + defaultX;
            let y = Oy - defaultY;
            item.style.push({
              background,
              color,
              left: x.toFixed(2) + 'px',
              top: y.toFixed(2) + 'px',
              bzStyle: bzStyle
            })
          }
        })
      }
    },

    // 多象限方法数据重构处理
    rebuildDataArr(dataArr, xxObj, pos) {
      let addBzMap = {
        '外上象限': [],
        '外下象限': [],
        '内上象限': [],
        '内下象限': [],
        '上象限': ['外上象限+内上象限', '外上象限+内上象限+上象限', '外上象限+上象限', '内上象限+上象限'],
        '下象限': ['外下象限+内下象限', '外下象限+内下象限+下象限', '外下象限+下象限', '内下象限+下象限'],
        '内象限': ['内上象限+内下象限', '内上象限+内下象限+内象限', '内上象限+内象限', '内下象限+内象限'],
        '外象限': ['外上象限+外下象限', '外上象限+外下象限+外象限', '外上象限+外象限', '外下象限+外象限'],
        '中间区': [
          '上象限+下象限',
          '内象限+外象限'
        ],
      }
      let dataStr = dataArr.join('+');
      dataStr = dataStr.replace('+乳头区', '');   //先忽略乳头区
      let xxPosArr = [];
      for(let key in addBzMap) {
        let item = addBzMap[key];
        if(item.indexOf(dataStr) > -1 || key === dataStr) {
          if(key === '中间区') {
            if(dataArr.indexOf('乳头区') > -1) {
              xxPosArr = [key];
            }
          } else {
            xxPosArr = [key];
          }
          break;
        }
      }
      if(xxPosArr.length) {
        xxObj[pos][xxPosArr[0]]['rtFlag'] = false;
        // 非中间区的处理乳头区数据
        if(xxPosArr.indexOf('中间区') === -1 && dataArr.indexOf('乳头区') > -1) {
          xxObj[pos][xxPosArr[0]]['diffRt'] = 0;  //靠近乳头区
          xxObj[pos][xxPosArr[0]]['rtFlag'] = true;  //靠近乳头区
        }
      }
      return xxPosArr;
    },
    // 找出指定id的值
    findValById(data, id, joinChar) {
      let idData = data.filter(item => item.id.endsWith(id) || item.id.includes(`${id}-rItem--`));
      let value = [];
      if(idData && idData.length) {
        value = idData.map(item => item.val);
      }
      return value.join(joinChar || '、');
    },

    // 获取病灶除表格外的描述
    getBzDetailDesc(bzDetailList) {
      let str = '';
      if(bzDetailList && bzDetailList.length) {
        let bzStrList = [];
        bzDetailList.forEach((bzDetail, i) => {
          let bzStr = '';
          
          let tempList = [], tempStr = '';
          if(bzDetail.posOtherDesc) {
            tempList.push(bzDetail.posOtherDesc); //其他情况
          }

          if(bzDetail.hsBjVal || bzDetail.hsTsVal) {
            tempStr = `${bzDetail.hsType}结节`;
            // 边界
            let hsDetail = [];
            if(bzDetail.hsBjVal) {
              hsDetail.push(bzDetail.hsBjVal);
            }
            if(bzDetail.hsTsVal) {
              hsDetail.push(bzDetail.hsTsVal);
            }
            if(hsDetail.length) {
              tempStr += `(${hsDetail.join('、')})`;
            }
            tempList.push(tempStr);  //回声类型
          }

          if(bzDetail.by === '不光整' && bzDetail.byType) {
            tempStr = `边缘${bzDetail.by}`;
            if(bzDetail.byType) {
              tempStr += `(${bzDetail.byType})`;
            }
            tempList.push(tempStr);  //边缘
          }

          // 钙化
          if(bzDetail.isGh === '有钙化') {
            if(bzDetail.ghPos || bzDetail.ghType) {
              tempList.push('可见钙化');
              if(bzDetail.ghPos) {
                tempList.push('呈' + bzDetail.ghPos);
              }
              if(bzDetail.ghType) {
                // if(!bzDetail.ghPos) {
                //   tempList.push('可见钙化灶');
                // }
                tempList.push((bzDetail.ghCount || '') + bzDetail.ghType);
              }
            } else {
              tempList.push('可见钙化灶');
            }
          }

          if(tempList.length) {
            bzStr += tempList.join('，') + '。';
          } 

          if(bzDetail.isXl === '有血流' && (bzDetail.xlCount || bzDetail.xlPos)) {
            bzStr += 'CDFI:';
            if(bzDetail.xlCount || bzDetail.xlPos) {
              bzStr += '结节内可见血流信号，呈' + bzDetail.xlCount + (bzDetail.xlPos ? bzDetail.xlPos : '血流');
            } else {
              bzStr += '结节内可见血流信号';
            }
            bzStr += '。'
          } 

          if(bzDetail.relateText) {  //相关特征
            bzStr += '相关特征：' + bzDetail.relateText + '。';
          }
          if(bzDetail.otherDesc) {  //其他征象
            bzStr += '其他征象：' + bzDetail.otherDesc + '。';
          }
          if(bzStr) {
            bzStrList.push(`病灶${i+1}：` + bzStr);
          }
        })

        if(bzStrList.length) {
          str += bzStrList.join('\n') + '\n';
        }
      }
      return str;
    },

    // 处理打印的分页
    async printPageBreakHandler() {
      let previewPage = $('#' + this.domId).find(`.print-page:eq(0)`).clone();  //原页面
      let printHtml = '<div class="print-page">' + previewPage.html() + '</div>';
      let params = {
        printHtml: printHtml,
        pageSelector:'.print-page', 
        headerSelector:'.rx-print-header', 
        footerSelector:'.rx-print-footer', 
        bodySelector:'.rx-print-body',
        showPageCounter: true
      }
      await this.rebuildPdfPage(params);
    },

    // 图像加载失败
    imgErrorHandler(vm) {
      $(vm.target).parents('.rpt-doctor').remove();
    },

    // 提交
    submitForm(subParams) {
      // this.printPageBreakHandler()
      // return
      this.createPdfFlag = true;
      this.printFlag = true;
      this.$nextTick(async() => {
        await this.printPageBreakHandler();
        await this.loadAllImages('#'+this.domId + ' img');
        let pdfRes = await this.toPDF($('#' + this.domId)[0]);
        if(window.srSendMessage && typeof window.srSendMessage === 'function') {
          if(typeof pdfRes === 'object') {
            pdfRes.submitType = 'pdf';
            pdfRes.type = '30';
          }
          window.srSendMessage(pdfRes);
        }
      })
      return 'pdf';
    },

    // 编写
    editForm() {
      this.srCommonOuterOptHandler({type: '4'})
    },

    resetDocStyle() {
      $('#'+this.domId).find('.page-clone').remove();
      $('#'+this.domId).find('.rt-page-body').removeClass('rt-page-body').removeAttr('total-height');
      $('#'+this.domId).find('.rt-print-add-page').removeClass('rt-print-add-page');
      $('#'+this.domId).find('.rt-page-counter').removeClass('show');
      $('#'+this.domId).find('.print-body').css('height', 'unset');
      this.createPdfFlag = false;
      this.printFlag = false;
    }
  }
}
</script>
<style>
@page{
  margin: 0;
}
@page:first {
  margin-top: 0;
}
</style>
<style lang="scss">
@media print {
  body {
    -webkit-print-color-adjust: exact !important;   /* Chrome, Safari */
    color-adjust: exact !important;                 /*Firefox*/
  }
  html {
    overflow: unset !important;
  }
  #app {
    overflow: unset !important;
  }
}
// break-after: page;
.rxscRmPrintReport { 
  font-family: PingFangSC-Regular, PingFang SC;
  .rx-print-header{
    padding: 0 56px;
  }
  .disease-body {
    background: #F5F7FA;
  }
  .widthAndAuto {
    width: 780px;
  }
  #rxscRmPrintReport {
    width: 100%;
    height: 100%;
    position: relative;
    * {
      box-sizing: border-box;
    }
    .print-page {
      width: 780px;
      height: unset;
      min-height: 1080px;
      background: #FFF;
      border: 1px solid #E6E6E6;
      position: relative;
      padding-bottom: 90px;
      // & + .print-page {
      //   margin-top: 15px;
      // }
      .rxscRm-logo {
        width: 100%;
        height: 60px;
        img {
          width: 100%;
        }
      }
      .print-body {
        padding: 0 56px;
      }
      .rxscRm-header {
        text-align: center;
        padding-top: 12px;
        margin-bottom: 10px;
        .rxscRm-hos {
          font-weight: bold;
          font-size: 24px;
          color: #000;
          margin-bottom: 8px;
        }
        .rxscRm-rpt-title {
          font-size: 18px;
          color: #000;
        }
      }
      .patient-info {
        padding-top: 6px;
        border-top: 1px solid #999;
        border-bottom: 1px solid #999;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;
        .pat-item {
          width: 25%;
          margin-bottom: 6px;
          display: flex;
          .pat-item-val {
            flex: 1;
            word-break: break-all;
            font-size: 14px;
            color: #000;
          }
        }
        .pat-item-col-3 {
          width: 75%;
        }
        .w3t {
          display: inline-block;
          width: 56px;
          text-align: right;
        }
        .w4t {
          display: inline-block;
          width: 70px;
          text-align: right;
        }
      }
      .bz-flag-img {
        margin-bottom: 0;
        .bz-img {
          padding: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          img {
            width: 320px;
          }
        }
        .map-wrap {
          border: 1px solid #C8D7E6;
          width: 327px;
          .map-img {
            position: relative;
            width: 320px;
            height: 140px;
          }
          .map-color {
            padding: 8px;
            padding-right: 0;
            display: flex;
            align-items: center;
            border-top: 1px solid #C8D7E6;
            box-sizing: border-box;
          }
          .map-color .map-tl {
            background: #F5F7FA;
            border: 1px solid #C8D7E6;
            border-radius: 4px;
            padding: 4px;
            height: 20px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
          }
          .map-color .map-tl + .map-tl {
            margin-left: 4px;
          }
          .map-color .c-pot {
            width: 12px;
            height: 12px;
          }
          .c-pot {
            display: inline-block;
            width: 14px;
            height: 14px;
            border: 1px solid #000;
            border-radius: 50%; 
            line-height: 1;
            text-align: center;
            font-size: 12px;
            z-index: 3;
            &[data-style="mediumn"] {
              width: 28px;
              height: 28px;
              line-height: 28px;
              font-size: 18px;
              z-index: 2;
              &.c-angle {
                border-left-width: 14px;
                border-right-width: 14px;
                border-bottom-width: 28px;
                .ft {
                  margin-left: -5px;
                  margin-top: 3px;
                }
              }
            }
            &[data-style="large"] {
              width: 40px;
              height: 40px;
              line-height: 40px;
              font-size: 24px;
              z-index: 1;
              &.c-angle {
                border-left-width: 20px;
                border-right-width: 20px;
                border-bottom-width: 40px;
                .ft {
                  margin-left: -6px;
                  margin-top: 5px;
                }
              }
            }
          }
          .abs {
            position: absolute;
          }
          .c-angle {
            border-radius: 0;
            border-left: 7px transparent solid;
            border-right: 7px transparent solid;
            border-bottom: 14px #303133 solid;
            border-top: 0 transparent solid;
            background: none !important;
            .ft{
              text-align: left;
              margin-left: -3px;
              margin-top: 2px;
              display: block;
            }
          }
          .map-color .c-angle {
            border-bottom-width: 12px;
          }
          .num-flag {
            color: #000;
            font-size: 12px;
            margin-left: 4px;
          }
        }
        .bz-img-tip {
          margin-left: -55px;
          margin-top: 8px;
        }
      }
      .bz-list {
        margin-bottom: 16px;
        .bz-table {
          margin-top: 0;
          .table-tip {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #333;
          }
          table {
            border-collapse: collapse;
            width: 100%;
            border-top: none;
            border-color: #999;
            position: relative;
          }
          thead {
            page-break-inside: avoid;
            tr {
              page-break-inside: avoid;
              th {
                color: #909399;
                font-size: 12px;
                background: #F5F7FA;
                font-weight: normal;
                padding: 4px 8px;
                page-break-inside: avoid;
                height: 24px;
              }
            }
          }
          tbody {
            page-break-inside: avoid;
            tr {
              height: 40px;
              color: #303133;
              font-size: 12px;
              page-break-inside: avoid;
              td {  
                text-align: center;
                padding: 0 3px;
                page-break-inside: avoid;
              }
            }
            .bz-circle {
              width: 24px;
              height: 24px;
              line-height: 24px;
              display: inline-block;
              border-radius: 50%;
              border: 1px solid #000;
            }
            .bz-angle {
              border-radius: 0;
              border-left: 12px transparent solid;
              border-right: 12px transparent solid;
              border-bottom: 24px #303133 solid;
              border-top: 0 transparent solid;
              background: none !important;
              .ft {
                text-align: left;
                margin-left: -3px;
                margin-top: 2px;
                display: block;
              }
            }
          }
        }
      }
      .rxscRm-descript {
        margin-bottom: 12px;
        // min-height: 304px;
        .description {
          word-break: break-all;
          color: #333;
          white-space: pre-line;
          font-size: 14px;
          line-height: 24px;
          page-break-inside: avoid;
        }
      }
      .rxscRm-impress {
        margin-bottom: 12px;
        // min-height: 206px;
        page-break-inside: avoid;
        .impression {
          margin-top: 8px;
          word-break: break-all;
          color: #333;
          white-space: pre-line;
          font-size: 14px;
          line-height: 24px;
        }
      }
      .rxscRm-rpt-img {
        margin-bottom: 12px;
        .rpt-img-list {
          // display: flex;
          // flex-wrap: wrap;
          .rpt-img-item {
            display: inline-block;
            margin-top: 8px;
          }
          .rpt-img {
            width: 160px;
            height: 120px;
            margin-right: 32px;
            background: #000;
            margin-top: 8px;
            page-break-inside: avoid;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              page-break-inside: avoid;
            }
          }
        }
      }
      .rpt-date-and-name {
        position: absolute;
        bottom: 10px;
        height: 90px;
        left: 56px;
        right: 56px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .rpt-doctor-wrap {
          display: flex;
          justify-content: flex-end;
          border-bottom: 1px solid #999;
          padding-bottom: 4px;
          margin-bottom: 4px;
          .rpt-doctor {
            width: 32%;
            font-size: 14px !important;
            & + .rpt-doctor {
              margin-left: 12px;
            }
            .sign-img {
              width: 100px;
              height: 32px;
              vertical-align: middle;
              object-fit: contain;
            }
          }
        }
        .rpt-data-wrap {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          .rpt-date {
            span {
              font-size: 12px !important;
            }
            & + .rpt-date {
              margin-left: 12px;
            }
          }
        }
      }
      .bold-txt {
        font-size: 18px;
        font-weight: 600;
        color: #3D3D3D;
      }
      .black-txt {
        font-size: 14px;
        color: #000;
      }
      .black-txt-sm {
        font-size: 12px;
        color: #000;
      }
      .gray-txt {
        font-size: 14px;
        color: #333;
      }
      .light-gray-txt {
        font-size: 14px;
        color: #606266;
      }
      .light-gray-txt-sm {
        font-size: 12px;
        color: #606266;
      }
    }
  }
  &.view-status {
    min-width: unset;
    .printContent {
      overflow: unset;
    }
    .disease-header {
      display: none;
    }
    .disease-body {
      padding: 12px;
      overflow: unset;
    }
  }
  &.print-status {
    min-width: unset;
    .printContent {
      overflow: unset;
    }
    .disease-header {
      display: none;
    }
    .disease-body {
      padding: 0;
      overflow: unset;
      background: #fff;
    }
    .print-page {
      border: none !important;
    }
    .rxscRm-logo {
      margin-left: -5px !important;
      margin-right: -5px !important;
      width: 790px !important;
    }
    .bz-table thead th {
      color: #666 !important;
    }
    .page-counter {
      position: absolute;
      font-size:12px;
      color:#000;
      right:5px;
      font-weight: bold;
    }
  }
}

.header-logo {
  width: 290px;
}
</style>