// var winTopParentStoreUserState = null;
// if(window.top.parentVueInstance && window.top.parentVueInstance.$store && 
//     window.top.parentVueInstance.$store.state && 
//     window.top.parentVueInstance.$store.state.user) {
//       winTopParentStoreUserState = window.top.parentVueInstance.$store.state.user;
// } else {
//   if(window === window.top && (location.href.indexOf('pypacs') > -1 || location.href.indexOf('3686') > -1)) {
//     winTopParentStoreUserState = JSON.parse(window.localStorage.getItem('webpacs_pathology') || '{}');
//   }
// }

/**
 * @method saveOrSubmit
 * @param {object} params 参数对象
 * @returns {Boolean} 操作是否成功，0:成功 1:失败
 * @desc 保存/提交公共方法
 */
async function saveOrSubmit(params) {
  // saveOrSubmitHandler为各个病种页面对外定义的全局方法，用于提交、保存,params.isSubmit:1提交0保存
  if(!saveOrSubmitHandler || typeof saveOrSubmitHandler !== 'function') {
    console.error('js保存方法不存在');
    return false;
  }
  var res = await saveOrSubmitHandler(params);
  // 'stop'是由于两癌筛查中间有转换canvas的操作，或其他校验性操作
  if(res !== 'stop') {
    if(!res) {
      // console.error('保存失败');
      srSendMessage(res, params.isSubmit);
      return false;
    }
    if(res !== 'pdf') {
      srSendMessage(res, params.isSubmit);
    }
    return Boolean(res);
  }
}

// 对外提供的一系列操作方法
/**
 * @method srSave
 * @param {Object} extraParam 额外的参数
 * @returns {Boolean} 操作是否成功，0:成功 1:失败
 * @desc 保存
 */
window.srSave = async function(extraParam) {
  if(location.href.indexOf('trace') > -1 || location.href.indexOf('preview=1') > -1) {
    return;
  }
  extraParam = extraParam || {};
  // 0保存
  var params = {
    isSubmit: '0',
    ...extraParam
  }
  var res = await saveOrSubmit(params);
  return Boolean(res);
}
/**
 * @method srSubmit
 * @param {Object} extraParam 额外的参数
 * @returns {Boolean} 操作是否成功，0:成功 1:失败
 * @desc 提交
 */
window.srSubmit = async function(extraParam) {
  if(location.href.indexOf('trace') > -1 || location.href.indexOf('preview=1') > -1) {
    return;
  }
  extraParam = extraParam || {};
  // 1提交
  var params = {
    isSubmit: '1',
    ...extraParam
  }
  var res = await saveOrSubmit(params);
  return Boolean(res);
}

/**
 * @method srEdit
 * @desc 编辑
 */
window.srEdit = function() {
  if(location.href.indexOf('trace') > -1 || location.href.indexOf('preview=1') > -1) {
    return;
  }
  window.editSrHandler && window.editSrHandler();  //报告内的方法
}

/**
 * @method srSendMessage
 * @param {Object|Boolean} params 任意参数
 * @desc 发送消息通知C++端操作结果及回传参数
 */
window.srSendMessage = function(params, isSubmit, winName) {
  var postJson = {};
  var funNameMap = {
    '1': 'srSubmit',  //提交
    '0': 'srSave',   //保存
  }
  var param = params.param || null;
  var pResult = params.result && typeof params.result !== 'string' ? params.result : {};
  const funName = isSubmit ? funNameMap[isSubmit] : undefined;
  if(!Boolean(params)) {
    postJson = {
      type: typeof params === 'object' ? params.type : '',
      status: '1',
      message: '操作失败',
      result: {
        examNo: window.patientInfo ? window.patientInfo.examNo : '',
        funName,
        ...pResult
      }
    }
  } else {
    var submitType = params.submitType;
    var status = params.status;
    var type = params.type;
    var message = params.message;
    delete params.type;
    delete params.status;
    delete params.message;
    if(status !== '0') {
      postJson = {
        type: type,
        submitType,
        status: status || '1',
        message: message || '操作失败',
        result: {
          ...params,
          ...pResult,
          examNo: params.examNo || (window.patientInfo ? window.patientInfo.examNo : ''),
          reportNo: params.docNo || (window.patientInfo ? window.patientInfo.docNo : ''),
          funName,
        }
      }
    } else {
      var docContent = params.docContent || {};
      var busInfo = docContent.busInfo || {};
      delete docContent.busInfo;
      delete docContent.docAttr;
      delete params.docContent;
      // 将所有数据返回
      postJson = {
        type: type,
        status: status,
        submitType,
        message: message || '操作成功',
        result: {
          ...params,
          ...pResult,
          funName,
          examNo: busInfo.busId || busInfo.examNo || params.examNo || (window.patientInfo ? window.patientInfo.examNo : '') || '',
          reportNo: params.docNo || (window.patientInfo ? window.patientInfo.docNo : ''),
          reportId: params.reportId || params.docId,
          reportName: params.patternName,
          patternName: params.patternName,
          showTitle: params.showTitle || params.patternName,
        }
      }
      if(JSON.stringify(docContent) !== '{}') {
        postJson.result = {
          ...postJson.result,
          description: docContent.description || '',
          impression: docContent.impression || '',
          recommendation: docContent.recommendation || '',
          rptImgs: docContent.rptImgs,
          isAbnormal: docContent.isAbnormal || '',
          ...docContent
        }
      }
    }
  }
  if(param) {
    if(param.id) {
      postJson.id = param.id;
      delete param.id;
    }
    if(JSON.stringify(param) !== '{}') {
      postJson.param = param;
    }
  }
  if(postJson.result) {
    // 第三方对接result仅返回文档的字段,除webpacs(fromWebpacs === true)
    if(window.ReturnCallJsResult || !postJson.result.fromWebpacs) {
      // 标准字段
      var srStandardParam = ['funName', 'examNo', 'reportNo', 'reportId', 'description', 'impression', 'recommendation',
        'isAbnormal', 'examParam', 'measureParam', 'organNames', 'reporter', 'reportDate', 'affirmReporter', 'affirmDate',
        'rptImgs', 'busId', 'busType', 'docId', 'examTechnician', 'actType', 'content', 'patternId',
        'pregnancyWeeks', 'areaType', 'memo', 'measureParam', 'lastMensesDate',
      ];
      var newResult = {};
      for(var i = 0; i < srStandardParam.length; i++) {
        var key = srStandardParam[i];
        if(postJson.result[key] !== undefined) {
          newResult[key] = postJson.result[key];
        }
      }
      postJson.result = newResult;
    }
  }
  if(window.ReturnCallJsResult) {
    console.log('ReturnCallJsResult----request：', JSON.stringify({postJson}));
    var res = window.ReturnCallJsResult(JSON.stringify(postJson));
    console.log('ReturnCallJsResult----response', res);
  } else {
    postJson.winName = winName;
    console.log('ReturnCallJsResult 方法不存在,通过postMessage回传结果：', JSON.stringify(postJson));
    window.parent.postMessage({
      message: 'srOuterOptListener',
      data: postJson
    }, '*');
  }
}

/**
 * @method srCommonOuterOpt
 * @param {Object} params {type,status,message,result,param}json
 * @desc 外部调用公共方法
 * type的含义
  1:保存回调
  10:保存成功/失败状态通知回调
  2:提交回调
  20:提交成功/失败状态通知回调
  3:确认回调
  30:确认成功/失败状态通知回调
  4:编辑回调
  5:打印回调
  6:静默打印回调
  7:AI助手信息回调
  8:AI助手切换检查
  9:语音结构化控制消息
  10:阴阳性回调
  11:手写签名 签署端-提交
  12:手写签名 签署端-重签
  13:手写签名 发起端-发起
  14:手写签名 发起端-重签
  15:计价收费
  16:页面关闭回调
  17:页面关闭成功回调
 */
window.srCommonOuterOpt = function(params, callBack) {
  if(location.href.indexOf('trace') > -1 || location.href.indexOf('preview=1') > -1) {
    return;
  }
  if(!params.onlySetStorage && (location.href.indexOf('child') > -1 || document.querySelector('.rt-dialog-wrap'))) {
    window.top.postMessage({
      message: 'valiResponse',
      data: {isVail: false}
    }, '*');
    alert('请先关闭弹框');
    return;
  }
  console.log('执行srCommonOuterOpt,公共方法入参--->', params);
  try {
    if(params && typeof params === 'string') {
      params = JSON.parse(params);
    }
    window.srCommonOuterOptHandler && window.srCommonOuterOptHandler(params, callBack);  //报告内的方法
  } catch(e) {
    console.error('数据格式错误：'+e);
  }
}

/**
 * 后台触发更新检查description和impression,目前病理pacs检验报告数据在用
 */
window.updateImpAndDesHandler = function(res, reportNo) {
  if(!window.createDescAndImpTextHandler || typeof window.createDescAndImpTextHandler !== 'function') {
    console.log('createDescAndImpTextHandler不存在')
    return;
  }
  window.createDescAndImpTextHandler();
  if(!window.rtStructure) {
    console.log('找不到相应数据description和impression')
    return;
  }
  // 先保存一遍节点
  var params = {
    description: rtStructure.description || '',
    impression: rtStructure.impression || '',
    examNo: res ? res.examNo : window.patientInfo.examNo,
    reportNo: reportNo,
    reportId: res ? res.reportId : ''
  }
  $.ajax({
    type: 'post',
    url: api.updateImpAndDes,
    data: JSON.stringify(params),
    dataType: "json",
    async: true,
    contentType: 'application/json',
    headers: {
      // token: winTopParentStoreUserState && winTopParentStoreUserState.token ? winTopParentStoreUserState.token : (JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || "")
      token: JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || ''
    },
    success: function () {
      // 无需处理
    },
    error: function () {
      // 无需处理
    },
  });
}

/**
 * 后台触发上传PDF,目前病理pacs中山一NGS-PDF在用
 */
window.srUploadPatternPdfHandler = function(blob, examInfo) {
  var formdata = new FormData();
  formdata.append("file", blob);
  formdata.append("imageType", "sr_rpt_pdf"); // 文件类型：sr_rpt_pdf 结构化的报告pdf，rpt 报告图像，apply 申请单图像
  formdata.append("examNo", examInfo.examNo);
  formdata.append("reportNo", examInfo.docNo);
  // store.dispatch("report/uploadPDF", {param: formdata, vm: Vue.prototype})
  $.ajax({
    type: 'post',
    url: api.filePatch2,
    data: formdata,
    async: false,
    contentType: false,
    processData: false,
    headers: {
      // token: winTopParentStoreUserState && winTopParentStoreUserState.token ? winTopParentStoreUserState.token : (JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || "")
      token: JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || ''
    },
    success: function () {
      // 无需处理
    },
    error: function () {
      // 无需处理
    },
  });
}

/**
 * 后台触发调用接口
 */
window.srGlobalCallbackHandler = function(res, reportNo, apiUrl) {
  // 先保存一遍节点
  var params = {
    examNo: res ? res.examNo : window.patientInfo.examNo,
    reportNo: reportNo,
    reportId: res ? res.reportId : ''
  }
  $.ajax({
    type: 'post',
    url: apiUrl,
    data: JSON.stringify(params),
    dataType: "json",
    async: true,
    contentType: 'application/json',
    headers: {
      // token: winTopParentStoreUserState && winTopParentStoreUserState.token ? winTopParentStoreUserState.token : (JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || "")
      token: JSON.parse(window.localStorage.getItem('rt_session_token') || '""') || ''
    },
    success: function () {
      // 无需处理
    },
    error: function () {
      // 无需处理
    },
  });
}
