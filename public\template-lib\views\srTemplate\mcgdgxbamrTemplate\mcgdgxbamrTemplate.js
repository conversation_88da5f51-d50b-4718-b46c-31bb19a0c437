$(function () {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('.t-pg', 'dev'); // 测试
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var isFillInStatus1 = {}, isFillInStatus2 = {}, isFillInStatus3 = {};
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过
var raidoArr1 = [], radioArr2 = [], radioArr3 = [], textareaIdArr = [];
var radioNameArr = ['mjmzgxs', 'mjmzgas', 'zzhbxs', 'zzhbas', 'zzszxs', 'zzszas', 'znzxs', 'znzas', 'zwzxs', 'zwzas', 'yzxs', 'yzas', 'yqzxs', 'yqzas',
  'yhzxs', 'yhzas', 'pjmxs', 'pjmas', 'cxmxs', 'cxmas', 'gzjmxs', 'gzjmas', 'gjmzxs', 'gjmzas', 'gyjmxs', 'gyjmas', 'zszxs', 'zszas', 'szzxs',
  'szzas', 'yszxs', 'yszas', 'yxgjmxs', 'yxgjmas', 'gsdxs', 'gsdas', 'gpmxs', 'gpmas', 'ghdxs', 'ghdas', 'gxdxs', 'gxdas',]
var gzfd = {
  s1: '尾状叶',
  s2: '左外叶上段',
  s3: '左外叶下段',
  s4: '左内叶',
  s5: '右前叶下段',
  s6: '右后叶下段',
  s7: '右后叶上段',
  s8: '右前叶上段',
}
var mjmxtArr = [
  ['mjmzgbzfj', 'mjmzgsq', 'mjmzgxs', 'mjmzgyxs', 'mjmzgas', 'mjmzgyas'], //门静脉主干
  ['zzhbbzfj', 'zzhbsq', 'zzhbxs', 'zzhbyxs', 'zzhbas', 'zzhbyas'],//左支横部
  ['zzszbzfj', 'zzszsq', 'zzszxs', 'zzszyxs', 'zzszas', 'zzszyas'],//左支矢状部
  ['znzbzfj', 'znzsq', 'znzxs', 'znzyxs', 'znzas', 'znzyas'],//左内支
  ['zwzbzfj', 'zwzsq', 'zwzxs', 'zwzyxs', 'zwzas', 'zwzyas'],//左外支
  ['yzbzfj', 'yzsq', 'yzxs', 'yzyxs', 'yzas', 'yzyas'],//右支
  ['yqzbzfj', 'yqzsq', 'yqzxs', 'yqzyxs', 'yqzas', 'yqzyas'],//右前支
  ['yhzbzfj', 'yhzsq', 'yhzxs', 'yhzyxs', 'yhzas', 'yhzyas'],//右后支
  ['pjmbzfj', 'pjmsq', 'pjmxs', 'pjmyxs', 'pjmas', 'pjmyas'],//脾静脉
  ['cxmbzfj', 'cxmsq', 'cxmxs', 'cxmyxs', 'cxmas', 'cxmyas'],//肠系膜上静脉
]
var gjmArr = [
  ['gzjmbzfj', 'gzjmsq', 'gzjmxs', 'gzjmyxs', 'gzjmas', 'gzjmyas'],//肝左静脉
  ['gjmzbzfj', 'gjmzsq', 'gjmzxs', 'gjmzyxs', 'gjmzas', 'gjmzyas'],//肝中静脉
  ['gyjmbzfj', 'gyjmsq', 'gyjmxs', 'gyjmyxs', 'gyjmas', 'gyjmyas'],//肝右静脉
  ['zszbzfj', 'zszsq', 'zszxs', 'zszyxs', 'zszas', 'zszyas',],//肝左静脉属支
  ['szzbzfj', 'szzsq', 'szzxs', 'szzyxs', 'szzas', 'szzyas',],//肝中静脉属支
  ['yszbzfj', 'yszsq', 'yszxs', 'yszyxs', 'yszas', 'yszyas',],//肝右静脉属支
  ['yxgjmbzfj', 'yxgjmsq', 'yxgjmxs', 'yxgjmyxs', 'yxgjmas', 'yxgjmyas'],//肝右下副肝静脉
]
var xqjmArr = [
  ['gsdbzfj', 'gsdsq', 'gsdxs', 'gsdyxs', 'gsdas', 'gsdyas'],//肝上段
  ['gpmbzfj', 'gpmsq', 'gpmxs', 'gpmyxs', 'gpmas', 'gpmyas'], //第二肝门平面
  ['ghdbzfj', 'ghdsq', 'ghdxs', 'ghdyxs', 'ghdas', 'ghdyas'],//肝后段
  ['gxdbzfj', 'gxdsq', 'gxdxs', 'gxdyxs', 'gxdas', 'gxdyas'],//肝下段
]
var veinNameData = {
  mjmName: ['门静脉主干', '左支横部', '左支矢状部', '左内支', '左外支', '右支', '右前支', '右后支', '脾静脉', '肠系膜上静脉'],
  gjmName: ['肝左静脉', '肝中静脉', '肝右静脉', '肝左静脉属支', '肝中静脉属支', '肝右静脉属支', '右下副肝静脉'],
  xqjmName: ['肝上段', '第二肝门平面', '肝后段', '肝下段'],
}
// 子支与父支对应
var veinNameChildPar = {
  '左支横部': '门脉左支',
  '左支矢状部': '门脉左支',
  '左内支': '门脉左支',
  '左外支': '门脉左支',

  '右支': '门脉右支',
  '右前支': '门脉右支',
  '右后支': '门脉右支',

  '肝左静脉属支': '属支',
  '肝中静脉属支': '属支',
  '肝右静脉属支': '属支',
};
var xhtsData = [
  {title: '低信号', id: '低信号'},
  {title: '稍低信号', id: '稍低信号'},
  {title: '中等信号', id: '中等信号'},
  {title: '轻度高信号', id: '轻度高信号'},
  {title: '中度高信号', id: '中度高信号'},
  {title: '明显高信号', id: '明显高信号'},
]
var adcData = [
  {title: '低信号', id: '低信号'},
  {title: '高信号', id: '高信号'},
]
var descriptionStr = '';
var mjmxtStrList = [], gjmStrList = [], xqjmStrList = [];
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#mcgxbmr1"),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    initPage();
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView();
    } else {
    }
  }
  dev && initPage();
}

function initView() {
  let bracketArr1 = [], showArr1 = [];
  // bracketArr1.push(initId('132'));
  bracketArr1.push(initId('507'));
  bracketArr1.push(initId('511'));
  bracketArr1.push(initId('513'));
  bracketArr1.push(initId('515'));
  bracketArr1.push(initId('525'));
  bracketArr1.push(initId('527'));
  bracketArr1.push(initId('529'));
  bracketArr1.push(initId('531'));
  bracketArr1.push(initId('533'));
  bracketArr1.push(initId('535'));
  bracketArr1.push(initId('545'));
  bracketArr1.push(initId('547'));
  bracketArr1.push(initId('555'));
  bracketArr1.push(initId('557'));
  bracketArr1.push(initId('559'));
  bracketArr1.push(initId('561'));
  bracketArr1.push(initId('563'));
  // bracketArr1.push(initId('590'));
  bracketArr1.push(initId('593'));
  bracketArr1.push(initId('593_1'));
  for (let i = 0; i < bracketArr1.length; i++) {
    // let val = $('#' + bracketArr1[i]).val();
    let val = rtStructure.getNodeVal(bracketArr1[i]);
    if (val) {
      let rtsc = $('#' + bracketArr1[i]).attr('rt-sc');
      let rtscArr = rtsc.split(';');
      let nameVal = ''
      rtscArr.forEach(item => {
        let [key, value] = item.split(':');
        if (key === 'name') {
          nameVal = value;
        }
      })
      $('#' + bracketArr1[i]).parent().html(`${nameVal}（${val}）`)
    }
  }
  
  // 静脉曲张其他选项处理
  rtStructure.setNodeVal('mcgxba-rt-135_3', rtStructure.getNodeVal('mcgxba-rt-136_1'));
  rtStructure.setNodeVal('mcgxba-rt-136_1', '');

  curElem.find('.bracket').each(function (i, dom) {
    // let val = $(dom).find('.rt-sr-w').val();
    let val = rtStructure.getNodeVal($(dom).find('.rt-sr-w').attr('id'));
    val ? $(dom).html(`（${val}）`) : ''
    if(!val) {
      // $(dom).parent().remove();
    }
  })
  // 淋巴结转移
  var isShow;
  showArr1.push(initId('485'));
  showArr1.push(initId('489'));
  showArr1.push(initId('493'));
  showArr1.push(initId('493_1'));
  showArr1.push(initId('497'));
  showArr1.forEach(obj => {
    let val = getVal($(`[id="${obj}"]:checked`)) || '';
    if (val) {
      isShow = true;
      $('#' + obj).parent().parent().show(); // 显示本条
    } else {
       $('#' + obj).parent().parent().hide(); // 隐藏本条
    }
  })
  if (isShow) {
    $('#' + showArr1[0]).parent().parent().parent().siblings('.right-cont').hide(); // 隐藏父复选框
    $('#' + showArr1[0]).parent().parent().parent().show().height('auto').prepend('<span>转移</span><br>'); // 显示父块
  } else {
    $('#' + showArr1[0]).parent().parent().parent().hide(); // 隐藏父块
  }
  // 淋巴结炎性增生
  let lbjzsArr = [];
  isShow = false;
  lbjzsArr.push(initId('485_1'));
  lbjzsArr.push(initId('489_1'));
  lbjzsArr.push(initId('493_2'));
  lbjzsArr.push(initId('493_2_1'));
  lbjzsArr.push(initId('497_1'));
  lbjzsArr.forEach(obj => {
    let val = getVal($(`[id="${obj}"]:checked`)) || '';
    if (val) {
      isShow = true;
      $('#' + obj).parent().parent().show(); // 显示本条
    } else {
      $('#' + obj).parent().parent().hide(); // 隐藏本条
    }
  })
  if (isShow) {
    $('#' + lbjzsArr[0]).parent().parent().parent().siblings('.right-cont').hide(); // 隐藏父复选框
    $('#' + lbjzsArr[0]).parent().parent().parent().show().height('auto').prepend('<span>炎性增生</span><br>'); // 显示父块
  } else {
    $('#' + lbjzsArr[0]).parent().parent().parent().hide(); // 隐藏父块
  }

  $('#mcgxba-rt-586').val() ? '' : $('#mcgxba-rt-586').parent().html('无');
  // console.log('>>>mjmxtStrList', mjmxtStrList);
  let mjmViewStrList = forJmArrFun3(mjmxtStrList, 'mjmName');
  let mmzz = '', mmyz = '', sz = '';
  let jmhbArr = [], line = []
  if (mjmViewStrList[1] || mjmViewStrList[2] || mjmViewStrList[3] || mjmViewStrList[4]) {
    jmhbArr = mjmViewStrList.slice(1, 5);
    jmhbArr = jmhbArr.filter(item => item);
    mmzz = `<span style="font-weight: 600">门脉左支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  if (mjmViewStrList[5] || mjmViewStrList[6] || mjmViewStrList[7]) {
    jmhbArr = mjmViewStrList.slice(5, 8);
    jmhbArr = jmhbArr.filter(item => item);
    mmyz = `<span style="font-weight: 600">门脉右支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  mjmViewStrList[0] ? line.push(mjmViewStrList[0]) : '';
  mmzz ? line.push(mmzz) : '';
  mmyz ? line.push(mmyz) : '';
  mjmViewStrList[8] ? line.push(mjmViewStrList[8]) : '';
  mjmViewStrList[9] ? line.push(mjmViewStrList[9]) : '';
  if (line.length > 0) {
    $('.mjm-view').html(line.map(item => `<div>${item}</div>`))
  }
  line = [];
  //=================================================================================
  // console.log('>>>gjmStrList', gjmStrList);
  let gjmViewStrList = forJmArrFun3(gjmStrList, 'gjmName');
  gjmViewStrList[0] ? line.push(gjmViewStrList[0]) : '';
  gjmViewStrList[1] ? line.push(gjmViewStrList[1]) : '';
  gjmViewStrList[2] ? line.push(gjmViewStrList[2]) : '';
  if (gjmViewStrList[3] || gjmViewStrList[4] || gjmViewStrList[5]) {
    jmhbArr = gjmViewStrList.slice(3, 6);
    jmhbArr = jmhbArr.filter(item => item);
    sz = `<span style="font-weight: 600">属支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  sz ? line.push(sz) : '';
  gjmViewStrList[6] ? line.push(gjmViewStrList[6]) : '';
  if (line.length > 0) {
    $('.gjm-view').html(line.map(item => `<div>${item}</div>`))
  }
  line = [];
  //=================================================================================
  let xqjmViewStrList = forJmArrFun3(xqjmStrList, 'xqjmName');
  // console.log('>>>xqjmStrList', xqjmStrList);
  if(xqjmViewStrList.length > 0){
    $('.xqjm-view').html(xqjmViewStrList.map(item => `<div>${item}</div>`))
  }
  var zzVal = getVal('[id="mcgxba-rt-17"]:checked') || '';
  if(!zzVal) {
    $('.zdShow').hide()
    $('.zz-block').css('cssText', 'display: none !important');
  }
  // var msval = $('#mcgxba-rt-505').val() || '';
  var msval = rtStructure.getNodeVal('mcgxba-rt-505');
  if(msval){
    $('.view-none1').show()
  }
  var fmqdzh = getVal($('input[type=checkbox][name=fmqdzh]:checked')) || ''; //增厚位置
  if(!fmqdzh) {
    $('.qdzh-show').hide()
  }
  var ygqk = $('#mcgxba-rt-564').val() || '';
  if(!ygqk){
    $('.ygqk-show').hide()
  }
  var yfzData = ['mcgxba-rt-20','mcgxba-rt-21','mcgxba-rt-22','mcgxba-rt-23','mcgxba-rt-24','mcgxba-rt-25','mcgxba-rt-26','mcgxba-rt-27']
  var yfzHtml = []
  yfzData.forEach(item => {
    if(idAndDomMap[item].value){
      yfzHtml.push(idAndDomMap[item].value)
    }
  })
  var yfzText = yfzHtml.join(',')
  idAndDomMap['mcgxba-rt-18'].value === '单发' ? yfzText = yfzText.replace(/,/g, '-') : ''
  $('.yfz-view').html(yfzText+'段')
  var zzData = ['mcgxba-rt-75','mcgxba-rt-76','mcgxba-rt-77','mcgxba-rt-78','mcgxba-rt-79','mcgxba-rt-80','mcgxba-rt-81','mcgxba-rt-82']
  var zzHtml = []
  zzData.forEach(item => {
    if(idAndDomMap[item].value){
      zzHtml.push(idAndDomMap[item].value)
    }
  })
  var zzText = zzHtml.join(',')
  idAndDomMap['mcgxba-rt-73'].value === '单发' ? zzText = zzText.replace(/,/g, '-') : ''
  $('.zz-view').html(zzText+'段')
}

function forJmArrFun3(list, type) {
  let propName = ['与病灶分界', '受侵', '血栓', '', '癌栓', ''];
  let veinNames = [];
  veinNames = veinNameData[type];
  var jmarrData = {
    mjmName: [[], [], [], [], [], [], [], [], [], []],
    gjmName: [[], [], [], [], [], [], []],
    xqjmName: [[], [], [], [],],
  }
  let tempResults = jmarrData[type];
  for (let i = 0; i < list.length; i++) {
    for (let j = 0; j < propName.length; j++) {
      let val = list[i][j] || '';
      let prop = propName[j];
      if (val && prop !== '') {
        tempResults[i].push(prop + '：' + val);
      }
      if (j === 3 && val) {
        tempResults[i][j - 1] = tempResults[i][j - 1] + '（' + val + '）';
      }
      if (j === 5 && val ) {
        tempResults[i][j - 1] = tempResults[i][4 - 1] + '（' + val + '）';
      }
    }
  }
  let strList = [];
  for (let t = 0; t < tempResults.length; t++) {
    if (tempResults[t].length > 0) {
      if (type === 'mjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        t === 0 || t === 8 || t === 9 ?
          strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`) :
          strList.push(`${veinNameData[type][t]}（${tempResults[t].join('；')}）`)
      }
      if (type === 'gjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        t !== 3 && t !== 4 && t !== 5 ?
        strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`) :
        strList.push(`${veinNameData[type][t]}（${tempResults[t].join('；')}）`)
      }
      if (type === 'xqjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`)
      }

    } else {
      strList.push('')
    }
  }
  // console.log('>>>strList', strList);
  return strList;
}

// 初始化数据
function initPage() {
  var inp1Arr = [], inp2Arr = [], inp3Arr = [];
  inp1Arr.push(initId('149'))
  inp1Arr.push(initId('150'))
  inp1Arr.push(initId('151'))
  inp1Arr.push(initId('152'))
  inp1Arr.push(initId('153'))
  inp1Arr.push(initId('301'))
  inp1Arr.push(initId('302'))
  inp1Arr.push(initId('303'))
  inp1Arr.push(initId('304'))
  inp1Arr.push(initId('305'))
  inp1Arr.push(initId('407'))
  inp1Arr.push(initId('408'))
  inp1Arr.push(initId('409'))
  inp1Arr.push(initId('410'))

  inp2Arr.push(initId('16'))
  inp2Arr.push(initId('17'))

  inp3Arr.push(initId('168'))
  inp3Arr.push(initId('169'))
  inp3Arr.push(initId('170'))
  inp3Arr.push(initId('171'))
  inp3Arr.push(initId('228'))
  inp3Arr.push(initId('229'))
  inp3Arr.push(initId('230'))
  inp3Arr.push(initId('348'))
  inp3Arr.push(initId('349'))
  inp3Arr.push(initId('350'))
  inp3Arr.push(initId('483_1'))
  inp3Arr.push(initId('484_1'))

  raidoArr1.push(initId('129'))
  raidoArr1.push(initId('130'))
  raidoArr1.push(initId('131'))

  radioArr2.push(initId('45'))
  radioArr2.push(initId('100'))
  radioArr2.push(initId('468'))
  radioArr2.push(initId('484'))
  radioArr2.push(initId('517'))
  radioArr2.push(initId('537'))
  radioArr2.push(initId('549'))

  radioArr3.push(initId('620'))
  radioArr3.push(initId('621'))
  radioArr3.push(initId('622'))

  textareaIdArr.push(initId('510'))
  textareaIdArr.push(initId('512'))
  textareaIdArr.push(initId('514'))
  textareaIdArr.push(initId('524'))
  textareaIdArr.push(initId('526'))
  textareaIdArr.push(initId('528'))
  textareaIdArr.push(initId('530'))
  textareaIdArr.push(initId('532'))
  textareaIdArr.push(initId('534'))
  textareaIdArr.push(initId('544'))
  textareaIdArr.push(initId('546'))
  textareaIdArr.push(initId('554'))
  textareaIdArr.push(initId('556'))
  textareaIdArr.push(initId('558'))
  textareaIdArr.push(initId('560'))
  textareaIdArr.push(initId('562'))
  textareaIdArr.push(initId('589'))
  textareaIdArr.push(initId('592'))

  // if (!isSavedReport) {
  //   inp1Arr.forEach(item => {
  //     isFillInStatus1[item] = '1'
  //   })
  // }
  inp1Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus1[item] = '1' : '';
  })

  inp2Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus2[item] = '1' : '';
  })

  inp3Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus3[item] = '1' : '';
  })

  radioArr2.forEach(item => {
    $('#' + item).is(':checked') ? $('#' + item + '-cont').show() : $('#' + item + '-cont').hide();
  })

  // $('.g-item').find('.block-ck1')

  changeBzSmText();
  changeYfzgs();
  changeZzgs();
  showGyh();
  showZfg();
  showDmqxlgz();
  showRadioInfo('init');
  showTextarea('init');
  initClickFun();
  getTipText();
  toggleRadioCheckStatus_sample();
  curElem.find('#mcgxbmr1 .body-wrap .rt-sr-w').change(function () {
    getTipText();
  })
  let xhts = ['mcgxba-rt-594','mcgxba-rt-595','mcgxba-rt-596','mcgxba-rt-601','mcgxba-rt-602','mcgxba-rt-603']
  xhts.forEach(e => {
    initInpAndSel(e, xhtsData);
  })
  initInpAndSel('mcgxba-rt-610', adcData);
  initInpAndSel('mcgxba-rt-615', adcData);
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal) {
  let selLen = 200;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  dropdown.render({
    id: idList,
    elem: `#${idList}`,
    data: optionList,
    className: 'laySelLab',
    click: function (obj) {
      this.elem.val(obj.title);
      this.elem.trigger('change');
    },
    style: `width: ${selLen}px;`
  });
}
function getTipText() {
  let str = '', str2 = '', str3 = '';
  let impressionArr = [], impressionArr1 = [], impressionArr2 = [];
  // 原发灶
  var yfzVal = getVal('[id="mcgxba-rt-16"]:checked') || '';
  var yfbzsm = getVal($('input[type=radio][name=bzsm]:checked')) || '';//数目
  var yfbzgs = getVal($('input[type=radio][name=bzgs]:checked')) || '';// 个数
  var yfzwz = getVal($('input[type=checkbox][name=yfzwz]:checked')) || '';//位置
  var yfzzdwy = $('#mcgxba-rt-639').val();//最大位于
  var yfzdx1 = $('#mcgxba-rt-28').val();//大小
  var yfzdx2 = $('#mcgxba-rt-29').val();//大小
  var yfzxz = getVal($('input[type=radio][name=yfzxz]:checked')) || '';//形状
  var yfzfx = getVal($('input[type=radio][name=yfzfx]:checked')) || '';//分型
  var yfzbj = getVal($('input[type=radio][name=yfzbj]:checked')) || '';//边界
  var yfT1WI = $('#mcgxba-rt-594').val();//T1WI
  var yfT2WI = $('#mcgxba-rt-595').val();//T2WI
  var yfDWI = $('#mcgxba-rt-596').val();//DWI
  var yfADC = $('#mcgxba-rt-610').val();//ADC
  var yfzT1 = getVal($('input[type=radio][name=yfzT1]:checked')) || '';//T1同反相位序列存在脂肪变性
  // var yfzR2zsg = getVal($('input[type=radio][name=yfzR2zsg]:checked')) || '';//R2*值升高
  var yfzpsmd = getVal($('input[type=radio][name=yfzpsmd]:checked')) || '';//平扫密度
  var yfzpsmd2 = getVal($('input[type=radio][name=yfzpsmd2]:checked')) || '';//平扫密度是否均匀
  var yfzpsmdbj = getVal($('input[type=radio][name=yfzpsmdbj]:checked')) || '';//平扫密度不均匀
  var yfzbjyqt = $('#mcgxba-rt-49').val();//平扫密度不均匀其他填写
  var yfzzqdmwq = getVal($('input[type=radio][name=yfzzqdmwq]:checked')) || '';//动脉晚期
  var yfzzqmjmq = getVal($('input[type=radio][name=yfzzqmjmq]:checked')) || '';//门静脉期
  var yfzzqycq = getVal($('input[type=radio][name=yfzzqycq]:checked')) || '';//延迟期
  var yfzzqyxq = getVal($('input[type=radio][name=yfzzqyxq]:checked')) || '';// 移行期
  var yfzzqxs = getVal($('input[type=radio][name=yfzzqxs]:checked')) || '';//增强强化形式
  var yfzzqxsqt = $('#mcgxba-rt-69').val();//增强形式其他填写
  var yfzbmz = getVal($('input[type=radio][name=yfzbmz]:checked')) || '';//包膜征
  var yfzbmzInpVal = $('[name="yfzqhbg"]:checked').val() || '';//强化包膜有填写
  var yfzgdqbxVal = getVal($('input[type=radio][name=yfzgdqbx]:checked')) || '';//肝胆期表现
  var yfgdqbxVal = $('#mcgxba-rt-608').val();//肝胆期表现填写
  var yfzrads = getVal($('input[name=yfzrads]:checked')) || '';//LI-RADS分级


  // 子灶
  var zzVal = getVal('[id="mcgxba-rt-17"]:checked') || '';
  var zzsm = getVal($('input[type=radio][name=zzsm]:checked')) || '';//数目
  var zzgs = getVal($('input[type=radio][name=zzgs]:checked')) || '';//个数
  var zzwz = getVal($('input[type=checkbox][name=zzwz]:checked')) || '';//位置
  var zzzdwy = $('#mcgxba-rt-643').val();//最大位于
  var zzdx1 = $('#mcgxba-rt-83').val();//大小
  var zzdx2 = $('#mcgxba-rt-84').val();//大小
  var zzxz = getVal($('input[type=radio][name=zzxz]:checked')) || '';//形状
  var zzfx = getVal($('input[type=radio][name=zzfx]:checked')) || '';//分型
  var zzbj = getVal($('input[type=radio][name=zzbj]:checked')) || '';//边界
  var zzT1WI = $('#mcgxba-rt-601').val();//T1WI
  var zzT2WI = $('#mcgxba-rt-602').val();//T2WI
  var zzDWI = $('#mcgxba-rt-603').val();//DWI
  var zzADC = $('#mcgxba-rt-615').val();//ADC
  var zzT1 = getVal($('input[type=radio][name=zzT1]:checked')) || '';//T1同反相位序列存在脂肪变性
  // var zzR2zsg = getVal($('input[type=radio][name=zzR2zsg]:checked')) || '';//R2*值升高
  var zzpsmd = getVal($('input[type=radio][name=zzpsmd]:checked')) || '';//平扫密度
  var zzpsmd2 = getVal($('input[type=radio][name=zzpsmd2]:checked')) || '';//平扫密度是否均匀
  var zzpsmdbj = getVal($('input[type=radio][name=zzpsmdbj]:checked')) || '';//平扫密度不均匀
  var zzsmdbj = getVal($('input[type=radio][name=zzsmdbj]:checked')) || '';//平扫密度不均匀
  var zzbjyqt = $('#mcgxba-rt-104').val();//平扫密度不均匀其他填写
  var zzzqdmwq = getVal($('input[type=radio][name=zzzqdmwq]:checked')) || '';//动脉晚期
  var zzzqmjmq = getVal($('input[type=radio][name=zzzqmjmq]:checked')) || '';//门静脉期
  var zzzqycq = getVal($('input[type=radio][name=zzzqycq]:checked')) || '';//延迟期
  var zzzqyxq = getVal($('input[type=radio][name=zzzqyxq]:checked')) || '';// 移行期
  var zzzqxs = getVal($('input[type=radio][name=zzzqxs]:checked')) || '';//增强强化形式
  var zzzqxsqt = $('#mcgxba-rt-124').val();//增强形式其他填写
  var zzbmz = getVal($('input[type=radio][name=zzbmz]:checked')) || '';//包膜征
  var zzbmzInpVal = $('[name="zzqhbg"]:checked').val() || '';//强化包膜有填写
  var zzgdqbx = getVal($('input[type=radio][name=zzgdqbx]:checked')) || '';//肝胆期表现
  var zzgdqbxVal = $('#mcgxba-rt-609').val();//肝胆期表现填写
  var zzrads = getVal($('input[name=zzrads]:checked')) || '';//LI-RADS分级

  //肝背景
  var gyhVal = getVal($('input[type=radio][name=gyh]:checked')) || '';
  var gyhInpVal = $('#mcgxba-rt-132:not(.rt-hide)').val() || ''; //肝硬化
  if (gyhVal === '有') {
    gyhVal = '肝硬化';
  }
  var zfgVal = getVal($('input[type=radio][name=zfg]:checked')) || ''; //脂肪肝
  var zfgqt = $('#mcgxba-rt-623:not(.rt-hide)').val();
  var gztgzVal = getVal($('input[type=radio][name=gztgz]:checked')) || ''; //肝脏铁过载
  var tgzqt = $('#mcgxba-rt-593:not(.rt-hide)').val();
  var dmqxlgzVal = getVal($('input[type=radio][name=dmqxlgz]:checked')) || ''; // 动脉期血流灌注异常
  var dmqxlgzqt = $('#mcgxba-rt-593_1:not(.rt-hide)').val();
  if (dmqxlgzVal === '有') {
    dmqxlgzVal = '动脉期血流灌注异常';
  }

  //门脉高压征象
  var jmqzVal = getVal($('input[type=checkbox][name=jmqz]:checked')) || '';
  var jmqzInpVal = $('#mcgxba-rt-136').val() || ''; //门脉高压静脉曲张
  var jmqcqtVal = $('#mcgxba-rt-136_1').val() || ''; // 门脉高压静脉曲张其他填写
  if (jmqcqtVal) {
    jmqzVal = jmqzVal.replace(/其他/, jmqcqtVal); // 替换其他为填写值
  }
  var fqjmkfVal = getVal($('input[type=radio][name=fqjmkf]:checked')) || '';
  var fqjmkfInpVal = $('#mcgxba-rt-139').val() || ''; //脐静脉开放
  var wsflVal = getVal($('input[type=radio][name=wsfl]:checked')) || '';
  var wsflInpVal = $('#mcgxba-rt-142').val() || ''; //胃-肾分流
  var psflVal = getVal($('input[type=radio][name=psfl]:checked')) || '';
  var psflInpVal = $('#mcgxba-rt-145').val() || ''; //脾-肾分流
  var mmgyqtInpVal = $('#mcgxba-rt-147').val() || ''; //门脉高压征象其他

  // 门静脉系统
  // var mjmxtCheckbox = getVal($('input[type=checkbox][name=mjmxt]:checked')) || '';
  // if (mjmxtCheckbox) {
  //   // for(let a = 0; a < mjmxtArr.length; a++){
  //   //   let arr = [];
  //   //   arr =  mjmxtArr[a].map(item => getVal($(`input[type=radio][name=${item}]:checked`)));
  //   //   mjmxtStrList.push(arr)
  //   // }
    mjmxtStrList = forJmArrFun(mjmxtArr);
  // }
  // 肝静脉
  // var gjmCheckbox = getVal($('input[type=checkbox][name=gjm]:checked')) || '';
  // if (gjmCheckbox) {
    gjmStrList = forJmArrFun(gjmArr)
  // }
  // 下腔静脉
  // var xqjmCheckbox = getVal($('input[type=checkbox][name=xqjm]:checked')) || '';
  // if (xqjmCheckbox) {
    xqjmStrList = forJmArrFun(xqjmArr)
  // }

  //胆管
  var dgVal = getVal($('input[type=radio][name=dg]:checked')) || '';
  var dgsqVal = getVal($('input[type=radio][name=dgsq]:checked')) || ''; //胆管受侵val
  var dgsqkj = getVal($('input[type=checkbox][name=dgsqkj]:checked')) || '';
  var dgkzVal = getVal($('input[type=radio][name=dgkz]:checked')) || '';
  var dgkzInpVal = $('#mcgxba-rt-479').val() || '';//胆管扩张最宽val
  var dskzVal = getVal($('input[type=radio][name=dskz]:checked')) || '';
  var dskzInpVal = $('#mcgxba-rt-482').val() || '';//胆栓形成val

  //淋巴结
  var lbjVal = getVal($('input[type=radio][name=lbj]:checked')) || '';
  // 转移
  var lbj_yc = getVal('[id="mcgxba-rt-483_1"]:checked') || '';
  var xgjVal = getVal('[id="mcgxba-rt-485"]:checked') || '';//心膈角val
  var xgjsl = $("#mcgxba-rt-486").val();
  var xgjdj = $("#mcgxba-rt-487").val();
  var xgjzq = $("#mcgxba-rt-488").val();
  var gmbVal = getVal('[id="mcgxba-rt-489"]:checked') || '';//肝门部
  var gmbsl = $("#mcgxba-rt-490").val();
  var gmbdj = $("#mcgxba-rt-491").val();
  var gmbzq = $("#mcgxba-rt-492").val();
  var fmhVal = getVal('[id="mcgxba-rt-493"]:checked') || '';//腹膜后
  var fmhsl = $("#mcgxba-rt-494").val();
  var fmhdj = $("#mcgxba-rt-495").val();
  var fmhzq = $("#mcgxba-rt-496").val();
  var xdVal = getVal('[id="mcgxba-rt-493_1"]:checked') || '';//心底
  var xdsl = $("#mcgxba-rt-494_1").val();
  var xddj = $("#mcgxba-rt-495_1").val();
  var xdzq = $("#mcgxba-rt-496_1").val();
  var lbjqtVal = getVal('[id="mcgxba-rt-497"]:checked') || '';//其他
  var lbjqtInpVal = $('#mcgxba-rt-498').val() || '';//淋巴结其他val
  var lbjqtsl = $('#mcgxba-rt-499').val();
  var lbjqtdj = $('#mcgxba-rt-500').val();
  // 炎性增生
  var lbj_yc_zs = getVal('[id="mcgxba-rt-484_1"]:checked') || '';
  var xgjVal_zs = getVal('[id="mcgxba-rt-485_1"]:checked') || '';//心膈角val
  var xgjsl_zs = $("#mcgxba-rt-486_1").val();
  var xgjdj_zs = $("#mcgxba-rt-487_1").val();
  var xgjzq_zs = $("#mcgxba-rt-488_1").val();
  var gmbVal_zs = getVal('[id="mcgxba-rt-489_1"]:checked') || '';//肝门部
  var gmbsl_zs = $("#mcgxba-rt-490_1").val();
  var gmbdj_zs = $("#mcgxba-rt-491_1").val();
  var gmbzq_zs = $("#mcgxba-rt-492_1").val();
  var fmhVal_zs = getVal('[id="mcgxba-rt-493_2"]:checked') || '';//腹膜后
  var fmhsl_zs = $("#mcgxba-rt-494_2").val();
  var fmhdj_zs = $("#mcgxba-rt-495_2").val();
  var fmhzq_zs = $("#mcgxba-rt-496_2").val();
  var xdVal_zs = getVal('[id="mcgxba-rt-493_2_1"]:checked') || '';//心底
  var xdsl_zs = $("#mcgxba-rt-494_2_1").val();
  var xddj_zs = $("#mcgxba-rt-495_2_1").val();
  var xdzq_zs = $("#mcgxba-rt-496_2_1").val();
  var lbjqtVal_zs = getVal('[id="mcgxba-rt-497_1"]:checked') || '';//其他
  var lbjqtInpVal_zs = $('#mcgxba-rt-498_1').val() || '';//淋巴结其他val
  var lbjqtsl_zs = $('#mcgxba-rt-499_1').val();
  var lbjqtdj_zs = $('#mcgxba-rt-500_1').val();

  //远处转移
  var yczyfb = getVal('[id="mcgxba-rt-501"]:checked') || ''; //远处转移肺部
  var fbwz = getVal($('input[type=radio][name=fb]:checked')) || ''; //远处转移肺部位置
  var fbwzInpVal = $('#mcgxba-rt-505').val() || '';
  var yczygt = getVal('[id="mcgxba-rt-506"]:checked') || ''; //远处转移骨
  var yczygtInpVal = $('#mcgxba-rt-507').val() || '';
  var yczyqt = getVal('[id="mcgxba-rt-508"]:checked') || ''; //远处其他
  var yczyqtInpVal = $('#mcgxba-rt-509').val() || '';//远处转移其他val

  //其他占位
  var gnzVal = getVal('[id="mcgxba-rt-510"]:checked') || ''; //肝囊肿val
  var gnzInpVal = $('#mcgxba-rt-511').val() || '';
  var gxglVal = getVal('[id="mcgxba-rt-512"]:checked') || ''; //肝血管瘤val
  var gxglInpVal = $('#mcgxba-rt-513').val() || '';
  var glxqtVal = getVal('[id="mcgxba-rt-514"]:checked') || ''; //其他占位其他val
  var glxqInpVal = $('#mcgxba-rt-515').val() || '';

  //胆囊
  var dnVal = getVal($('input[type=radio][name=dn]:checked')) || '';
  var dnxt = getVal($('input[type=radio][name=dnxt]:checked')) || '';//形态
  var dnbzh = getVal($('input[type=radio][name=dnbzh]:checked')) || '';
  var dndx1 = $('#mcgxba-rt-522').val() || ''; //大小
  var dndx2 = $('#mcgxba-rt-523').val() || ''; //大小
  var dnjs = getVal('[id="mcgxba-rt-524"]:checked') || ''; //胆囊结石
  var dnjsInpVal = $('#mcgxba-rt-525').val() || '';
  var dznc = getVal('[id="mcgxba-rt-526"]:checked') || ''; //胆汁粘稠
  var dzncInpVal = $('#mcgxba-rt-527').val() || '';
  var xjz = getVal('[id="mcgxba-rt-528"]:checked') || ''; //腺肌症
  var xjzInpVal = $('#mcgxba-rt-529').val() || '';
  var dny = getVal('[id="mcgxba-rt-530"]:checked') || ''; //胆囊炎
  var dnyInpVal = $('#mcgxba-rt-531').val() || '';
  var xr = getVal('[id="mcgxba-rt-532"]:checked') || ''; //息肉/腺瘤
  var xrInpVal = $('#mcgxba-rt-533').val() || '';
  var dnqt = getVal('[id="mcgxba-rt-534"]:checked') || ''; //胆囊其他
  var dnqtInpVal = $('#mcgxba-rt-535').val() || ''; //胆囊其他

  //脾脏
  var pzVal = getVal($('input[type=radio][name=pz]:checked')) || '';
  var pzxt = getVal($('input[type=radio][name=pzxt]:checked')) || '';//形态
  var pzxtqt = $('#mcgxba-rt-541').val() || '';//形态其他
  var pzdx1 = $('#mcgxba-rt-542').val();
  var pzdx2 = $('#mcgxba-rt-543').val();
  var pnz = getVal('[id="mcgxba-rt-544"]:checked') || ''; //脾囊肿
  var pnzInpVal = $('#mcgxba-rt-545').val() || '';
  var pzqt = getVal('[id="mcgxba-rt-546"]:checked') || ''; //脾脏其他
  var pzqtInpVal = $('#mcgxba-rt-547').val() || '';

  // 胰腺
  var yxVal = getVal($('input[type=radio][name=yx]:checked')) || '';
  var yxxt = getVal($('input[type=radio][name=yxxt]:checked')) || '';//形态
  var yxxtqt = $('#mcgxba-rt-553').val() || '';//形态其他
  var yxnz = getVal('[id="mcgxba-rt-554"]:checked') || ''; //胰腺囊肿
  var yxnzInpVal = $('#mcgxba-rt-555').val() || '';
  var ipmn = getVal('[id="mcgxba-rt-556"]:checked') || ''; //ipmn
  var ipmnInpVal = $('#mcgxba-rt-557').val() || '';
  var yxy = getVal('[id="mcgxba-rt-558"]:checked') || ''; //胰腺炎
  var yxyInpVal = $('#mcgxba-rt-559').val() || '';
  var sjnfb = getVal('[id="mcgxba-rt-560"]:checked') || ''; //神经内分泌肿瘤
  var sjnfbInpVal = $('#mcgxba-rt-561').val() || '';
  var yxqt = getVal('[id="mcgxba-rt-562"]:checked') || ''; //脾脏其他
  var yxqtInpVal = $('#mcgxba-rt-563').val() || '';
  var ygqk = $('#mcgxba-rt-564').val() || ''; //胰管情况

  //腹腔
  var fqzfjx = getVal($('input[type=radio][name=fqzfjx]:checked')) || ''; //腹腔脂肪间隙
  var fs = getVal($('input[type=radio][name=fs]:checked')) || ''; //腹水
  var fsyl = getVal($('input[type=radio][name=fsyl]:checked')) || ''; //腹水
  var fsInpVal = getVal('#mcgxba-rt-573') || ''; //腹水详情
  var fqjx = getVal($('input[type=radio][name=fqjx]:checked')) || "";//积血
  var fqjxqt = $('#mcgxba-rt-576').val();//积血其他
  var fmzh = getVal($('input[type=radio][name=fmzh]:checked')) || ''; //增厚
  var fmqdzh = getVal($('input[type=checkbox][name=fmqdzh]:checked')) || ''; //增厚位置
  var fmzy = getVal($('input[type=radio][name=fmzy]:checked')) || ''; //腹膜种植转移
  var fmzyInpVal = $('#mcgxba-rt-585').val();//腹膜种植转移详情

  //其它征象
  var qtzx = $('#mcgxba-rt-586').val();

  // 印象推导
  $('#mcgxba-rt-587').val('');
  if (yfzVal) {
    yfbzsm === '单发' ? yfzwz = yfzwz.replace(/、/g, '-') : ''
    yfzwz ? impressionArr1.push(`肝${yfzwz}段肝细胞癌` + (yfzrads ? `（${yfzrads.replace(/、/g, '；')}）` : '')) : ''
  }
  
  fqjx && fqjx === '有' ? impressionArr1.push(`破裂出血并腹腔内积血/血肿形成`) : ''

  if (zzVal) {
    zzsm ? impressionArr1.push(`肝内可见${zzsm}子灶` + (zzrads ? `（${zzrads.replace(/、/g, '；')}）` : '')) : ''
  }

  // 门静脉系统、肝静脉、下腔静脉印象
  let mjmFormData = getVeinNameInput(mjmxtStrList, 'mjmName'); // 门静脉系统表单内容
  let gjmFormData = getVeinNameInput(gjmStrList, 'gjmName'); // 肝静脉表单内容
  let xqjmFormData = getVeinNameInput(xqjmStrList, 'xqjmName'); // 下腔静脉表单内容
  // console.log(mjmFormData);
  // console.log(gjmFormData);
  // console.log(xqjmFormData);
  let mjmSqArr = mjmFormData.filter(item => item.keyName === '受侵' && item.value === '可见');
  let gjmSqArr = gjmFormData.filter(item => item.keyName === '受侵' && item.value === '可见');
  let xqjmSqArr = xqjmFormData.filter(item => item.keyName === '受侵' && item.value === '可见');
  let mjmXsArr = mjmFormData.filter(item => item.keyName === '血栓' && item.value === '有');
  let gjmXsArr = gjmFormData.filter(item => item.keyName === '血栓' && item.value === '有');
  let xqjmXsArr = xqjmFormData.filter(item => item.keyName === '血栓' && item.value === '有');
  let mjmAsArr = mjmFormData.filter(item => item.keyName === '癌栓' && item.value === '有');
  let gjmAsArr = gjmFormData.filter(item => item.keyName === '癌栓' && item.value === '有');
  let xqjmAsArr = xqjmFormData.filter(item => item.keyName === '癌栓' && item.value === '有');
  if (mjmXsArr.length || mjmAsArr.length) {
    if (mjmXsArr.length) {
      impressionArr1.push(`门静脉系统血栓：${mjmXsArr.map(item => item.veinNames).join('、')}`);
    }
    if (mjmAsArr.length) {
      impressionArr1.push(`门静脉系统癌栓：${mjmAsArr.map(item => item.veinNames).join('、')}`);
    }
  } else {
    if (mjmSqArr.length) {
      impressionArr1.push(`门静脉系统受侵：${mjmSqArr.map(item => item.veinNames).join('、')}`);
    }
  }
  if (gjmXsArr.length || gjmAsArr.length) {
    if (gjmXsArr.length) {
      impressionArr1.push(`肝静脉血栓：${gjmXsArr.map(item => item.veinNames).join('、')}`);
    }
    if (gjmAsArr.length) {
      impressionArr1.push(`肝静脉癌栓：${gjmAsArr.map(item => item.veinNames).join('、')}`);
    }
  } else {
    if (gjmSqArr.length) {
      impressionArr1.push(`肝静脉受侵：${gjmSqArr.map(item => item.veinNames).join('、')}`);
    }
  }
  if (xqjmXsArr.length || xqjmAsArr.length) {
    if (xqjmXsArr.length) {
      impressionArr1.push(`下腔静脉血栓：${xqjmXsArr.map(item => item.veinNames).join('、')}`);
    }
    if (xqjmAsArr.length) {
      impressionArr1.push(`下腔静脉癌栓：${xqjmAsArr.map(item => item.veinNames).join('、')}`);
    }
  } else {
    if (xqjmSqArr.length) {
      impressionArr1.push(`下腔静脉受侵：${xqjmSqArr.map(item => item.veinNames).join('、')}`);
    }
  }
  
  // let mjmsq = isChecked(mjmxtStrList, 'sq');
  // let gjmsq = isChecked(gjmStrList, 'sq');
  // let xqjmsq = isChecked(xqjmStrList, 'sq');
  // let mjmxs = isChecked(mjmxtStrList, 'xs');
  // let gjmxs = isChecked(gjmStrList, 'xs');
  // let xqjmxs = isChecked(xqjmStrList, 'xs');
  // let mjmas = isChecked(mjmxtStrList, 'as');
  // let gjmas = isChecked(gjmStrList, 'as');
  // let xqjmas = isChecked(xqjmStrList, 'as');
  // let sqArr = [], xsArr = [], asArr = [], sameJms = [];
  // mjmsq ? sqArr.push('门静脉') : '';
  // gjmsq ? sqArr.push('肝静脉') : '';
  // xqjmsq ? sqArr.push('下腔静脉') : '';
  // sqArr && sqArr.length > 0 ? impressionArr1.push(`${sqArr.join('、')}受侵`) : '';
  // mjmxs ? xsArr.push('门静脉') : ''
  // gjmxs ? xsArr.push('肝静脉') : ''
  // xqjmxs ? xsArr.push('下腔静脉') : '';
  // mjmas ? asArr.push('门静脉') : ''
  // gjmas ? asArr.push('肝静脉') : ''
  // xqjmas ? asArr.push('下腔静脉') : '';
  // sameJms = xsArr.filter(x => asArr.includes(x));
  // xsArr = xsArr.filter(x => !sameJms.includes(x));
  // asArr = asArr.filter(x => !sameJms.includes(x));
  // xsArr && xsArr.length > 0 ? impressionArr1.push(`${xsArr.join('、')}血栓形成`) : '';
  // asArr && asArr.length > 0 ? impressionArr1.push(`${asArr.join('、')}癌栓形成`) : '';
  // sameJms && sameJms.length ? impressionArr1.push(`${sameJms.join('、')}血栓、癌栓形成`) : '';

  dgsqVal === '可见' ? str += '胆管可见受侵' : ''
  if (dskzVal === '有') {
    str ? str += "、胆栓形成" : '胆管可见胆栓'
  }
  str ? impressionArr1.push(`${str}`) : '';
  str = '';

  // 淋巴结
  if (lbjVal === '可见异常') {
    // 转移
    let lbjArr = [];
    if (lbj_yc) {
      xgjVal ? lbjArr.push(xgjVal) : '';
      gmbVal ? lbjArr.push(gmbVal) : '';
      fmhVal ? lbjArr.push(fmhVal) : '';
      xdVal ? lbjArr.push(xdVal) : ''; // 心底
      lbjqtVal ? lbjArr.push(lbjqtInpVal) : '';
      // lbjqtInpVal ? lbjArr.push(lbjqtInpVal) : '';
      lbjArr && lbjArr.length > 0 ? impressionArr1.push(`${lbjArr.join('、')}可见淋巴结${lbj_yc}`) : '';
    }
    // 增生
    lbjArr = [];
    if (lbj_yc_zs) {
      xgjVal_zs ? lbjArr.push(xgjVal_zs) : '';
      gmbVal_zs ? lbjArr.push(gmbVal_zs) : '';
      fmhVal_zs ? lbjArr.push(fmhVal_zs) : '';
      xdVal_zs ? lbjArr.push(xdVal_zs) : ''; // 心底
      lbjqtVal_zs ? lbjArr.push(lbjqtInpVal_zs) : '';
      // lbjqtInpVal_zs ? lbjArr.push(lbjqtInpVal_zs) : '';
      lbjArr && lbjArr.length > 0 ? impressionArr1.push(`${lbjArr.join('、')}可见淋巴结${lbj_yc_zs}`) : '';
    }
  }

  let yczyArr = [];
  if (yczyfb) {
    fbwz ? yczyArr.push(fbwz) : yczyArr.push(yczyfb)
  }
  yczygt ? yczyArr.push(yczygt) : ''
  // yczyqtInpVal ? yczyArr.push(yczyqtInpVal) : ''
  yczyArr && yczyArr.length > 0 ? impressionArr1.push(`远处转移：${yczyArr.join('、')}`) : '';
  fmzy && fmzy === '有' ? impressionArr1.push(`腹膜种植转移`) : '';

  gyhVal && gyhVal !== '无' ? impressionArr2.push(gyhVal) : ''
  // gyhVal && gyhVal === '无' ? $('#mcgxba-rt-132').val('') : ''
  zfgVal && zfgVal !== '无' ? str += zfgVal : ''
  // zfgVal && zfgVal === '无' ? $('#mcgxba-rt-623').val('') : ''
  if (gztgzVal === '有') {
    str ? str += "并铁过载" : str += '铁过载'
  }
  str ? impressionArr2.push(str) : ''
  str = '';
  dmqxlgzVal && dmqxlgzVal !== '无' ? impressionArr2.push(dmqxlgzVal) : ''

  // 脾脏形态增大印象
  pzxt && pzxt === '增大' && impressionArr2.push('脾大');

  let mmgyArr = []
  jmqzVal ? mmgyArr.push(jmqzVal + '曲张') : ''
  fqjmkfVal === '是' ? mmgyArr.push('脐静脉开放') : ''
  wsflVal === '是' ? mmgyArr.push('胃-肾分流') : ''
  psflVal === '是' ? mmgyArr.push('脾-肾分流') : ''
  // mmgyArr && mmgyArr.length > 0 ? impressionArr2.push(mmgyArr.join('，')) : ''

  fsyl ? str += `${fsyl}腹水` : '';
  if (fqzfjx && fqzfjx !== '清晰' || fmzh === '轻度增厚') {
    str ? str += "，腹膜炎" : str = '腹膜炎'
  }
  str && mmgyArr.push(str);
  mmgyArr.length > 0 ? impressionArr2.push(`门脉高压：${mmgyArr.join('，')}`) : ''
  impressionArr1.length > 0 ? impressionArr.push(impressionArr1.join('；')) : ''
  impressionArr2.length > 0 ? impressionArr.push(impressionArr2.join('，')) : ''
  mmgyArr.length === 0 && str ? impressionArr.push(str) : ''
  str = '';
  let glxzwArr = [];
  gnzVal ? glxzwArr.push(gnzVal) : ''
  gxglVal ? glxzwArr.push(gxglVal) : ''
  glxzwArr.length > 0 ? impressionArr.push(glxzwArr.join('、')) : ''

  let dnArr = [];
  dnjs ? dnArr.push(dnjs) : ''
  dznc ? dnArr.push(dznc) : ''
  xjz ? dnArr.push(xjz) : ''
  dny ? dnArr.push(dny) : ''
  xr ? dnArr.push(xr) : ''
  if (dnArr.length > 0) {
    if (dnbzh && dnbzh === '增厚') {
      dnArr.unshift('胆囊壁增厚');
    }
    impressionArr.push(`胆囊（${dnArr.join('、')}）`);
  } else {
    if (dnbzh && dnbzh === '增厚') {
      impressionArr.push(`胆囊壁增厚`);
    }
  }
  pnz ? impressionArr.push(`脾${pnz}`) : ''

  let yxArr = []
  yxnz ? yxArr.push(yxnz) : '';
  ipmn ? yxArr.push(ipmn) : '';
  yxy ? yxArr.push(yxy) : '';
  sjnfb ? yxArr.push(sjnfb) : '';
  if (yxArr.length > 0) {
    if (yxxt && yxxt === '增大') {
      yxArr.unshift('胰腺增大');
    }
    impressionArr.push(`胰腺（${yxArr.join('、')}）`);
  } else {
    if (yxxt && yxxt === '增大') {
      impressionArr.push(`胰腺增大`);
    }
  }

  if (impressionArr.length > 0) {
    for (let i = 0; i < impressionArr.length; i++) {
      impressionArr[i] = `${i + 1}.${impressionArr[i]}`
    }
    impressionArr.length > 0 ? $('#mcgxba-rt-587').val(impressionArr.join('。\n')+'。') : ''
  }

  // 影像描述推导
  descriptionStr = '';
  let descriptionArr = [], line = [];
  //-------------------------------------------------------------------------------
  // var gztj = getVal($('input[type=radio][name=gztj]:checked')) || ""; //肝脏体积
  // var gzxt = getVal($('input[type=radio][name=xt]:checked')) || ""; //形态
  // var gybl = getVal($('input[type=radio][name=gybl]:checked')) || ""; //肝叶比例
  // var gyblqt = $('#mcgxba-rt-13').val();//肝叶比例其他
  // var gl = getVal($('input[type=radio][name=gl]:checked')) || ""; //肝裂
  // gztj === '正常' && gzxt === '光滑' ? line.push('肝脏大小、形态未见异常') : line.push(`肝脏体积${gztj}，表面${gzxt}`)
  // gybl == '协调' ? (str = gybl) : (gyblqt ? str = gyblqt : str = gybl)
  // line.push(`肝叶比例${str}`)
  // str = '';
  // line.push(`肝裂${gl}`);
  // line.length > 0 ? descriptionArr.push("　　肝脏整体形态：" + line.join('，')) : ''
  // line = [];
  // ------------------------------------------------------------------------------
  if (yfzVal) {
    yfbzsm === '单发' ? str = '一' : '';
    yfbzsm === '多发' ? str = '多发' : '';
    yfbzsm === '单发' ? yfzwz = yfzwz.replace(/、/g, '-') : ''
    yfzwz ? line.push(`肝内${yfzwz}段见${str}${yfzxz}病灶`) : '';
    yfbzgs && line.push(yfbzgs);
    str = '';
    yfzfx ? line.push(`形态呈${yfzfx}`) : '';
    yfzbj ? line.push(`边界${yfzbj}`) : '';
    yfzzdwy && line.push(`最大者位于${yfzzdwy}段`)
    yfzdx1 || yfzdx2 ? line.push(`${yfbzsm === '多发' ? '最大者大小约' : '大小约'}：${yfzdx1}mmx${yfzdx2}mm`) : ''
    yfT1WI ? line.push(`T1WI呈${yfT1WI}`) : '';
    yfT2WI ? line.push(`T2WI呈${yfT2WI}`) : '';
    yfDWI ? line.push(`DWI呈${yfDWI}`) : '';
    yfADC ? line.push(`ADC图呈${yfADC}`) : '';
    yfzT1 === '是' ? line.push(`T1同反相位序列存在脂肪变性`) : ''
    // yfzR2zsg === '是' ? line.push(`R2*值升高`) : ''
    yfzpsmd ? line.push(`病灶平扫呈${yfzpsmd}`) : '';
    yfzpsmd2 ? str = `密度${yfzpsmd2}` : '';
    if (yfzpsmdbj) {
      str2 = yfzpsmdbj === '其他' ? yfzbjyqt : yfzpsmdbj;
      str += `（${str2}）`;
    }
    str ? line.push(str) : ''
    str = '', str2 = '';
    yfzzqdmwq ? line.push(`增强动脉晚期${yfzzqdmwq}`) : '';
    yfzzqmjmq ? str = `门静脉期${yfzzqmjmq}` : '';
    yfzzqycq ? str2 = `延迟期${yfzzqycq}` : '';

    var yfzzqyxq2 = '';
    if (yfzzqyxq) {
      yfzzqyxq2 = `移行期${yfzzqyxq}`;
    }

    yfzzqmjmq === yfzzqycq ? str3 = `门静脉期、延迟期${yfzzqmjmq}` : ''
    if (str3) {
      line.push(str3)
      yfzzqyxq2 && line.push(yfzzqyxq2);
    } else {
      str ? line.push(str) : ''
      str2 ? line.push(str2) : ''
      yfzzqyxq2 && line.push(yfzzqyxq2);
    }
    str = '', str2 = '', str3 = '';
    yfzzqxs ? line.push(`呈${yfzzqxs === '其他' ? yfzzqxsqt : yfzzqxs}`) : '';
    yfzbmzInpVal ? line.push(`有${yfzbmzInpVal}强化包膜`) : (yfzbmz === '无' ? line.push('未见明显延迟性强化包膜') : '');
    yfzgdqbxVal ? line.push(`肝胆期表现呈${yfzgdqbxVal}`) : ''
    yfgdqbxVal ? line.push(`${yfgdqbxVal}`) : ''
    descriptionArr.push("　　原发灶：" + line.join('，'));
    line = [];
  }
  //-------------------------------------------------------------------------------
  if (zzVal) {
    zzsm === '单发' ? str = '一' : '';
    zzsm === '多发' ? str = '多发' : '';
    str || zzxz ? line.push(`肝内可见${str}大小不等的${zzxz}病灶`) : '';
    zzgs && line.push(zzgs);
    str = '';
    zzfx ? line.push(`形态呈${zzfx}`) : '';
    zzbj ? line.push(`边界${zzbj}`) : '';
    zzsm === '单发' ? zzwz = zzwz.replace(/、/g, '-') : ''
    zzzdwy && line.push(`最大者位于${zzzdwy}段`)
    zzdx1 || zzdx2 ? line.push(`${zzsm === '多发' ? '最大者大小约' : '大小约'}：${zzdx1}mmx${zzdx2}mm`) : ''
    zzT1WI ? line.push(`T1WI呈${zzT1WI}`) : '';
    zzT2WI ? line.push(`T2WI呈${zzT2WI}`) : '';
    zzDWI ? line.push(`DWI呈${zzDWI}`) : '';
    zzADC ? line.push(`ADC图呈${zzADC}`) : '';
    zzT1 === '是' ? line.push(`T1同反相位序列存在脂肪变性`) : ''
    // zzR2zsg === '是' ? line.push(`R2*值升高`) : ''
    zzpsmd ? line.push(`病灶平扫呈${zzpsmd}`) : '';
    zzpsmd2 ? str = `密度${zzpsmd2}` : '';
    if (zzsmdbj) {
      str2 = zzsmdbj === '其他' ? zzbjyqt : zzsmdbj;
      str += `（${str2}）`;
    }
    str ? line.push(str) : '';
    str = '', str2 = '';
    if (zzzqdmwq === yfzzqdmwq &&
      zzzqmjmq === yfzzqmjmq &&
      zzzqycq === yfzzqycq &&
      zzzqyxq === yfzzqyxq &&
      zzzqxs === yfzzqxs
    ) {
      line.push('增强强化形式同上述病灶类似')
    } else {
      zzzqdmwq ? line.push(`增强动脉晚期${zzzqdmwq}`) : '';
      zzzqmjmq ? str = `门静脉期${zzzqmjmq}` : '';
      zzzqycq ? str2 = `延迟期${zzzqycq}` : '';

      var zzzqyxq2 = '';
      if (zzzqyxq) {
        zzzqyxq2 = `移行期${zzzqyxq}`;
      }

      zzzqmjmq === zzzqycq ? str3 = `门静脉期、延迟期${yfzzqmjmq}` : ''
      if (str3) {
        line.push(str3)
        zzzqyxq2 && line.push(zzzqyxq2);
      } else {
        str ? line.push(str) : ''
        str2 ? line.push(str2) : ''
        zzzqyxq2 && line.push(zzzqyxq2);
      }
      str = '', str2 = '', str3 = '';
      zzzqxs ? line.push(`呈${zzzqxs === '其他' ? zzzqxsqt : zzzqxs}`) : '';
    }

    zzbmzInpVal ? line.push(`有${zzbmzInpVal}强化包膜`) : (zzbmz === '无' ? line.push('未见明显延迟性强化包膜') : '');
    zzgdqbx ? line.push(`肝胆期表现呈${zzgdqbx}`) : ''
    zzgdqbxVal ? line.push(`${zzgdqbxVal}`) : ''
    descriptionArr.push("　　子灶：" + line.join('，'));
    line = [];
  }
  //-------------------------------------------------------------------------------
  let gbzqtArr = []; // 肝背景填写

  var gztj = getVal($('input[type=radio][name=gztj]:checked')) || ""; //肝脏体积
  var gzxt = getVal($('input[type=radio][name=xt]:checked')) || ""; //形态
  var gybl = getVal($('input[type=radio][name=gybl]:checked')) || ""; //肝叶比例
  var gyblqt = $('#mcgxba-rt-13').val();//肝叶比例其他
  var gl = getVal($('input[type=radio][name=gl]:checked')) || ""; //肝裂
  gztj === '正常' && gzxt === '光滑' ? gbzqtArr.push('肝脏大小、形态未见异常') : gbzqtArr.push(`肝脏体积${gztj}，表面${gzxt}`)
  gybl == '协调' ? (str = gybl) : (gyblqt ? str = gyblqt : str = gybl)
  gbzqtArr.push(`肝叶比例${str}`)
  str = '';
  gbzqtArr.push(`肝裂${gl}`);
  line = [];

  gyhInpVal ? gbzqtArr.push(gyhInpVal) : ""
  zfgqt ? gbzqtArr.push(zfgqt) : ""
  gztgzVal === '有' && tgzqt ? gbzqtArr.push(tgzqt) : ""
  dmqxlgzqt && gbzqtArr.push(dmqxlgzqt);
  gbzqtArr.length > 0 && descriptionArr.push("　　肝背景：" + gbzqtArr.join('，'))
  //-------------------------------------------------------------------------------
  jmqzInpVal ? line.push(jmqzInpVal) : "";
  fqjmkfVal === '是' && fqjmkfInpVal ? line.push(fqjmkfInpVal) : "";
  wsflVal === '是' && wsflInpVal ? line.push(wsflInpVal) : "";
  psflVal === '是' && psflInpVal ? line.push(psflInpVal) : "";
  mmgyqtInpVal ? line.push(mmgyqtInpVal) : "";
  line.length > 0 ? descriptionArr.push("　　门脉高压：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  var mjmzgkd = $('#mcgxba-rt-148').val();
  let mjStr = '';
  mjStr = forJmArrFun2(mjmxtStrList, 'mjmName') || ''
  let gjmStr = '';
  gjmStr = forJmArrFun2(gjmStrList, 'gjmName') || ''
  let xqjmStr = "";
  xqjmStr = forJmArrFun2(xqjmStrList, 'xqjmName') || ''
  // console.log('gjmArr2', gjmStr);
  // console.log('xqjmArr2', xqjmStr);
  mjmzgkd ? mjStr = `门脉主干宽约${mjmzgkd}mm。肝内` + mjStr : ''
  mjStr ? descriptionArr.push("　　门静脉系统：" + mjStr) : ''
  gjmStr ? descriptionArr.push("　　肝静脉：" + gjmStr) : ''
  xqjmStr ? descriptionArr.push("　　下腔静脉：" + xqjmStr) : ''
  //-------------------------------------------------------------------------------
  if (dgVal && dgVal === '无异常') { 
    line.push('未见异常');
  }else {
    dgsqkj ? line.push(`${dgsqkj}可见受侵`) : ''
    dgkzVal === '可见' ? line.push(`肝内胆管扩张`) : ''
    dgkzInpVal ? line.push(`最宽处约${dgkzInpVal}mm`) : ''
    dskzVal === '有' ? str += '胆管可见胆栓形成' : '';
    dskzVal === '有' && dskzInpVal ? str += '，' + dskzInpVal : ''
    str ? line.push(str) : '';
    str = '';
  }
  line.length > 0 ? descriptionArr.push("　　胆管：" + line.join('，')) : ''
  line = []
  //-------------------------------------------------------------------------------
  if (lbjVal && lbjVal === '无异常') { 
    line.push('未见异常');
  }else {
    var lbjDesArr = [];
    if (lbj_yc) { // 转移
      str = '';
      xgjVal ? str += xgjVal : '' 
      xgjsl ? str += `，${xgjsl}枚` : ''
      xgjdj ? str += `，最大短径约${xgjdj}mm` : '';
      xgjzq ? str += `，增强${xgjzq}` : '';
      str ? line.push(str) : '';
      str = '';
      gmbVal ? str += gmbVal : ''
      gmbsl ? str += `，${gmbsl}枚` : ''
      gmbdj ? str += `，最大短径约${gmbdj}mm` : '';
      gmbzq ? str += `，增强${gmbzq}` : '';
      str ? line.push(str) : '';
      str = '';
      fmhVal ? str += fmhVal : ''
      fmhsl ? str += `，${fmhsl}枚` : ''
      fmhdj ? str += `，最大短径约${fmhdj}mm` : '';
      fmhzq ? str += `，增强${fmhzq}` : '';
      str ? line.push(str) : '';
      // 心底
      str = '';
      xdVal ? str += xdVal : ''
      xdsl ? str += `，${xdsl}枚` : ''
      xddj ? str += `，最大短径约${xddj}mm` : '';
      xdzq ? str += `，增强${xdzq}` : '';
      str ? line.push(str) : '';
      str = '';
      lbjqtInpVal ? str += lbjqtInpVal : ''
      lbjqtsl ? str += `，${lbjqtsl}枚` : ''
      lbjqtdj ? str += `，最大短径约${lbjqtdj}mm` : '';
      str ? line.push(str) : '';
      str = '';
      var lbjzs = Number(xgjsl || '0') + Number(gmbsl || '0') + Number(fmhsl || '0') + Number(lbjqtsl || '0') + Number(xdsl || '0');
      var timst = line.length > 1 ? '分别位于' : ''
      lbjzs ? line[0] = `${lbjzs}枚可疑淋巴结${lbj_yc}，${timst}${line[0]}` : ''
      line.length > 0 && lbjDesArr.push(line.join('；'));
      line = [];
    }
    if (lbj_yc_zs) { // 炎性增生
      str = '';
      xgjVal_zs ? str += xgjVal_zs : '' 
      xgjsl_zs ? str += `，${xgjsl_zs}枚` : ''
      xgjdj_zs ? str += `，最大短径约${xgjdj_zs}mm` : '';
      xgjzq_zs ? str += `，增强${xgjzq_zs}` : '';
      str ? line.push(str) : '';
      str = '';
      gmbVal_zs ? str += gmbVal_zs : ''
      gmbsl_zs ? str += `，${gmbsl_zs}枚` : ''
      gmbdj_zs ? str += `，最大短径约${gmbdj_zs}mm` : '';
      gmbzq_zs ? str += `，增强${gmbzq_zs}` : '';
      str ? line.push(str) : '';
      str = '';
      fmhVal_zs ? str += fmhVal_zs : ''
      fmhsl_zs ? str += `，${fmhsl_zs}枚` : ''
      fmhdj_zs ? str += `，最大短径约${fmhdj_zs}mm` : '';
      fmhzq_zs ? str += `，增强${fmhzq_zs}` : '';
      str ? line.push(str) : '';
      // 心底
      str = '';
      xdVal_zs ? str += xdVal_zs : ''
      xdsl_zs ? str += `，${xdsl_zs}枚` : ''
      xddj_zs ? str += `，最大短径约${xddj_zs}mm` : '';
      xdzq_zs ? str += `，增强${xdzq_zs}` : '';
      str ? line.push(str) : '';
      str = '';
      lbjqtInpVal_zs ? str += lbjqtInpVal_zs : ''
      lbjqtsl_zs ? str += `，${lbjqtsl_zs}枚` : ''
      lbjqtdj_zs ? str += `，最大短径约${lbjqtdj_zs}mm` : '';
      str ? line.push(str) : '';
      str = '';
      var lbjzs = Number(xgjsl_zs || '0') + Number(gmbsl_zs || '0') + Number(fmhsl_zs || '0') + Number(lbjqtsl_zs || '0') + Number(xdsl_zs || '0');
      var timst = line.length > 1 ? '分别位于' : ''
      lbjzs ? line[0] = `${lbjzs}枚可疑淋巴结${lbj_yc_zs}，${timst}${line[0]}` : ''
      line.length > 0 && lbjDesArr.push(line.join('；'));
      line = [];
    }
    lbjDesArr.length > 0 && line.push(lbjDesArr.join('。'));
  }
  line.length > 0 ? descriptionArr.push("　　淋巴结：" + line.join('；')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  fbwz ? str += fbwz : ''
  yczyfb && fbwzInpVal ? str += `（${fbwzInpVal}）` : '';
  str ? line.push(str) : ''
  str = ''
  yczygt ? str += yczygt : '';
  yczygt && yczygtInpVal ? str += `（${yczygtInpVal}）` : '';
  str ? line.push(str) : ''
  str = ''
  yczyqtInpVal ? line.push(yczyqtInpVal) : '';
  line.length > 0 ? descriptionArr.push("　　远处转移：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  gnzVal && gnzInpVal ? line.push(gnzInpVal) : ''
  gxglVal && gxglInpVal ? line.push(gxglInpVal) : ''
  glxqInpVal ? line.push(glxqInpVal) : ''
  line.length > 0 ? descriptionArr.push("　　其他占位：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (dnVal && dnVal === '无异常') { 
    line.push('大体形态未见异常，壁无增厚');
  } else {
    pzxt && dnxt === '增大' ? line.push(`形态${dnxt}`) : '';
    dnbzh ? line.push(`胆囊壁${dnbzh}`) : '';
    dndx1 || dndx2 ? line.push(`大小约：${dndx1}mmx${dndx2}mm`) : '';
    dnjs && dnjsInpVal ? line.push(dnjsInpVal) : '';
    dznc && dzncInpVal ? line.push(dzncInpVal) : '';
    xjz && xjzInpVal ? line.push(xjzInpVal) : '';
    dny && dnyInpVal ? line.push(dnyInpVal) : '';
    xr && xrInpVal ? line.push(xrInpVal) : '';
    dnqtInpVal ? line.push(dnqtInpVal) : '';
  }
  line.length > 0 ? descriptionArr.push("　　胆囊：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (pzVal && pzVal === '无异常') {
    line.push('大体形态未见异常');
  } else {
    pzxt && pzxt !== '其他' && pzxt !== '正常' ? line.push(`形态${pzxt}`) : '';
    pzxtqt ? line.push(`形态${pzxtqt}`) : '';
    pzdx1 || pzdx2 ? line.push(`大小约：${pzdx1}mmx${pzdx2}mm`) : '';
    pnz && pnzInpVal ? line.push(pnzInpVal) : '';
    pzqtInpVal ? line.push(pzqtInpVal) : '';
  }
  line.length > 0 ? descriptionArr.push("　　脾脏：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (yxVal && yxVal === '无异常') {
    line.push('大体形态未见异常');
  } else {
    yxxt && yxxt !== '其他' && pzxt !== '正常' ? line.push(`形态${yxxt}`) : '';
    yxxtqt ? line.push(`形态${yxxtqt}`) : '';
    yxnz && yxnzInpVal ? line.push(yxnzInpVal) : '';
    ipmn && ipmnInpVal ? line.push(ipmnInpVal) : '';
    yxy && yxyInpVal ? line.push(yxyInpVal) : '';
    sjnfb && sjnfbInpVal ? line.push(sjnfbInpVal) : '';
    yxqtInpVal ? line.push(yxqtInpVal) : '';
    ygqk ? line.push(ygqk) : '';
  }
  line.length > 0 ? descriptionArr.push("　　胰腺：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  fqzfjx && fqzfjx !== '清晰' ? line.push(`腹腔脂肪间隙${fqzfjx}`) : '';
  // fs === "无" ? line.push(`腹腔内无液体密度影`) : line.push(`腹腔内可见${fsyl || ''}液体密度影`);
  fs === "无" ? '' : line.push(`腹腔内见${fsyl || ''}积液`);
  fs === '有' && fsInpVal ? line.push(`${fsInpVal}`) : ''
  fqjx === '有' && fqjxqt ? line.push(fqjxqt) : ''
  fmqdzh ? line.push(fmqdzh + fmzh) : ''
  fmzy === '有' && fmzyInpVal ? line.push(fmzyInpVal) : ''
  line.length > 0 ? descriptionArr.push("　　腹腔：" + line.join('，')) : descriptionArr.push("　　腹腔：未见异常")
  line = [];
  //-------------------------------------------------------------------------------
  qtzx ? descriptionArr.push("　　其他征象：" + qtzx) : '';
  //-------------------------------------------------------------------------------
  descriptionStr = descriptionArr.join('。\n') + '。';
  console.log('>>>descriptionStr');
  console.log(descriptionStr);
}

function forJmArrFun(list) {
  let arr = []
  for (let i = 0; i < list.length; i++) {
    arr.push(list[i].map(item => {
      var el = document.querySelector(`input[name=${item}]`);
      if (!el) {
        return '';
      }
      var curVal = getVal($(`input[type=radio][name=${item}]:checked`));
      if (!isParentChecked(el)) { // 如果父元素未勾选，返回默认正常值
        // if (curVal === '不清') {
        //   curVal = '尚清';
        // }
        // if (curVal === '可见') {
        //   curVal = '未见';
        // }
        // if (curVal === '有') {
        //   curVal = '无';
        // }
        curVal = '';
      }
      if (el.type === 'radio') { // 单选按钮直接返回
        return curVal;
      }
      if (el.type === 'text') { // 处理隐藏输入框内容
        let pid = $(el).attr('pid');
        if ($('#' + pid).is(':checked')) {
          return getVal($(`input[name=${item}]`));
        } else {
          return '';
        }
      }
    }));
  }
  return arr;
}

// 检查父元素是否选择
function isParentChecked(el) {
  var pid = el.getAttribute('pid');
  if (pid) {
    var pel = document.getElementById(pid);
    if (pel) {
      if (pel.type === 'checkbox' || pel.type === 'radio') {
        if (pel.checked) {
          return isParentChecked(pel);
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      return true;
    }
  } else {
    return true;
  }
}

//门静脉、 肝静脉、下腔静脉分支内容汇总
function getVeinNameInput(list, type) {
  let propName = ['病灶分界', '受侵', '血栓', '血栓密度', '癌栓', '癌栓密度'];
  let veinNames = [];
  veinNames = veinNameData[type];
  let tempResults = [];
  for (let i = 0; i < propName.length; i++) {
    let prop = propName[i];
    for (let j = 0; j < list.length; j++) {
      let val = list[j][i] || '';
      if (val) {
        tempResults.push({
          veinNames: veinNames[j],
          keyName: prop,
          value: val,
        })
      }
    }
  }
  return tempResults;
}

//门静脉、 肝静脉、下腔静脉内容返回
// list为分支列表，分支下为所选择的内容列表
function forJmArrFun2(list, type) {
  let tempResults = getVeinNameInput(list, type); // 各分支的内容详细列表
  let veinNameDataMap = {}; // 分支名对应内容
  let veinNameArr = []; // 按顺序的分支名列表
  for (let v of tempResults) {
    if (!veinNameDataMap[v.veinNames]) {
      veinNameArr.push(v.veinNames);
      veinNameDataMap[v.veinNames] = [];
    }
    veinNameDataMap[v.veinNames].push(v);
  }
  let descMap = {}; // 相同描述的分支名汇总，用于合并描述
  let descArr = []; // 描述列表
  let abnormalParNameArr = []; // 子支有异常的父支列表
  let normalDescArr = []; // 正常的描述
  // 处理各分支描述
  for (let name of veinNameArr) {
    let arr = veinNameDataMap[name];
    let fj = arr.find(item => item.keyName === '病灶分界') || {};
    let sq = arr.find(item => item.keyName === '受侵') || {};
    let xs = arr.find(item => item.keyName === '血栓') || {};
    let as = arr.find(item => item.keyName === '癌栓') || {};
    let xsmd = arr.find(item => item.keyName === '血栓密度') || {};
    let asmd = arr.find(item => item.keyName === '癌栓密度') || {};
    let strArr = [];
    if (fj.value) {
      strArr.push('与病灶分界' + fj.value);
    }
    if (sq.value && xs.value !== '有' && as.value !== '有') {
      strArr.push(sq.value + '受侵');
    }
    if (xsmd.value) {
      strArr.push(xsmd.value);
    }
    if (asmd.value) {
      strArr.push(asmd.value);
    }
    let desc = strArr.join('，');
    if (!descMap[desc]) {
      descMap[desc] = [];
      if (fj.value === '尚清' && sq.value === '未见' && xs.value === '无' && as.value === '无') {
        descArr.unshift(desc); // 正常的放前面
        normalDescArr.push(desc);
      } else {
        descArr.push(desc);
        let parName = veinNameChildPar[name];
        if (parName) {
          abnormalParNameArr.push(parName);
        }
      }
    }
    descMap[desc].push(name);
  }
  let veinNameInfoArr = [];
  for (let d of descArr) {
    // 子支无异常的，只需描述父支正常，子支有异常，其他子支不用描述正常
    let nArr = descMap[d].map(n => {
      let parName = veinNameChildPar[n];
      if (parName) {
        if (abnormalParNameArr.includes(parName)) {
          if (normalDescArr.includes(d)) {
            // 正常的描述 无需描述子支
            return '';
          } else {
            return n;
          }
        } else {
          return parName;
        }
      } else {
        return n;
      }
    });
    nArr = nArr.filter(n => !!n); // 去空
    nArr = [...new Set(nArr)]; // 去重
    veinNameInfoArr.push(nArr.join('、') + d);
  }
  return veinNameInfoArr.join('；');
  
  let fjArr = [], sqArr = [], xs = [], xsmd = [], as = [], asmd = [];
  fjArr[0] = tempResults.filter(item => item.value === '尚清')
  fjArr[1] = tempResults.filter(item => item.value === '不清')
  sqArr[0] = tempResults.filter(item => item.value === '未见')
  sqArr[1] = tempResults.filter(item => item.value === '可见')
  xs[0] = tempResults.filter(item => item.keyName === '血栓' && item.value === '无')
  xs[1] = tempResults.filter(item => item.keyName === '血栓' && item.value === '有')

  let xsValArr = []; // 汇总血栓输入内容
  tempResults.forEach(item => {
    if (item.keyName === '血栓密度') {
      if (tempResults.find(_item => _item.veinNames === item.veinNames && _item.keyName === '血栓' && _item.value === '有')) {
        if (!xsValArr.includes(item.value)) {
          xsValArr.push(item.value);
        }
      }
    }
  });
  xsValArr.forEach(val => {
    let sub = tempResults.filter(item => {
      if (item.keyName === '血栓密度' && item.value === val) {
        if (tempResults.find(_item => _item.veinNames === item.veinNames && _item.keyName === '血栓' && _item.value === '有')) {
          return true;
        }
      }
    });
    xsmd.push(sub);
  });
  // xsmd[0] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '低信号')
  // xsmd[1] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '等信号')
  // xsmd[2] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '稍高信号')

  as[0] = tempResults.filter(item => item.keyName === '癌栓' && item.value === '无')
  as[1] = tempResults.filter(item => item.keyName === '癌栓' && item.value === '有')

  let asValArr = []; // 汇总癌栓输入内容
  tempResults.forEach(item => {
    if (item.keyName === '癌栓密度') {
      if (tempResults.find(_item => _item.veinNames === item.veinNames && _item.keyName === '癌栓' && _item.value === '有')) {
        if (!asValArr.includes(item.value)) {
          asValArr.push(item.value);
        }
      }
    }
  });
  asValArr.forEach(val => {
    let sub = tempResults.filter(item => {
      if (item.keyName === '癌栓密度' && item.value === val) {
        if (tempResults.find(_item => _item.veinNames === item.veinNames && _item.keyName === '癌栓' && _item.value === '有')) {
          return true;
        }
      }
    });
    asmd.push(sub);
  });
  // asmd[0] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '低信号')
  // asmd[1] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '等信号')
  // asmd[2] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '稍高信号')

  let fjStr = jmStrSet(fjArr, 'fjData');
  let sqStr = jmStrSet(sqArr, 'sqData');
  let xsmdStr = jmStrSet(xsmd, 'mdData');
  let asmdStr = jmStrSet(asmd, 'mdData');
  
  let strArr = [];
  fjStr ? strArr.push(...fjStr) : ''
  // sqStr ? strArr.push(sqStr) : ''
  xsmdStr ? strArr.push(...xsmdStr) : ''
  asmdStr ? strArr.push(...asmdStr) : ''
  
  let str = '';
  const output = prioritizeUnclearAndVisible(strArr);
  output && output.length > 0 ? str = output.join('，') : ''
  if(sqStr.length > 0){
    var modifiedStr = str.replace(/尚清/g, '尚清，未见受侵');
    modifiedStr = modifiedStr.replace(/不清/g, '不清，可见受侵');
    str = modifiedStr
  }
  return str;
}
//门静脉、 肝静脉、下腔静脉拼接内容
function jmStrSet(list, type) {
  let keyObj = {
    fjData: { 0: '尚清', 1: '不清' },
    sqData: { 0: '未见', 1: '可见' },
    // mdData: { 0: '可见低信号', 1: '可见等信号', 2: '可见稍高信号' }
  }
  let str = '';
  type === 'fjData' ? str = '与病灶分界' : ''
  type === 'sqData' ? str = '受侵' : ''
  // type === 'mdData' ? str = `充盈缺损影，增强${desc}见强化` : ''
  let arr = [];
  for (let i = 0; i < list.length; i++) {
    if (list[i] && list[i].length > 0) {
      // type === 'fjData' ? arr.push(list[i].map(item => item.veinNames).join('、')+ str + keyObj[type][i]) : '';
      // type === 'sqData' ? arr.push(list[i].map(item => item.veinNames).join('、') + keyObj[type][i] + str) : '';
      // type === 'mdData' ? arr.push(list[i].map(item => item.veinNames).join('、') + '可见' + keyObj[type][i] + str) : '';
      // arr.push(list[i].map(item => item.veinNames).join('、') + (str === '与病灶分界' ? str : '') + keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      if (type === 'mdData') {
        arr.push(list[i].map(item => item.veinNames).join('、') + list[i][0].value)
      } else if(str === '与病灶分界') {
        arr.push(list[i].map(item => item.veinNames).join('、') + (str === '与病灶分界' ? str : '') + keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      }else {
        arr.push( keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      }
    }
  }
  str = ''
  // const output = prioritizeUnclearAndVisible(arr);
  // output && output.length > 0 ? str = output.join('，') : '';
  return arr;
}
function prioritizeUnclearAndVisible(arr) {
  var result = [];      // 存放包含"不清"或"可见"的元素
  var withoutKeywords = []; // 存放不包含关键字的元素
  for (var item of arr) {
      if (item.includes("不清") || item.includes("可见")) {
          result.push(item);    // 符合条件：加到前面组
      } else {
          withoutKeywords.push(item); // 不符合：暂存
      }
  }
  return result.concat(withoutKeywords); // 合并两组
}
//门静脉、 肝静脉、下腔静脉勾选值获取
function isChecked(list, type) {
  let check = false;
  for (let i = 0; i < list.length; i++) {
    if (type === 'sq' && list[i].indexOf('可见') !== -1) {
      check = true;
      i = list.length;
    }
    if (type === 'xs' && list[i][2] === '有') {
      check = true;
      i = list.length;
    }
    if (type === 'as' && list[i][4] === '有') {
      check = true;
      i = list.length;
    }
  }
  return check;
}

function initId(num) {
  return 'mcgxba-rt-' + num
}

function showRadioInfo(type, radioName) {
  if (type === 'init') {
    for (let i = 0; i < radioNameArr.length; i++) {
      let name = radioNameArr[i];
      let val = getVal($(`input[type=radio][name=${name}]:checked`));
      let secondLastChar = name.charAt(name.length - 2).toUpperCase();
      let id = name.slice(0, name.length - 2) + secondLastChar + name.charAt(name.length - 1);
      val && val === '有' ? $('#' + id).css('display', 'inline-block') : $('#' + id).hide();
    }
  } else {
    let secondLastChar = radioName.charAt(radioName.length - 2).toUpperCase();
    let val = $("input[type='radio'][name='" + radioName + "']:checked").val();
    let id = radioName.slice(0, radioName.length - 2) + secondLastChar + radioName.charAt(radioName.length - 1);
    val && val === '有' ? $('#' + id).css('display', 'inline-block') : $('#' + id).hide();
  }

}

function showTextarea(type, id) {
  if (type === 'init') {
    for (let i = 0; i < textareaIdArr.length; i++) {
      let changeId = Number(textareaIdArr[i].slice(-3));
      changeId = changeId + 1
      if ($('#' + textareaIdArr[i]).is(':checked')) {
        $('#mcgxba-rt-' + changeId).show();
        $('#mcgxba-rt-' + changeId).siblings('.red-tip').show();
      } else {
        $('#mcgxba-rt-' + changeId).hide();
        $('#mcgxba-rt-' + changeId).siblings('.red-tip').hide();
      }
    }
  } else {
    let changeId = Number(id.slice(-3));
    changeId = changeId + 1
    if ($('#' + id).is(':checked')) {
      $('#mcgxba-rt-' + changeId).show();
      $('#mcgxba-rt-' + changeId).siblings('.red-tip').show();
    } else {
      $('#mcgxba-rt-' + changeId).hide();
      $('#mcgxba-rt-' + changeId).siblings('.red-tip').hide();
    }
  }
}

function changeBzSmText() {
  let val1 = $('input[type="radio"][name="bzsm"]:checked').val(); // 原发灶
  // $('#yfzwzText').text(val1 === '多发' ? '最大位于：' : '位置：');
  if (val1 === '多发') {
    $('#yfzdxText').text('最大者大小约：');

    $('#bzgsWrap').show();
    $('#mcgxba-rt-636').removeClass('rt-hide');
  } else {
    $('#yfzdxText').text('大小约：');

    $('#bzgsWrap').hide();
    $('#mcgxba-rt-636').addClass('rt-hide');
    $('input[type=radio][name=bzgs]').prop('checked', false);
  }

  let val2 = $('input[type="radio"][name="zzsm"]:checked').val(); // 子灶
  // $('#zzwzText').text(val2 === '多发' ? '最大位于：' : '位置：')
  if (val2 === '多发') {
    $('#zzdxText').text('最大者大小约：')

    $('#zzgsWrap').show();
  } else {
    $('#zzdxText').text('大小约：')

    $('#zzgsWrap').hide();
    $('input[type=radio][name=zzgs]').prop('checked', false);
  }
}

// 切换原发灶个数
function changeYfzgs() {
  var arr = [];
  var sm = getVal($('input[type=radio][name=bzsm]:checked')) || '';
  var wz = getVal($('input[type=checkbox][name=yfzwz]:checked')) || '';
  if (wz) {
    arr = wz.split('、');
  }
  if (sm === '多发' && arr.length >= 2) {
    $('#bzzdwzWrap').show();
    $('#mcgxba-rt-639').removeClass('rt-hide');
  } else {
    $('#bzzdwzWrap').hide();
    $('#mcgxba-rt-639').addClass('rt-hide');
    $('#mcgxba-rt-639').val('');
  }
  arr = arr.map(function(item) { return {title: item, id: item}});
  if ($('#mcgxba-rt-639').attr('dropdown-init')) {
    layui.dropdown.reload('mcgxba-rt-639', {
      data: arr
    });
  } else {
    initInpAndSel('mcgxba-rt-639', arr);
    $('#mcgxba-rt-639').attr('dropdown-init', '1');
  }
}
// 切换子灶个数
function changeZzgs() {
  var arr = [];
  var sm = getVal($('input[type=radio][name=zzsm]:checked')) || '';
  var wz = getVal($('input[type=checkbox][name=zzwz]:checked')) || '';
  if (wz) {
    arr = wz.split('、');
  }
  if (sm === '多发' && arr.length >= 2) {
    $('#zzzdwzWrap').show();
  } else {
    $('#zzzdwzWrap').hide();
    $('#mcgxba-rt-643').val('');
  }
  arr = arr.map(function(item) { return {title: item, id: item}});
  if ($('#mcgxba-rt-643').attr('dropdown-init')) {
    layui.dropdown.reload('mcgxba-rt-643', {
      data: arr
    });
  } else {
    initInpAndSel('mcgxba-rt-643', arr);
    $('#mcgxba-rt-643').attr('dropdown-init', '1');
  }
}

function showGyh() {
  for (let i = 0; i < raidoArr1.length; i++) {
    if ($(`#${raidoArr1[i]}`).is(':checked')) {
      // $('#mcgxba-rt-132').show();
      $('#mcgxba-rt-132').removeClass('rt-hide').show();
      i = raidoArr1.length;
      return
    }
    // $('#mcgxba-rt-132').hide();
    $('#mcgxba-rt-132').addClass('rt-hide').hide();
  }
}
function showZfg() {
  for (let i = 0; i < radioArr3.length; i++) {
    if ($(`#${radioArr3[i]}`).is(':checked')) {
      // $('#mcgxba-rt-623').show();
      $('#mcgxba-rt-623').removeClass('rt-hide').show();
      i = radioArr3.length;
      return
    }
    // $('#mcgxba-rt-623').hide();
    $('#mcgxba-rt-623').addClass('rt-hide').hide();
  }
}
// 动脉期血流灌注异常
function showDmqxlgz() {
  let dmqxlgzVal = $('[name="dmqxlgz"]:checked').val();
  if(dmqxlgzVal && dmqxlgzVal !== '无') {
    $('#mcgxba-rt-593_1').removeClass('rt-hide').show();
  } else {
    $('#mcgxba-rt-593_1').addClass('rt-hide').hide();
  }
}
function initClickFun() {
  for (let j = 0; j < radioNameArr.length; j++) {
    $(`input[type="radio"][name="${radioNameArr[j]}"]`).click(function (e) {
      showRadioInfo('click', $(this).attr('name'));
    })
  }
  $('input[type=radio][name=gztgz]').click(function (e) {
    let val = $('input[type=radio][name=gztgz]:checked');
    val === '有' ? $('#mcgxba-rt-593').show() : $('#mcgxba-rt-593').hide()
  })

  $('.bz-item').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus2[id]) {
      e.preventDefault();
    } else {
      // target.prop('checked', true)
    }
    var block = _this.attr('block-name2');
    var radioButtons = $( `.${block} input[type="radio"]`);
    if (target.is(':checked')) {
      isFillInStatus2[id] = '1';
      radioChed(radioButtons)
      // radioButtons.filter('[value="类圆形"]').prop('checked', true);
      // radioButtons.filter('[value="清楚"]').prop('checked', true);
      // radioButtons.filter('[value="均匀"]').prop('checked', true);
      // radioButtons.filter('[value="无"]').prop('checked', true);
    }else {
      radioButtons.filter('[value="类圆形"]').prop('checked', false);
      radioButtons.filter('[value="清楚"]').prop('checked', false);
      radioButtons.filter('[value="均匀"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('visit-status').siblings().removeClass('visit-status');
    $("." + block).show().siblings('.block2').hide();
    block === 'zz-block' ? $('.boryfz').css('cssText', 'border-bottom: 1px solid #dcdfe6') : $('.boryfz').css('cssText', 'border-bottom: none')
    block === 'yfz-block' ? $('.borzd').css('cssText', 'border-bottom: 1px solid #dcdfe6') : $('.borzd').css('cssText', 'border-bottom: none')
    // $("." + block).css('cssText', 'border-bottom: none').siblings('.block2').css('cssText', 'border-bottom: 1px solid #dcdfe6');
  })
  function radioChed(data) {
    if(!data.filter('[value="类圆形"]').is(':checked') && !data.filter('[value="不规则形"]').is(':checked')){
      data.filter('[value="类圆形"]').prop('checked', true);
    }
    if(!data.filter('[value="清楚"]').is(':checked') && !data.filter('[value="欠清"]').is(':checked') && !data.filter('[value="不清楚"]').is(':checked')){
      data.filter('[value="清楚"]').prop('checked', true);
    }
    if(!data.filter('[value="均匀"]').is(':checked') && !data.filter('[value="不均匀"]').is(':checked')){
      data.filter('[value="均匀"]').prop('checked', true);
    }
    if(!data.filter('[value="无"]').is(':checked') && !data.filter('[value="有"]').is(':checked')){
      data.filter('[value="无"]').prop('checked', true);
    }
  }
  $('.ck-inp2').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus2[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus2[id];
      // $(this).parents().find('.block3').hide();
    }
  })

  $("input[type='radio'][name='bzsm']").click(function (e) {
    changeBzSmText();
    changeYfzgs();
  })
  $("input[type='radio'][name='zzsm']").click(function (e) {
    changeBzSmText();
    changeZzgs();
  })
  $("input[type='radio'][name='dg']").click(function (e) {
    let val = $('input[type="radio"][name="dg"]:checked').val();
    if(val === '可见异常') {
      $('input[type="radio"][name="dgsq"][value="未见"]').prop('checked', true);
      $('input[type="radio"][name="dgkz"][value="未见"]').prop('checked', true);
      $('input[type="radio"][name="dskz"][value="无"]').prop('checked', true);
    }
  })

  // 原发灶位置勾选
  $("input[type='checkbox'][name='yfzwz']").on('change', function (e) {
    changeYfzgs();
  });
  // 子发灶位置勾选
  $("input[type='checkbox'][name='zzwz']").on('change', function (e) {
    changeZzgs();
  });


  var changeRadio2 = {
    yfzpsmd2: '45',
    zzpsmd2: '100',
    dg: '468',
    lbj: '484',
    dn: '517',
    pz: '537',
    yx: '549',
  }
  $("input[type='radio'][name='gyh']").click(function (e) {
    showGyh();
  })
  $("input[type='radio'][name='zfg']").click(function (e) {
    showZfg();
  })
  $("input[type='radio'][name='dmqxlgz']").click(function (e) {
    showDmqxlgz();
  });
  for (let key in changeRadio2) {
    $(`input[type='radio'][name='${key}']`).click(function (e) {
      $(`#mcgxba-rt-${changeRadio2[key]}`).is(':checked') ? $(`#mcgxba-rt-${changeRadio2[key]}-cont`).show() : $(`#mcgxba-rt-${changeRadio2[key]}-cont`).hide();
    })
  }

  for (let i = 0; i < textareaIdArr.length; i++) {
    $('#' + textareaIdArr[i]).click(function (e) {
      showTextarea('click', e.target.id)
    })
  }

  $('.block-ck1').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus1[id]) {
      e.preventDefault();
    }
    var block = _this.attr('block-name1');
    var radioButtons = $( `.${block} input[type="radio"]`);
    
    
    if (target.is(':checked')) {
      isFillInStatus1[id] = '1'; 
      radioBto(radioButtons)
    }else {
      radioButtons.filter('[value="尚清"]').prop('checked', false);
      radioButtons.filter('[value="未见"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    $("." + block).show().siblings('.block1').hide();
  })
  function radioBto(data) {
    if(!data.filter('[value="尚清"]').is(':checked') && !data.filter('[value="不清"]').is(':checked')){
      data.filter('[value="尚清"]').prop('checked', true);
    }
    if(!data.filter('[value="未见"]').is(':checked') && !data.filter('[value="可见"]').is(':checked')){
      data.filter('[value="未见"]').prop('checked', true);
    }
    if(!data.filter('[value="无"]').is(':checked') && !data.filter('[value="有"]').is(':checked')){
      data.filter('[value="无"]').prop('checked', true);
    }
  }

  $('.ck-inp1').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus1[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus1[id];
      if (id === 'mcgxba-rt-149') {
        let list = radioNameArr.slice(0, 2);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-150') {
        delete isFillInStatus3['mcgxba-rt-168'];
        delete isFillInStatus3['mcgxba-rt-169'];
        delete isFillInStatus3['mcgxba-rt-170'];
        delete isFillInStatus3['mcgxba-rt-171'];
        $('#zzhbXs').hide();
        $('#zzhbAs').hide();
        $('#zzhbXs').hide();
        $('#zzhbAs').hide();
        let list = radioNameArr.slice(2, 10);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-151') {
        delete isFillInStatus3['mcgxba-rt-228'];
        delete isFillInStatus3['mcgxba-rt-229'];
        delete isFillInStatus3['mcgxba-rt-230'];
        let list = radioNameArr.slice(10, 16);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-152') {
        let list = radioNameArr.slice(16, 18);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-152') {
        let list = radioNameArr.slice(18, 20);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-301') {
        let list = radioNameArr.slice(20, 22);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-302') {
        let list = radioNameArr.slice(22, 24);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-303') {
        let list = radioNameArr.slice(24, 26);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-304') {
        delete isFillInStatus3['mcgxba-rt-348'];
        delete isFillInStatus3['mcgxba-rt-349'];
        delete isFillInStatus3['mcgxba-rt-350'];
        let list = radioNameArr.slice(26, 32);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-305') {
        let list = radioNameArr.slice(32, 34);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-407') {
        let list = radioNameArr.slice(34, 36);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-408') {
        let list = radioNameArr.slice(36, 38);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-409') {
        let list = radioNameArr.slice(38, 40);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-410') {
        let list = radioNameArr.slice(40, 42);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
    }
  })

  $('.block-ck3').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus3[id]) {
      e.preventDefault();
    }
    var block = _this.attr('block-name3');
    var radioButtons = $( `.${block} input[type="radio"]`);
    if (target.is(':checked')) {
      isFillInStatus3[id] = '1';
      radioBto(radioButtons)
    }else {
      radioButtons.filter('[value="尚清"]').prop('checked', false);
      radioButtons.filter('[value="未见"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    $("." + block).show().siblings('.block3').hide();
  })

  $('.ck-inp3').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus3[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus3[id];
      // $(this).parents().find('.block3').hide();
      if (id === 'mcgxba-rt-168') {
        let list = radioNameArr.slice(2, 4);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-169') {
        let list = radioNameArr.slice(4, 6);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-170') {
        let list = radioNameArr.slice(6, 8);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-171') {
        let list = radioNameArr.slice(8, 10);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-228') {
        let list = radioNameArr.slice(10, 12);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-229') {
        let list = radioNameArr.slice(12, 14);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-230') {
        let list = radioNameArr.slice(14, 16);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-348') {
        let list = radioNameArr.slice(26, 28);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-349') {
        let list = radioNameArr.slice(28, 30);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'mcgxba-rt-350') {
        let list = radioNameArr.slice(30, 32);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
    }
  })
}

function hideRadioInfo(radioName) {
  let secondLastChar = radioName.charAt(radioName.length - 2).toUpperCase();
  let id = radioName.slice(0, radioName.length - 2) + secondLastChar + radioName.charAt(radioName.length - 1);
  $('#' + id).hide();
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = descriptionStr;
  rtStructure.impression = curElem.find('#mcgxba-rt-587').val();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}