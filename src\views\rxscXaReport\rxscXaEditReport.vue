<template>
  <div class="rxscRmEditReport main-page">
   <!-- v-if="!patientInfo.sex.includes('男')" -->
    <div class="dis-rp-wrap rxscRm-wrap">
      <!-- 编辑页 -->
      <defaultPage 
        key="defaultPage"
        :resultFormData="resultFormData"
        :patientInfo="patientInfo"
        :showHistoyByHalf="false"
        :newestCheckResult="newestCheckResult"
        :getDataFinished="getDataFinished"
        :docId="docId"
        :htmlUrl="htmlUrl"
        :resImpress="resImpress"
        @htmlLoaded="defaultPageGetData"
        domId="defaultPage"
        ref="rxscFjrmPage"
      >
      </defaultPage>
    </div>
  </div>
</template>

<script>
import '@/assets/css/diseaseReport.scss'
import RxscXaPage from './rxscXaPage.vue';
import diseaseReportMixin from '@/mixins/newDiseaseReport.js'
import '../../../public/template-lib/utils/structFun.js'
import $ from 'jquery'
export default {
  name: "rxscRmEditReport",
  components: {
    defaultPage: RxscXaPage,
  },
  mixins: [diseaseReportMixin],
  data() {
    return {
      prefixName: "rxscRm-",
      patientInfo: {
        sex: ''
      },
      resultFormData: [],
      getDataFinished: false,
      newestCheckResult: {},
      docId: '',   //文档id
      htmlUrl: process.env.BASE_URL + 'diseaseFileStore/html/rxscXaTemplate.html',
      resImpress: ''
    }
  },
  watch: {
    $route() {
      window.location.reload();
    },
  },
  mounted() {
    this.pageInit();
  },
  methods: {
    async pageInit() {
      await this.getPatientInfo();
    },

    async defaultPageGetData(html) {
      this.resultFormData = await this.getData();
      let docContent = this.allContent.docContent || {};
      this.resImpress = docContent.impression || '';
      // 动态加载病灶rxscRm-0003作为标识
			let cnotent = $('#defaultPage #bz-tabs').children()[0].innerHTML;
			window.rxbzTemplate = '<el-tab-pane class="rt-pane" name="rxscRm-0003.001">' + cnotent + '</el-tab-pane>';
			$($('#defaultPage #bz-tabs').children()[0]).remove();
      if(this.resultFormData && this.resultFormData.length) {
        $("#defaultPage .t-pg").attr('hasEdited', true);
        let bzData = this.resultFormData.filter(item => item.id && item.id.startsWith('rxscRm-0003'));
        if(bzData && bzData.length) {
          this.loadBzPanel(bzData, window.rxbzTemplate);
        }
      }
      let params = { examNo: this.patientInfo.examNo, fileType: 'RptImg' };
      console.log('this.isFromYunPacs',this.isFromYunPacs);
      await this.getReportDoc();
      this.$refs.rxscFjrmPage.getEntryPeopleList();
      await this.$store.dispatch('report/getReportImageList', { params, vm: this });
      setTimeout(async () => {
        this.getDataFinished = true;
      }, 200)
    },
    loadBzPanel(bzData, html) { 
      let bzList = bzData[0].child || [];
      let panelHtml = '';
      bzList.forEach((bzItem, i) => {
        let flagId = bzItem.id.split('.')[0];
        let tempHtml= html.replace(/rxscRm-0003\./g, flagId + '.');
        tempHtml= tempHtml.replace(/RG-0003\./g, flagId + '.');
        tempHtml= tempHtml.replace(/data-pre="rxscRm-panel"/g, 'data-pre="'+flagId+'"');
        panelHtml += tempHtml;
      });
      $('#defaultPage #bz-area').show();
      $('#defaultPage #bz-area').attr('data-bzlen', bzList.length);
      $('#defaultPage #bz-tabs').prepend(panelHtml);
    }
  }
}
</script>

<style lang="scss"></style>