$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; // 报告存储的节点集合
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#fjsgact1"),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || []) : [];
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};

    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewPageCon();
      showTableVal('edit');
    } else {
      pageInit();
      // 兼容以前旧模板回显问题
      if(!$('#fjsgact-rt-5').hasClass('row-tit')){
        $('#fjsgact-rt-5').html('病变位置：')
      } else {
        $('#fjsgact-rt-5').html(`<div class="wd-112">病变位置：</div>
        <div class="wd-112">（肿瘤中心位于）</div>`);
      }
    }
    // 病变位置下一级显示隐藏
    toggleBbwzBlock();
  }
}

// 初始化页面
function pageInit() {
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);
  initFbFun();
  if(!resultData.length) {
    addRow();
  }else {
    showTableVal('edit');
  }
  // 获取推导结果
  curElem.find('#fjsgact1 .rpt-con .rt-sr-w').change(function() {
    getImpressionText();
  })
}

// 病变位置显示隐藏交互
function toggleBbwzBlock() {
  $('.bbwz-block .box-item input[type="checkbox"]').change(function() {
    if($(this).is(':checked')) {
      $(this).closest('.box-item').next('.sub-box').show();
    } else {
      $(this).closest('.box-item').next('.sub-box').hide();
    }
  })
  $('.bbwz-block .box-item input[type="checkbox"]').each(function() {
    if($(this).is(':checked')) {
      $(this).closest('.box-item').next('.sub-box').show();
    } else {
      $(this).closest('.box-item').next('.sub-box').hide();
    }
  })
}

// 肺部添加事件
function initFbFun() {
  $('.fb-block [type="checkbox"]').change(function() {
    let attrName = $(this).attr('name');
    let isShow = null;
    if($(this).is(':checked')) {
      isShow = 'block';
    }else {
      isShow = 'none';
    }
    $('.'+attrName+'-inp').css('display',isShow);
  })
}

// 回显颈部区域淋巴结
function showTableVal(pageType, data) {
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  let resultDataLen = resultData.length;
  let jbqyData = resultData.filter(item => item.id === 'fjsgact-rt-359');
  let childData = jbqyData.length ? jbqyData[0].child : [];
  let flagId = '';
  if(data) {
    childData = data;
  }
  if(childData) {
    childData.map(function(item) {
      let childArr = item.child || [];
      childArr.map(function(cItem) {
        flagId = cItem.id ? cItem.id.split('-')[3] : '';
      });
      flagId ? addRow(flagId,pageType,childArr) : '';
    })
  }
}

// 颈部区域淋巴结-添加事件
function addRow(flagId,pageType,childArr) {
  let cloneRow = $('#fjsgact1 .cloneRow:first').clone(true);
  let notHiddenInp = cloneRow.find('input:not([type="hidden"])');
  let hiddenInp = cloneRow.find('input[type="hidden"]'); // 父节点，隐藏
  let radioCheckboxList = cloneRow.find('label');
  let inpSortArr = ['01','02','03','04','05','06','07','08'];
  let randomNum = createUUidFun();

  if(flagId && typeof flagId === 'string') {
    pid = 'fjsgact-rt-00' + '-' + flagId;
  }else {
    pid = 'fjsgact-rt-00' + '-' + randomNum;
  }

  // 父节点添加属性
  rtStructure.idAndDomMap[pid] = {
    id: pid,
    desc: '行',
    name: '行',
    pid: 'fjsgact-rt-359',
    pvf: '',
    value: '1',
    wt: '',
    vt: '',
  }
  $(hiddenInp).attr('id',pid);
  $(hiddenInp).attr('pid','fjsgact-rt-359');
  $(hiddenInp).addClass('rt-sr-w');

  // 新增与编辑
  notHiddenInp.each(function(i,dom){
    let ele = $(dom);
    let attrType = $(dom).attr('type');
    let inpName = ele.attr('inp-name');
    let cueEleId = ele.attr('id');
    let id = '',radCkId = '';
    if(flagId && pageType === 'edit') {
      id = 'fjsgact-rt-' + inpSortArr[i]  + '-' + flagId;
      radCkId = flagId;
      childArr.map(function(cItem) {
        let value = idAndDomMap[cItem.id].value || '';
        if(cItem.id.includes(id)) {
          if(attrType === 'text') {
            ele.val(value);
          }else {
            ele.attr('checked',true);
          }
        }
      })
    }else {
      id = 'fjsgact-rt-' + inpSortArr[i]  + '-' + randomNum;
      radCkId = randomNum;
    }

    // 修改radio与checkbox的for属性
    let wt = 1; // 文本1，单选框5，复选框4
    if(attrType === 'radio' || attrType === 'checkbox') {
      let radCkName = attrType === 'radio' ? 'jblbj-jbfx' : 'jblbj-lev';
      ele.attr('name',radCkName + '-' + radCkId);
      radioCheckboxList.each(function(radCkIdx,radCkDom){
        let attrFor = $(radCkDom).attr('for');
        if(cueEleId === attrFor) {
          $(radCkDom).attr('for',id);
        }
      })
      wt = attrType === 'radio' ? 5 : 4;
    }
    rtStructure.idAndDomMap[id] = {
      id: id,
      desc: inpName,
      name: inpName,
      pid: pid,
      pvf: '',
      value: '',
      wt: wt,
      vt: '',
    }
    ele.addClass('rt-sr-w');
    ele.attr('id',id);
    ele.attr('pid',pid);
  })
  cloneRow.css('display','block');
  $('.jbqylbj-block .cloneRow:last').after(cloneRow);
}

// 颈部区域淋巴结-删除事件
function removeRow(item) {
  let flag = confirm('确认删除吗？');
  if(flag) {
    $(item).closest("div").remove();
  }
}

// 推导肿瘤分期
function getSgaLevel() {
  var T = '', N = 'N0', M = 'M0', levelStr = '';
  // T分期（肿瘤分期）
  var zlfqTVal = getVal('[name="zlfq-t"]:checked');
  var zlfqTMap = {
    '未侵及纤维膜': 'T1-2',
    '侵犯食管深肌层外纤维、脂肪组织': 'T3',
    't4a侵犯': 'T4a',
    't4b侵犯': 'T4b'
  }
  for(let key in zlfqTMap) {
    if(key === zlfqTVal) {
      T = zlfqTMap[key];
    }
  }

  // N分期（淋巴结）
  let numInpList = $('[tit-name="numInp"]:not([id="fjsgact-rt-05.1234"]');
  let allNumInpVal = null;
  numInpList.each(function(i,dom) {
    let id = $(dom).attr('id');
    let numInpVal = getVal(`[id=${id}]`);
    if(!isNaN(Number(numInpVal))) {
      allNumInpVal += Number(numInpVal);
    }
  })
  if(allNumInpVal >= 7) {
    N = 'N3';
  }else if(3 <= allNumInpVal && allNumInpVal <=6) {
    N = 'N2';
  }else if(1 <= allNumInpVal && allNumInpVal <=2) {
    N = 'N1';
  }

  // M分期（远处转移）
  var yczyVal = getVal('[name="yczy"]:checked');
  if(yczyVal.includes('其他转移') || yczyVal.split('、').length > 1) {
    M = 'M1b';
  }else if(yczyVal === '肝脏' || yczyVal === '肺部' || yczyVal === '骨转移') {
    M = 'M1a';
  }

  levelStr = `影像分期：ct${T}${N}${M}`;
  return levelStr;
}

// 获取描述——病变位置、大小测量、病变环绕管周径、病变所在方位、信号特点及强化描述、病灶处管腔狭窄
function getBbwzCon() {
  var strArr = [], sgaBbwzArr = [], bzInfoArr = [], qhtdArr = [], gqxzArr = [];
  // 病变位置
  let checkboxNames = ['jdsg', 'xsdsg', 'xzdsg', 'xxdsg'];
  // 肿瘤上缘对应
  let syChildIds = ['fjsgact-rt-10', 'fjsgact-rt-13', '-2']
  checkboxNames.forEach(name => {
    var oneCheckbox = [];  //一条的数据
    var checkboxVal = getVal(`[name="${name}"]:checked`);
    if(checkboxVal) {
      oneCheckbox.push(checkboxVal.split('：')[0] || '');
      if(name === 'jdsg') {
        var sqjlVal = getVal('[name="sqjl"]:checked','，');
        sqjlVal && oneCheckbox.push(sqjlVal);
      }
      var pid = $(`[name="${name}"]`).attr('id');
      for(var i = 0; i < syChildIds.length; i++) {
        // 兼容旧数据
        var childId = syChildIds[i].includes('fjsgact') ? syChildIds[i] : `${pid}${syChildIds[i]}`;
        if($(`[id="${childId}"]`).siblings().attr('pid') === pid) {
          var syVal = getVal(`[id="${childId}"]`);
          syVal && oneCheckbox.push('肿瘤上缘对应' + syVal + '椎体平面水平');
          break;
        }
      }
      // 肿瘤下缘对应 
      var xsdsyVal = getVal(`[id="${pid}-4"]`);
      xsdsyVal && oneCheckbox.push('肿瘤下缘对应' + xsdsyVal + '椎体平面水平');
      if(oneCheckbox.length) {
        sgaBbwzArr.push(oneCheckbox.join('，'));
      }
    }
  })
  sgaBbwzArr.length && strArr.push('病灶位于' + sgaBbwzArr.join('，'));

  // 大小测量
  var inpArr = [];
  var zldxclVal = getVal('[name="zldxcl"]:checked');
  zldxclVal && bzInfoArr.push('病灶为' + zldxclVal);
  // 肿块输入框
  var zkHdmInp1 = getVal('[id="fjsgact-rt-20"]');
  var zkHdmInp2 = getVal('[id="fjsgact-rt-21"]');
  zkHdmInp1 && inpArr.push(zkHdmInp1 + 'mm');
  zkHdmInp2 && inpArr.push(zkHdmInp2 + 'mm');
  inpArr.length && bzInfoArr.push('横断面测量：' + inpArr.join('x'));
  var zkSzmInp = getVal('[id="fjsgact-rt-23"]');
  zkSzmInp && bzInfoArr.push('矢状面测量（纵径）：' + zkSzmInp + 'mm');
  // 管壁浸润型输入框
  var gbHdmInp = getVal('[id="fjsgact-rt-25"]');
  var gbSzmInp = getVal('[id="fjsgact-rt-27"]');
  gbHdmInp && bzInfoArr.push('横断面测量管壁最厚：' + gbHdmInp + 'mm');
  gbSzmInp && bzInfoArr.push('矢状面测量（纵径）：' + gbSzmInp + 'mm');

  // 病变环绕管周径
  var bbhrgzjVal = getVal('[name="bbhrgzj"]:checked');
  bbhrgzjVal && bzInfoArr.push('累及' + bbhrgzjVal + '管壁');

  // 病变所在方位
  var bbszfwVal = getVal('[name="bbszfw"]:checked');
  bbszfwVal && bzInfoArr.push('所在方位' + bbszfwVal);
  bzInfoArr.length ? strArr.push(bzInfoArr.join('，')) : '';

  // 强化特点
  var qhtdVal = getVal('[name="qhtd"]:checked')
  qhtdVal ? strArr.push(qhtdVal) : '';

  // 病灶处管腔狭窄
  var bzcgqxzVal = getVal('[name="bzcgqxz"]:checked');
  if(bzcgqxzVal === '无') {
    gqxzArr.push('病灶处管腔未见狭窄');
  }else if(bzcgqxzVal === '有') {
    var ygqxzVal = getVal('[name="jfjdyssg"]:checked','、');
    ygqxzVal && gqxzArr.push('病灶处管腔狭窄，继发近端以上食管' + ygqxzVal);
  }
  gqxzArr.length ? strArr.push(gqxzArr.join('，')) : '';

  return strArr.length ? '　　' + strArr.join('。') : '';
}

// 获取描述——T分期
function getTfqCon() {
  var strArr = [];
  var zlfqTVal = getVal('[name="zlfq-t"]:checked');
  var zlfqTMap = {
    '未侵及纤维膜': 'T1-2',
    '侵犯食管深肌层外纤维、脂肪组织': 'T3',
    't4a侵犯': 'T4a',
    't4b侵犯': 'T4b'
  }
  for(let key in zlfqTMap) {
    if(key === zlfqTVal) {
      if(zlfqTVal === 't4a侵犯') {
        var t4aVal = getVal('[name="t4a-qf"]:checked');
        t4aVal && strArr.push(zlfqTMap[key] + '期：病灶侵犯' + t4aVal);
      }else if(zlfqTVal === 't4b侵犯') {
        var t4bVal = getVal('[name="t4b-qf"]:checked');
        t4bVal && strArr.push(zlfqTMap[key] + '期：病灶侵犯' + t4bVal);
      }else {
        strArr.push(zlfqTMap[key] + '期：病灶' + zlfqTVal);
      }
    }
  }
  return strArr.length ? '　　' + strArr.join('。') : '';
}

// 不同分组医生只输入一个最大淋巴结大小，不同分组需要分开描述
/***
 * className 类名
 * type 区分描述、印象， desc 描述；imp 印象
*/
function getLbjCon(className,type) {
  let lbjRowList = $(`#fjsgact1 .${className}:not(.cloneRow:first-child)`);
  let lbjRowInfoList = [];
  let lbjGroupNameMap = {
    'zglbj-row': '纵膈淋巴结',
    'fqfmhlbj-row': '腹腔腹膜后淋巴结',
    'jbsglbj-row': '颈部、锁骨区淋巴结',
    'jbqylbj-row': '颈部区域淋巴结',
    'qtqylbj-row': '其他',
  };
  // 组合各个淋巴结的数量/最大短径/ser/image值
  lbjRowList.each(function(i,dom) {
    let checkboxList = $(dom).find('[type="checkbox"]:checked'); // 选中的淋巴结复选框
    let allInpList = $(dom).find('[type="text"]'); // 数量/最大短径/ser/image输入框
    let radioList = $(dom).find('[type="radio"]:checked'); // 选中的颈部淋巴结单选框
    checkboxList.each(function(ckIdx,ckDom) {
      let ckVal = $(ckDom).val();
      let ckId = $(ckDom).attr('id');
      let inpTitleName = '';// 输入框标题名称
      let inpVal = ''; // 输入框值
      let lbjRowConObj = {}; // 当前淋巴结信息
      let fillInAllFlag = true; //判断数量输入框是否全部填写，全部填写true,否则false
      allInpList.each(function(inpIdx,inpDom) {
        let titNameMap = {
          'numInp': 'num',
          'djInp': 'maxDj',
          'serInp': 'ser',
          'imageInp': 'image',
        }
        let titNameAttr = $(inpDom).attr('tit-name');
        inpTitleName = titNameMap[titNameAttr];
        inpVal = $(inpDom).val();
        if(className === 'jbqylbj-row') {
          let inpId = $(inpDom).attr('id');
          let ckIdArr = ckId.split('-');
          let inpIdArr = inpId.split('-');
          if(ckIdArr[3] === inpIdArr[3]) {
            inpVal = $(inpDom).val();
          }else {
            return;
          }
        }
        if(titNameAttr === 'numInp' && !inpVal) {
          fillInAllFlag = false;
        }
        lbjRowConObj[inpTitleName] = inpVal;
      })
      // 特殊处理复选框文本
      let xdsgpArr = ['上','中','下']; // 胸段食管旁
      let jbqyArr = ['VI区','VII区']; // VI区、VII区
      if(xdsgpArr.includes(ckVal)) {
        ckVal = ckVal + '胸段食管旁';
      }else if(jbqyArr.includes(ckVal)) {
        // 颈部区域淋巴结
        radioList.each(function(radIdx,radDom) {
          let radId = $(radDom).attr('id');
          let ckIdArr = ckId.split('-');
          let radIdArr = radId.split('-');
          let radVal = $(radDom).val();
          if(ckIdArr[3] === radIdArr[3]) {
            ckVal = radVal + '颈部' + ckVal;
          }
        })
      }else if(ckVal === '其他') {
        let qtInpVal = $('#fjsgact-rt-339').val();
        ckVal = qtInpVal ? qtInpVal : ckVal;
      } else if (['左', '右'].includes(ckVal)) {
        ckVal = `${ckVal}肺门区`
      }
      lbjRowInfoList.push({ groupName: lbjGroupNameMap[className], name:ckVal, ...lbjRowConObj,fillInAllFlag });
    })
  })
  // 查找最大淋巴结的信息
  let checkedLbjArr = []; // 所选中的淋巴结
  let maxLbjObj = {}; // 最大淋巴结
  let maxNum = 0;
  for(var idx=0; idx < lbjRowInfoList.length; idx++) {
    let lbjObj = lbjRowInfoList[idx];
    let inpVal = lbjObj['maxDj'];
    let subLbjName = lbjObj.name;
    // 如果两个区域的膈肌淋巴结都勾选了，就推导膈肌淋巴结转移
    // 如果只有单个勾选，如纵膈淋巴结下的膈肌淋巴结，则推导纵膈（膈肌淋巴结）转移，反之就是腹腔（膈肌淋巴结）转移
    if(lbjObj.name === '膈肌淋巴结') {
      if(type === 'imp') {
        if($('#fjsgact-rt-245').is(':checked') && $('#fjsgact-rt-254').is(':checked')){
          subLbjName = lbjObj.name;
        }else if(lbjObj.groupName === '纵膈淋巴结') {
          subLbjName = '纵膈（膈肌淋巴结）';
        }else if(lbjObj.groupName === '腹腔腹膜后淋巴结') {
          subLbjName = '腹腔（膈肌淋巴结）';
        }
      }else if(type === 'desc') {
        if(lbjObj.groupName === '纵膈淋巴结') {
          subLbjName = '纵膈（膈肌淋巴结）';
        }else if(lbjObj.groupName === '腹腔腹膜后淋巴结') {
          subLbjName = '腹腔（膈肌淋巴结）';
        }
      }
      
    }
    checkedLbjArr.push(subLbjName);
    if(Number(inpVal) && !isNaN(Number(inpVal))) {
      let transformInpVal = Number(inpVal);
      if(transformInpVal > maxNum) {
        maxNum = transformInpVal;
        maxLbjObj = lbjObj;
      }
    }
  }
  // 如果同时包含 左、右肺门区 替换为 双肺门区
  const lungLIndex = checkedLbjArr.findIndex(item => item === '左肺门区');
  const lungRIndex = checkedLbjArr.findIndex(item => item === '右肺门区');
  if (lungLIndex !== -1 && lungRIndex !== -1) {
    const indexL = checkedLbjArr.indexOf('左肺门区');
    checkedLbjArr[indexL] = '双肺门区';
    const indexR = checkedLbjArr.indexOf('右肺门区');
    checkedLbjArr.splice(indexR, 1);
    
    // 合并枚数，取左肺门区的详细信息
    const lungLItem = lbjRowInfoList[lungLIndex];
    const lungRItem = lbjRowInfoList[lungRIndex];
    if (lungLItem && lungRItem) {
      const totalNum = (parseInt(lungLItem.num) || 0) + (parseInt(lungRItem.num) || 0);
      lungLItem.num = totalNum.toString();
      // 保持使用左肺门区的详细信息（最大短径、Ser、Image）
    }
    
    // 从lbjRowInfoList中移除右肺门区项
    if (lungLIndex > lungRIndex) {
      lbjRowInfoList.splice(lungRIndex, 1);
      lbjRowInfoList.splice(lungLIndex - 1, 1, lungLItem);
    } else {
      lbjRowInfoList.splice(lungLIndex, 1, lungLItem);
      lbjRowInfoList.splice(lungRIndex, 1);
    }
    lbjRowInfoList.forEach(item => {
      item.name.includes('肺门区') ? (item.name = '双肺门区') : '';
    })
  }
  return { checkedLbjArr, maxLbjObj, lbjRowInfoList };
}

// 去重膈肌淋巴结
function filterGjlbj(arr) {
  if(!arr || !arr.length) return [];
  let filterArr = [];
  for (var i = 0; i < arr.length; i++) {
    if (filterArr.indexOf(arr[i]) === -1) {
      filterArr.push(arr[i]);
    }
  }
  return filterArr;
}

// 修改N分期的膈肌淋巴结名称
function updateLbjName(item) {
  let { name = '', groupName = '' } = item;
  let newName = '';
  if(name !== '膈肌淋巴结') {
    newName = item.name;
  }else if(groupName === '纵膈淋巴结') {
    newName = '纵膈（膈肌淋巴结）';
  }else if(groupName === '腹腔腹膜后淋巴结') {
    newName = '腹腔（膈肌淋巴结）';
  }
  return newName;
}

/**
 * 同个分组，勾选多个，填的数量和勾选数量一致的推导逻辑
 * N（淋巴结）：6枚纵膈淋巴结转移，分别位于右上气管旁(2R)，2枚，最大短径22mm(Ser:2,Image22)，左下气管旁(4L），2枚，最大短径：16mm(Ser:2,Image12)，主动脉旁，2枚，最大短径：28mm(Ser:2,Image32)；
 * 
 * 是同个分组，勾选多个，只填1个的推导逻辑，这边缺了个枚数
 * 同个分组，勾选多个，如勾选3个，填的数量＜3个，则推导逻辑如下：
 * N（淋巴结）：右上气管旁(2R)，左下气管旁(4L)主动脉旁，右上气管旁(2R)可疑淋巴结转移，右上气管旁(2R)，2枚，最大短径22mm(Ser:2,Image22)，最大者位于主动脉旁，2枚，最大短径：28mm(Ser:2,Image32)；
 */
// 获取描述——N分期
function getNfqCon() {
  var strArr = [],oneGroupLbjList = [];
  let lbjClassNameArr = ['zglbj-row','fqfmhlbj-row','jbsglbj-row','jbqylbj-row','qtqylbj-row'];
  for(let className of lbjClassNameArr) {
    let { checkedLbjArr, maxLbjObj, lbjRowInfoList } = getLbjCon(className,'desc');
    let lbjStrArr = [],allNumLbjStr = '';
    lbjRowInfoList.length && oneGroupLbjList.push(...lbjRowInfoList); // 储存一个分组淋巴结
    let noFillInAllList = lbjRowInfoList.filter(item => !item.fillInAllFlag); // 过滤未全部填写的项
    if(noFillInAllList.length) {
      // 同个分组，勾选多个，勾选的数量 ＜ 数量输入框填的数量
      checkedLbjArr.length ? lbjStrArr.push(checkedLbjArr.join('、')  + '可疑淋巴结转移'): '';
      lbjRowInfoList.map(item => {
        let { name='', num='' } = item;
        let kyLbjStr = '', maxDjSerStr = '', numStr = '';
        let newName = updateLbjName(item);
        if(maxLbjObj.name === name && JSON.stringify(maxLbjObj) !== '{}') {
          // 最大者描述
          numStr = maxLbjObj['num'] ? '，' + maxLbjObj['num'] + '枚' : '';
          maxDjSerStr = getMaxDjSerStr(maxLbjObj);
          kyLbjStr = (numStr || maxDjSerStr) ? '最大者位于' + newName + numStr : '';
        }else {
          numStr = num ? '，' + num + '枚' : '';
          maxDjSerStr = getMaxDjSerStr(item);
          kyLbjStr = (numStr || maxDjSerStr) ? newName + numStr : '';
        }
        kyLbjStr && lbjStrArr.push(kyLbjStr);
        maxDjSerStr && lbjStrArr.push(maxDjSerStr);
      })
      lbjStrArr.length ? strArr.push(lbjStrArr.join('，')) : '';
    }else {
      // 同个分组，勾选多个，勾选数量和填的数量一致
      let allNum = 0,lbjGroupName = '';
      lbjRowInfoList.map((item,index) => {
        let newName = updateLbjName(item);
        let { groupName= '', num='' } = item;
        let kyLbjStr = '', maxDjSerStr = '';
        kyLbjStr = newName + '，' + num + '枚';

        // 每个分组只勾选一个
        if(checkedLbjArr.length === 1) {
          kyLbjStr = newName;
        }

        maxDjSerStr = getMaxDjSerStr(item);
        kyLbjStr && lbjStrArr.push(kyLbjStr);
        maxDjSerStr && lbjStrArr.push(maxDjSerStr);
        lbjGroupName = groupName;
        allNum += Number(num);
        if(index === lbjRowInfoList.length - 1) {
          let fbStr = ''; // 不同的组里面，只有勾选了一个，不要写【分别】
          if(checkedLbjArr.length > 1) {
            fbStr = '分别';
          }
          allNumLbjStr = allNum ? allNum + '枚' + lbjGroupName + '转移' + '，'+ fbStr + '位于' : '';
        }
      })
      lbjStrArr.length ? strArr.push(allNumLbjStr + lbjStrArr.join('，')) : '';
    }
  }
  // 当只有一个淋巴结分组时候
  if(oneGroupLbjList.length === 1 && strArr.length === 1) {
    let obj = oneGroupLbjList[0], oneLbjArr = [];
    strArr = [];
    let newName = updateLbjName(obj);
    obj['num'] && oneLbjArr.push(obj['num'] + '枚可疑淋巴结转移');
    newName && oneLbjArr.push('位于' + newName);
    let  maxDjSerStr = getMaxDjSerStr(obj);
    maxDjSerStr && oneLbjArr.push(maxDjSerStr);
    oneLbjArr.length ? strArr.push(oneLbjArr.join('，')) : '';
  }
  const result = strArr.length ? '　　' + 'N（淋巴结）：' + strArr.join('；') : '';
  console.log(result);
  return result;
}

// 获取最大短径、层面信息
function getMaxDjSerStr(maxLbjObj) {
  let maxDjStr = maxLbjObj['maxDj'] && '最大短径：' + maxLbjObj['maxDj'] + 'mm';
  let serStr = maxLbjObj['ser'] && 'Ser：' + maxLbjObj['ser'];
  let imageStr = maxLbjObj['image'] && 'Image：' + maxLbjObj['image'];
  let isNeedDot =  imageStr ? '，' : ''; // 是否需要逗号
  let serOrImage = (serStr || imageStr) ? '（' +   serStr + isNeedDot + imageStr + '）' : '';
  let maxDjSerStr = (maxDjStr || serOrImage) ? maxDjStr + serOrImage : '';
  return maxDjSerStr;
}

// 获取描述——远处转移
function getYczyCon(type) {
  var strArr = [];
  var yczyVal = getVal('[name="yczy"]:checked');
  var yczyArr = yczyVal.split('、') || [];
  var yczyInpMap = {
    '肝脏': 'fjsgact-rt-130',
    '肺部': 'fjsgact-rt-135',
    '骨转移': 'fjsgact-rt-137',
    '其他转移': 'fjsgact-rt-139',
  }
  for(var i=0;i<yczyArr.length;i++) {
    var item = yczyArr[i];
    var inpVal = getVal(`[id=${yczyInpMap[item]}]`);
    var str = '';
    if(item === '肺部') {
      var fbSiteVal = getVal('[name="fb-site"]:checked');
      if(fbSiteVal) {
        str = fbSiteVal;
      }else {
        str = item;
      }
      str += inpVal &&  '（' + inpVal + '）';
    }else if(item === '其他转移') {
      str = inpVal && inpVal;
    }else {
      str = item;
      str += inpVal &&  '（' + inpVal + '）';
    }
    str && strArr.push(str);
  }
  if(type === 'view') {
    return strArr.length ? strArr.join('；\n') : ''; 
  }else {
    return strArr.length ? '　　' + '远处转移：' + strArr.join('，') : '';
  }
}

// 获取描述——其他征象
function getQtzxCon() {
  var strArr = [];
  var sglVal = getVal('[id="fjsgact-rt-142"]:checked');
  var sglInpVal = getVal('[id="fjsgact-rt-144"]');
  sglVal && strArr.push('食管瘘' + (sglInpVal ? ('（' + sglInpVal + '）') : ''));
  var sglgfxVal = getVal('[id="fjsgact-rt-146"]:checked');
  var sglgfxInpVal = getVal('[id="fjsgact-rt-148"]');
  sglgfxVal && strArr.push('食管瘘高风险' + (sglgfxInpVal ? ('（' + sglgfxInpVal + '）') : ''));
  var qtzxInpVal = getVal('[id="fjsgact-rt-150"]');
  qtzxInpVal && strArr.push(qtzxInpVal);
  return '　　' + '其他征象：' + (strArr.length ? '可见' + strArr.join('，') : '无');
}

// 获取描述——肺部
function getFbCon() {
  let strArr = [];
  $('.fb-block [type="checkbox"]:checked').each(function(i,dom) {
    let ckVal = $(dom).val();
    let attrName = $(dom).attr('name')
    let inpVal = $('.'+attrName+'-inp').val();
    let inpStr = inpVal ? '，'+inpVal : '';
    strArr.push(ckVal + inpStr);
  })
  return strArr.length ? '　　' + '肺部：' + strArr.join('；') : '';
}

// 获取描述
function getDescContent() {
  var descArr = [];
  // 第一段
  var bbwzCon = getBbwzCon();
  bbwzCon && descArr.push(bbwzCon);

  // 第二段
  var tfqCon = getTfqCon();
  tfqCon && descArr.push(tfqCon);

  // 第三段
  var nfqCon = getNfqCon();
  nfqCon && descArr.push(nfqCon);

  // 第四段
  var yczyCon = getYczyCon();
  yczyCon && descArr.push(yczyCon);

  // 第五段
  var qtzxCon = getQtzxCon();
  qtzxCon && descArr.push(qtzxCon);

  // 第六段
  var fbCon = getFbCon();
  fbCon && descArr.push(fbCon);

  // console.log(descArr.join('。\n'));
  return descArr.length ? descArr.join('。\n') : '';
}

// 获取印象——病变位置
function getBbwzImp() {
  var strArr = [], sgaBbwzArr = [], tfqArr = [];
  // 颈段
  var jdsgVal = getVal('[name="jdsg"]:checked');
  jdsgVal && sgaBbwzArr.push('颈段');
  // 胸上段
  var jdsgVal = getVal('[name="xsdsg"]:checked');
  jdsgVal && sgaBbwzArr.push('胸上段');
  // 胸中段
  var jdsgVal = getVal('[name="xzdsg"]:checked');
  jdsgVal && sgaBbwzArr.push('胸中段');
  // 胸下段
  var jdsgVal = getVal('[name="xxdsg"]:checked');
  jdsgVal && sgaBbwzArr.push('胸下段');

  sgaBbwzArr.length ? strArr.push('考虑' + sgaBbwzArr.join('、') + '食管癌') : '';

  // T分期
  var zlfqTVal = getVal('[name="zlfq-t"]:checked');
  if(zlfqTVal === 't4a侵犯') {
    var t4aVal = getVal('[name="t4a-qf"]:checked');
    t4aVal && tfqArr.push('侵犯' + t4aVal);
  }else if(zlfqTVal === 't4b侵犯') {
    var t4bVal = getVal('[name="t4b-qf"]:checked');
    t4bVal && tfqArr.push('侵犯' + t4bVal);
  }else if(zlfqTVal) {
    zlfqTVal && tfqArr.push(zlfqTVal);
  }
  tfqArr.length ? strArr.push(tfqArr) : '';

  return strArr.length ? strArr.join('，') : '';
}

// 获取印象——远处转移
function getYczyImp() {
  var yczyVal = getVal('[name="yczy"]:checked');
  if(yczyVal.includes('肺部')) {
    var fbSiteVal = getVal('[name="fb-site"]:checked');
    if(fbSiteVal) {
      yczyVal = yczyVal.replace('肺部',fbSiteVal);
    }
  }
  if(yczyVal.includes('其他转移') || yczyVal.includes('、其他转移')) {
    yczyVal = yczyVal.replace('、其他转移','');
    yczyVal = yczyVal.replace('其他转移','');
  }
  return yczyVal ? '远处转移：' + yczyVal : '';
}

// 获取印象——其他征象
function getQtzxImp() {
  var qtzxArr = [];
  var sglVal = getVal('[id="fjsgact-rt-142"]:checked');
  sglVal && qtzxArr.push('食管瘘');
  var sglgfxVal = getVal('[id="fjsgact-rt-146"]:checked');
  sglgfxVal && qtzxArr.push('食管瘘高风险');
  return qtzxArr.length ? '其他征象：' + qtzxArr.join('、') : '';
}

// 获取印象
function getImpressionText() {
  var strArr = [];
  
  // 病变位置
  var bbwzStr = getBbwzImp();
  bbwzStr && strArr.push(bbwzStr);

  // N分期
  let lbjClassNameArr = ['zglbj-row','fqfmhlbj-row','jbsglbj-row','jbqylbj-row','qtqylbj-row'];
  let lbjCkArr = [];
  for(let className of lbjClassNameArr) {
    let { checkedLbjArr } = getLbjCon(className,'imp');
    checkedLbjArr.length && lbjCkArr.push(...checkedLbjArr);
  }
  // 需要去重，不要生成膈肌淋巴结淋巴结转移
  let filterLbjArr = filterGjlbj(lbjCkArr);
  if(filterLbjArr.length) {
    let lastItem = filterLbjArr[filterLbjArr.length - 1];
    if(lastItem.includes('淋巴结')) {
      strArr.push(filterLbjArr.join('、')+'转移');
    }else {
      strArr.push(filterLbjArr.join('、')+'淋巴结转移');
    }
  }

  // 远处转移
  var yczyStr = getYczyImp();
  yczyStr && strArr.push(yczyStr);

  // 其他征象
  var qtzxStr = getQtzxImp();
  qtzxStr && strArr.push(qtzxStr);

  // 获取分期等级
  var levelStr = getSgaLevel();
  levelStr && strArr.push(levelStr);

  var str = strArr.join('，');

  curElem.find('[id="fjsgact-rt-157"]').val(str);
  // console.log(str);
  return str;
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  let description = getDescContent();
  const startString = description.slice(0, 10);
  if (!startString.includes('结合薄层扫描')) {
    description = description.substring(0, 2) + '结合薄层扫描，' + description.substring(3);
  }
  rtStructure.description = description;
  rtStructure.impression = getImpressionText();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

// 预览页面
function initViewPageCon() {
  var idAndDomMap = rtStructure.idAndDomMap;

  // 病变位置
  var sgaBbwzArr = [];
  var bbwzCk = $('.bbwz-block input[type="checkbox"]:checked');
  if(bbwzCk.length) {
    // 病变位置
    let checkboxNames = ['jdsg', 'xsdsg', 'xzdsg', 'xxdsg'];
    // 肿瘤上缘对应
    let syChildIds = ['fjsgact-rt-10', 'fjsgact-rt-13', '-2']
    checkboxNames.forEach(name => {
      var oneCheckbox = [];  //一条的数据
      var checkboxVal = getVal(`[name="${name}"]:checked`);
      // oneCheckbox.push(checkboxVal);
      if(checkboxVal) {
        if(name === 'jdsg') {
          var sqjlVal = getVal('[name="sqjl"]:checked','，');
          sqjlVal && oneCheckbox.push(sqjlVal);
        }
        var pid = $(`[name="${name}"]`).attr('id');
        for(var i = 0; i < syChildIds.length; i++) {
          // 兼容旧数据
          var childId = syChildIds[i].includes('fjsgact') ? syChildIds[i] : `${pid}${syChildIds[i]}`;
          if($(`[id="${childId}"]`).siblings().attr('pid') === pid) {
            var syVal = getVal(`[id="${childId}"]`);
            syVal && oneCheckbox.push('肿瘤上缘对应' + syVal + '椎体平面水平');
            break;
          }
        }
        // 肿瘤下缘对应 
        var xsdsyVal = getVal(`[id="${pid}-4"]`);
        xsdsyVal && oneCheckbox.push('肿瘤下缘对应' + xsdsyVal + '椎体平面水平');
        if(oneCheckbox.length) {
          sgaBbwzArr.push(checkboxVal + '\n' + `（${oneCheckbox.join('；')}）`);
        } else {
          sgaBbwzArr.push(checkboxVal);
        }
      }
    })
    if(sgaBbwzArr.length) {
      $('.bbwz-block .row-tit').html('<div>病变位置：</div><div>（肿瘤中心位于）</div>');
      $('.bbwz-con').html(sgaBbwzArr.join('\n'));
    }
  }else {
    $('.bbwz-block').hide();
  }
  
  // 大小测量
  var dxclArr = [], inpArr = [], bzInfoArr = [];
  var zldxclVal = getVal('[name="zldxcl"]:checked');
  if(zldxclVal) {
    // 肿块输入框
    var zkHdmInp1 = getVal('[id="fjsgact-rt-20"]');
    var zkHdmInp2 = getVal('[id="fjsgact-rt-21"]');
    zkHdmInp1 && inpArr.push(zkHdmInp1 + 'mm');
    zkHdmInp2 && inpArr.push(zkHdmInp2 + 'mm');
    inpArr.length && bzInfoArr.push('横断面测量：' + inpArr.join('x'));
    var zkSzmInp = getVal('[id="fjsgact-rt-23"]');
    zkSzmInp && bzInfoArr.push('矢状面测量（纵径）：' + zkSzmInp + 'mm');
    // 管壁浸润型输入框
    var gbHdmInp = getVal('[id="fjsgact-rt-25"]');
    var gbSzmInp = getVal('[id="fjsgact-rt-27"]');
    gbHdmInp && bzInfoArr.push('横断面测量管壁最厚：' + gbHdmInp + 'mm');
    gbSzmInp && bzInfoArr.push('矢状面测量（纵径）：' + gbSzmInp + 'mm');
    zldxclVal && dxclArr.push(zldxclVal);
    bzInfoArr.length && dxclArr.push(bzInfoArr.join('；'));
    $('.dxcl-con').html(dxclArr.join('，'));
  }else {
    $('.dxcl-block').hide();
  }
  
  // 病变环绕管周径
  var bbhrgzjVal = getVal('[name="bbhrgzj"]:checked');
  if(!bbhrgzjVal) {
    $('.bbhrgzj-block').hide();
  }

  // 病变所在方位
  var bbszfwVal = getVal('[name="bbszfw"]:checked');
  if(!bbszfwVal) {
    $('.bbszfw-block').hide();
  }else {
    $('.bbszfw-con').html(bbszfwVal);
  }

  // 强化特点
  var qhtdVal = getVal('[name="qhtd"]:checked');
  if(qhtdVal) {
    $('.xhtdqh-block .row-tit').html('信号特点及强化描述：');
    $('.xhtdqh-con').html(qhtdVal+'。');
  }else {
    $('.xhtdqh-block').hide();
  }

  // 病灶处管腔狭窄
  let ywBzcgqxzVal = getVal('[name="bzcgqxz"]:checked');
  var ygqxzVal = getVal('[name="jfjdyssg"]:checked','、');
  if(ywBzcgqxzVal === '无') {
    $('.bzcgxz-block .bzcgxz-con').html(ywBzcgqxzVal);
  }
  if(ygqxzVal) {
    let ygqxzStr = `有（继发近端以上食管${ygqxzVal}）`;
    $('.bzcgxz-block .bzcgxz-con').html(ygqxzStr);
  }

  // T分期
  var tfqStr = '';
  var zlfqTVal = getVal('[name="zlfq-t"]:checked');
  if(zlfqTVal) {
    var zlfqTMap = {
      '未侵及纤维膜': 'T1-2',
      '侵犯食管深肌层外纤维、脂肪组织': 'T3',
      't4a侵犯': 'T4a',
      't4b侵犯': 'T4b'
    }
    for(let key in zlfqTMap) {
      if(key === zlfqTVal) {
        if(zlfqTVal === 't4a侵犯') {
          var t4aVal = getVal('[name="t4a-qf"]:checked');
          tfqStr = t4aVal && `T4：${zlfqTMap[key]}：侵犯` + t4aVal;
        }else if(zlfqTVal === 't4b侵犯') {
          var t4bVal = getVal('[name="t4b-qf"]:checked');
          tfqStr = t4bVal && `T4：${zlfqTMap[key]}：侵犯` + t4bVal;
        }else {
          tfqStr = zlfqTMap[key] + '期：' + zlfqTVal;
        }
      }
    }
    $('.tfq-block .row-tit').html('<div>T分期：</div><div>（肿瘤浸润程度）</div>');
    $('.tfq-block .tfq-con').html(tfqStr);
  }else if(!zlfqTVal) {
    $('.tfq-block').hide();
  }

  // N分期
  showTableVal('edit');
  let lbjClassNameArr = ['zglbj-row','fqfmhlbj-row','jbsglbj-row','jbqylbj-row','qtqylbj-row'];
  let lbjClassNameMap = {
    'zglbj-row': 'zglbj',
    'fqfmhlbj-row': 'fqfmhlbj',
    'jbsglbj-row': 'jbsglbj',
    'jbqylbj-row': 'jbqylbj',
    'qtqylbj-row': 'qtqylbj',
  };
  for(let className of lbjClassNameArr) {
    let { lbjRowInfoList } = getLbjCon(className,'desc');
    let lbjStr = '';
    if(!lbjRowInfoList.length) {
      $('.'+lbjClassNameMap[className]+'-tit').css('display','none');
      $('.'+lbjClassNameMap[className]+'-block').css('display','none');
    }
    for(let idx in lbjRowInfoList) {
      let lbjStrArr = [];
      let item = lbjRowInfoList[idx];
      lbjStrArr.push(item.name);
      item['num'] && lbjStrArr.push(item['num'] + '枚');
      item['maxDj'] && lbjStrArr.push(item['maxDj'] + 'mm');
      let serStr = item['ser'] && 'Ser：' + item['ser'];
      let imageStr = item['image'] && 'Image：' + item['image'];
      let isNeedDot =  imageStr ? '，' : ''; // 是否需要逗号
      let serOrImage = (serStr || imageStr) ? '（' +   serStr + isNeedDot + imageStr + '）' : '';
      serOrImage && lbjStrArr.push(serOrImage);
      lbjStr += lbjStrArr.join('，') + '\n';
    }
    $('.'+lbjClassNameMap[className]+'-block').html(lbjStr);
  }

  // 远处转移
  var yczyStr = getYczyCon('view');
  if(yczyStr) {
    $('.yczy-con').html(yczyStr);
  }else {
    $('.yczy-block').hide();
  }

  // 其他征象
  var strArr = [];
  var sglVal = getVal('[id="fjsgact-rt-142"]:checked');
  var sglInpVal = getVal('[id="fjsgact-rt-144"]');
  sglVal && strArr.push('食管瘘：是' + (sglInpVal ? ('（' + sglInpVal + '）') : ''));
  var sglgfxVal = getVal('[id="fjsgact-rt-146"]:checked');
  var sglgfxInpVal = getVal('[id="fjsgact-rt-148"]');
  sglgfxVal && strArr.push('食管瘘高风险：是' + (sglgfxInpVal ? ('（' + sglgfxInpVal + '）') : ''));
  var qtzxInpVal = getVal('[id="fjsgact-rt-150"]');
  qtzxInpVal && strArr.push(qtzxInpVal);
  $('.qtzx-block .row-tit').css('line-height','40px');
  if(strArr.length) {
    $('.qtzx-con').html(strArr.join('\n'));
  }else {
    $('.qtzx-con').html('无');
  }

  // 肺部
  var fbVal = getFbCon();
  if(fbVal) {
    $('.fb-con').html(fbVal);
  }else {
    $('.fb-block').hide();
  }
  // 印象
  $('.footer-view .bt-imp').html(idAndDomMap['fjsgact-rt-157']?.value || '');
}