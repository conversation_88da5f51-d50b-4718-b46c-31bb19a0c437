var defaultDateTimeFormat = {
  'date': 'yyyy-MM-dd',
  'time': 'HH:mm:ss',
  'datetime': 'yyyy-MM-dd HH:mm:ss'
};

var plainImgBase64 = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // 空白图片

var widgetInit = {
  renderDateTime: function (params) {
    var id = params.id;
    var type = params.type;
    var format = params.format;
    if (!id && !type) {
      return;
    }
    delete params.id;
    delete params.type;
    delete params.format;
    var opts = {
      type: type,
      elem: '#' + id,
      format: format || defaultDateTimeFormat[type]
    };
    for (var k in params) {
      if (!(k in opts)) {
        opts[k] = params[k];
      }
    }
    layui.laydate.render(opts);
  },
  renderDropdown: function (params) {
    var id = params.id;
    var options = params.options;
    if (!id || !options || options.length === 0) {
      return;
    }
    _renderDropdown({
      selector: '#' + id,
      dataList: options,
      titleKey: 'value',
      filterKeys: ['value'],
      multiple: params.multiple
    });
  },
  renderSampleDateTime: function(params) {
    // 获取标本的时间类型
    // actType 1：标本采集 2：标本送出 3：标本接收 4：取材 5：制片 6：诊断
    if(window.getSampleInfoForSR && window.rtStructure) {
      var actType = params.actType;
      var id = params.id;
      var format = params.format || 'YYYY-MM-DD HH:mm:ss';
      var [dateFormat = '', timeFormat = ''] = format.split(' ');
      if(dateFormat.toLowerCase().includes('yyyy')) {
        dateFormat = dateFormat.toUpperCase();
      }
      format = `${dateFormat||''}${dateFormat?' ':''}${timeFormat||''}`;
      var publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
      var sampleList = window.getSampleInfoForSR(publicInfo.examNo, actType) || [];
      if(sampleList.length) {
        var datetime = (sampleList[0].actBeginDate || '') + ' ' + (sampleList[0].actBeginTime || '');
        var res = dayjs(datetime).format(format);
        $(`#${id}`).val(res);
      }
    }
  },
  renderSampleBarNumber: function(params) {
    // 获取标本条码号
    if(window.getSampleInfoForSR && window.rtStructure) {
      var id = params.id;
      var publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
      var sampleList = window.getSampleInfoForSR(publicInfo.examNo) || [];
      if(sampleList.length) {
        $(`#${id}`).val(sampleList[0].barcodeNo || '');
      }
    }
  },
  getRptImpression: function(params) {
    // 获取诊断结果
    if(window.getRptImpression && window.rtStructure) {
      var id = params.id;
      var rptType = params.rptType;
      var publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
      var resText = window.getRptImpression({examNo: publicInfo.examNo, rptType: rptType}) || [];
      if(resText) {
        var originVal = $(`#${id}`).val();
        var wrapText = '';
        if (originVal && originVal[originVal.length - 1] !== '\n') {
          wrapText = '\n';
        }
        let text = originVal + wrapText + resText;
        $(`#${id}`).val(text || '');
      }
    }
  },
  renderStaffType: function(params) {
    // 获取当前科室人员
    // 人员类型staffType 
    if(window.getDepartUserList) {
      var staffType = params.staffType;
      var sampleList = window.getDepartUserList(staffType) || [];
      return sampleList;
    }
  },
  getPickBtnContent: function(params) { 
    // 获取提取按钮的内容
    if(window.getPacsPickResult && window.rtStructure) {
      var pickType = params.pickType;
      var id = params.id;
      var tid = params.tid;
      var publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
      // 调用接口提取对应内容
      window.getPacsPickResult(publicInfo.examNo, pickType, $(`#${tid}`), params);
    }
  },
  getSignData: function(params) { 
    // 获取提取按钮的内容
    if(window.getSignDataHandler && window.rtStructure) {
      var id = params.id;
      var publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
      // 调用接口电子签名接口对应数据
      var data = window.getSignDataHandler(publicInfo, id);
      if($('#' + id).length) {
        var wrap = $('#' + id).find('.sign-data-wrap');
        wrap.text(data);
        if(window.scaleSignArea) {
          window.scaleSignArea('#' + id, '.sign-lb', '.sign-data-wrap');
        }
      }
    }
  },
  getMeasureParam: function(params) {
    // 获取测量参数值
    if(window.getRptImpression && window.rtStructure) {
      var id = params.id;
      var paramCodeStr = params.paramCode.trim();
      if (!paramCodeStr) {
        return;
      }
      var publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  // 检查信息
      var examNo = publicInfo.examNo;
      var matchParams = paramCodeStr.match(/{[^}]+}/g);
      if (matchParams) {
        matchParams.forEach(function (param, idx, arr) {
          var reg = new RegExp(param, 'ig');
          var key = param.replace(/[{}]/g, '');
          var resText = window.getListByExamNoAndParamCode({examNo: examNo, paramCode: key}) || '';
          paramCodeStr = paramCodeStr.replace(reg, resText);
        });
        $(`#${id}`).text(paramCodeStr);
        $(`#${id}`).val(paramCodeStr);
      }
    }
  },
  // 初始化手写签名板
  initSignPannel: function(params) {
    var id = params.id;
    var signLineWidth = params.signLineWidth || 4;
    var signLineColor = params.signLineColor || '#000';
    var inp =  $('#' + id)[0];
    var wrap = inp.parentElement;
    var img =  wrap.querySelector('img');
    var canvas = wrap.querySelector('canvas');
    var oGC = canvas.getContext('2d');
    canvas.width = parseFloat(wrap.style.width);
    canvas.height = parseFloat(wrap.style.height);
    var isMouseDown = false; // 是否按下鼠标
    var isDraw = false; // 是否画有内容
    var lastLoc = { x: 0, y: 0 }; // 上一次鼠标的坐标点

    if (inp.value) {
      img.src = inp.value;
      img.onload = function () {
        oGC.drawImage(img , 0 , 0);
        isDraw = true;
      };
    }

    if (!window.rtStructure || window.rtStructure.enterOptions.type === 'edit') {
      if ('ontouchstart' in window) {
        canvas.addEventListener('touchstart', function (ev) {
          ev.preventDefault();
          var curLoc = windowToCanvas(ev.touches[0].clientX, ev.touches[0].clientY);
          pointerdown({offsetX: curLoc.x, offsetY: curLoc.y});
        }, false);
        canvas.addEventListener('touchmove', function (ev) {
          ev.preventDefault();
          var curLoc = windowToCanvas(ev.touches[0].clientX, ev.touches[0].clientY);
          pointermove({offsetX: curLoc.x, offsetY: curLoc.y});
        }, false);
        canvas.addEventListener('touchend', pointerEnd, false);
      } else {
        canvas.addEventListener('pointerdown', pointerdown, false);
        canvas.addEventListener('pointermove', pointermove, false);
        canvas.addEventListener('pointerup', pointerEnd, false);
        wrap.addEventListener('pointerleave', pointerEnd, false);
      }
    }

    function pointerdown(ev) {
      ev.preventDefault && ev.preventDefault();
      isMouseDown = true;
      lastLoc = {x: ev.offsetX, y: ev.offsetY};
    }

    function pointermove(ev) {
      if (!isMouseDown) {
        return;
      }
      ev.preventDefault && ev.preventDefault();
      var curLoc = {x: ev.offsetX, y: ev.offsetY};
      oGC.beginPath();
      oGC.moveTo(lastLoc.x, lastLoc.y);
      oGC.lineTo(curLoc.x, curLoc.y);
      oGC.strokeStyle = signLineColor;
      oGC.lineWidth = signLineWidth;
      oGC.lineCap = "round";
      oGC.lineJoin = "round";
      oGC.stroke();
      lastLoc = curLoc;
      isDraw = true;
    }

    function pointerEnd() {
      if (isMouseDown && isDraw) {
        var src = canvas.toDataURL();
        inp.value = src;
        img.src = src;
      }
      isMouseDown = false;
    }

    // 触屏模式 拿到当前点击的坐标点
    function windowToCanvas(x, y) {
      // 拿到canvas距离文档里offsetLeft 和 offsetTop
      var clientRect = canvas.getBoundingClientRect();
      return {
        x: Math.round(x - clientRect.left),
        y: Math.round(y - clientRect.top)
      }
    }

    window.addEventListener('message', function(e) {
      if (!e.data) {
        return;
      }
      var message = e.data.message;
      // 重签
      if (message === 'signPannelClear') {
        inp.value = '';
        img.src = plainImgBase64;
        isDraw = false;
        oGC.clearRect(0, 0, 9999, 9999);
      }
    });
  },
  // 初始化信手书签名组件
  initBjcaSign: function(params) {
    var id = params.id;
    var businessParam = params.businessParam;
    var inp =  $('#' + id)[0];
    var wrap = inp.parentElement;
    var img =  wrap.querySelector('img');
    if (inp.value) {
      img.src = inp.value;
    }
    window.addEventListener('message', function(e) {
      if (!e.data) {
        return;
      }
      var message = e.data.message;
      // 重签
      if (message === 'signPannelClear') {
        inp.value = '';
        img.src = plainImgBase64;
      }
    });
  }
};

function widgetRender() {
  if (!window.$ || !window.layui) {
    setTimeout(widgetRender, 100);
    return;
  }
  $('[data-init]').each(function () {
    var params = $(this).attr('data-init');
    if (!params) {
      return;
    }
    try {
      params = JSON.parse(params);
      if (!params.method) {
        return;
      }
      if (params.params) {
        for(let key in params.params) {
          // 如果参数是方法则先执行方法，再赋值
          if(widgetInit[params.params[key]] && typeof widgetInit[params.params[key]] === 'function') {
            params.params[key] = widgetInit[params.params[key]](params.params);
            continue;
          }
        }
        if(params.event) {
          $(this).on(params.event, function() {
            widgetInit[params.method](params.params);
          });
        } else {
          widgetInit[params.method](params.params);
        }
      } else {
        widgetInit[params.method]();
      }
    } catch (error) {
      console.error(error);
    }
  });
}

/**
 * 报告图片回显
 * @param {Array<string>} rptImageList 报告图片地址列表
 * @param {String} id 组件元素id，不带#
 */
function widgetRptImageRender(rptImageList, id) {
  if (!rptImageList || rptImageList.length === 0 || !id) {
    return;
  }
  var $imgs = $('#' + id).find('.rpt-image');
  if ($imgs.length === 0) {
    return;
  }
  $imgs.each(function(idx) {
    if (rptImageList[idx]) {
      $(this).attr('src', rptImageList[idx]).show();
    } else {
      // $(this).hide();
      $(this).closest('.image-cont').remove();
    }
  });
}

/**
 * 签名图片回显
 * @param {String} imgUrl 签名图片地址
 * @param {String} id 组件元素id，不带#
 */
function widgetSignImageRender(imgUrl, id) {
  if (!imgUrl || !id) {
    $('#' + id).closest('.w-con').remove();
    return;
  }
  $('#' + id).attr('src', imgUrl).show();
}

/**
 * 转换下拉数据格式
 * @param {Array<Object>|Array<String>} dataList 下拉数据列表
 * @param {String} [titleKey] 显示在下拉菜单的内容的键名
 * @returns {Array}
 */
function _transformDropdownOptList(dataList, titleKey) {
  var arr = [];
  dataList.forEach(function(item, index) {
    if (titleKey) {
      var obj = {
        title: item[titleKey],
        id: index,
        templet: '<span title="' + item[titleKey] + '">' + item[titleKey] + '</span>'
      };
      for (var k in item) {
        obj[k] = item[k];
      }
      arr.push(obj);
    } else {
      arr.push({
        title: item,
        id: index,
        templet: '<span title="' + item + '">' + item + '</span>'
      });
    }
  })
  return arr;
}

/**
 * 渲染下拉类型表单，内部已支持点击选项后填写内容到输入框，可以传入layui.dropdown.render支持的参数
 * @param {Object} options 参数
 * @param {String} options.selector 输入框选择器
 * @param {Array<Object>|Array<String>} options.dataList 下拉数据列表
 * @param {String} [options.titleKey] 下拉数据的key。如果options.dataList为Array<String>，可忽略
 * @param {Array<String>|Boolean} [options.filterKeys] 手动输入时搜索的键名列表，传入这参数即开启选项搜索功能
 * @example
 *
  // 普通使用
  var dataList1 = ['选项1', '选项2', '选项3'];
  _renderDropdown({
    selector: '.class1',
    dataList: dataList1
  });
  // 开启选项过滤
  _renderDropdown({
    selector: '.class2',
    dataList: dataList1,
    filterKeys: true
  });

  var dataList2 = [{name: '选项1', code: 'xx1'}, {name: '选项2', code: 'xx2'}, {name: '选项3', code: 'xx3'}];
  _renderDropdown({
    selector: '.class3',
    dataList: dataList2,
    titleKey: 'name'
  });
  // 开启选项过滤
  _renderDropdown({
    selector: '.class4',
    dataList: dataList2,
    titleKey: 'name',
    filterKeys: ['name', 'code']
  });
*/
function _renderDropdown(options) {
  var selector = options.selector;
  var dataList = options.dataList;
  var titleKey = options.titleKey;
  var filterKeys = options.filterKeys;
  var customClick = options.click;
  var multiple = options.multiple;
  var separator = ',';
  delete options.click;
  if (!selector || typeof selector !== 'string') {
    console.error('无效selector');
    return;
  }
  if ($(selector).length === 0) {
    console.error('无效selector');
    return;
  }
  if (!dataList || !(dataList instanceof Array) || dataList.length === 0) {
    console.error('无效dataList');
    return;
  }
  if (typeof dataList[0] === 'string') {
    titleKey = '';
  }
  var dropOpts;
  if (titleKey) {
    dropOpts = _transformDropdownOptList(dataList, titleKey);
  } else {
    dropOpts = _transformDropdownOptList(dataList);
  }
  var isFilter = (filterKeys instanceof Array && filterKeys.length > 0 && typeof filterKeys[0] === 'string') || (typeof filterKeys === 'boolean' && filterKeys);
  var showKey = 'title';
  var renderOptions = {
    elem: selector,
    data: dropOpts,
    className: 'laySelLab',
    click: function(data, othis) {
      var elVal = this.elem.val().trim();
      var clickVal = data[showKey];
      var newVal;
      if (elVal && multiple) {
        var valArr = elVal.split(separator);
        valArr = valArr.filter(function(v) {return !!v});
        if (valArr.indexOf(clickVal) === -1) {
          valArr.push(clickVal);
          newVal = valArr.join(separator);
        } else {
          newVal = elVal;
        }
      } else {
        newVal = clickVal;
      }
      this.elem.val(newVal);
      this.elem.attr('value', newVal);
      this.elem.focus();
      let id = this.elem.attr('id');
      if(this.elem.siblings('[id="'+id+'_no"]').length) {
        this.elem.siblings('[id="'+id+'_no"]').val(data.id);
      }
      if (isFilter) {
        layui.dropdown.reload(this.id, {
          data: dropOpts,
          show: false
        });
      }
      if (customClick) {
        return customClick.call(this, data, othis);
      }
    }
  };
  for (var k in options) {
    renderOptions[k] = options[k];
  }
  if (!isFilter) {
    layui.dropdown.render(renderOptions);
    return;
  }
  $(selector).each(function() {
    if (!this.id) {
      this.id = ('d' + Math.random()).replace('.', '');
    }
    renderOptions.elem = '#' + this.id;
    renderOptions.id = this.id;
    layui.dropdown.render(renderOptions);
    var TT;
    // 监听输入，根据输入内容过滤选项
    $(this).on('input', function() {
      var el = this;
      clearTimeout(TT);
      TT = setTimeout(function() {
        var elVal = el.value.trim();
        var val;
        if (multiple) {
          var valArr = elVal.split(separator);
          val = valArr.pop() || '';
        } else {
          val = elVal;
        }
        var newOpts;
        if (!val) {
          newOpts = dropOpts;
        } else {
          newOpts = dropOpts.filter(function(item) {
            if (typeof filterKeys === 'boolean') {
              if (item.title.indexOf(val) !== -1) {
                return item;
              }
            } else {
              var isTarget = filterKeys.some(function(k) {
                return item[k] && item[k].indexOf(val) !== -1;
              });
              if (isTarget) {
                return item;
              }
            }
          });
        }
        layui.dropdown.reload(el.id, {
          data: newOpts,
          show: true
        });
      }, 200);
    });
  });
}

/**
 * 文本域内容自适应撑开事件初始化和输入调整，标识h-resize=1的控件
 */
function _renderHeightResizeByContent() {
  function handlerTextareaHeight(dom) {
    autoAdjustTextareaHeight(dom);
  }
  // 高度随输入内容内容增高
  $('body').on('input', 'textarea[h-resize="1"]', function() {
    autoAdjustTextareaHeight(this);  //存在/sreport/template-lib/utils/common/common.js
  })
  
  // 初始化内容高度
  $('body').find('textarea[h-resize="1"]').each(function() {
    this.handlerTextareaHeight = handlerTextareaHeight;  //挂个内置方法供外部调用，插入常用词处理
    autoAdjustTextareaHeight(this, true);
  })
}

/**
 * 对于设为百分比宽度的元素，结合页面编辑时的宽度大小进行调整，显示在实际业务应用中
 * 避免出现多余滚动条，页面宽度存于.page  et-w
 * 思路：
 * 1、判断先展示页面与编辑器页面的宽度比例，实际比编辑的大，则不进行调整left，值调整按百分比为宽度的组件
 * 2、如果实际小，那么将原left/宽度比例，反之left*宽度比例，确保位置不变
 * 3、如果组件宽度为%，也要按比例对应缩放
 */
function _renderWidgetWidthByPercent() {
  let pW = $('[is-rt-editor="1"] .page').attr('et-w');  //在编辑器中的使用宽度
  let currentPageWidth = $('[is-rt-editor="1"] .page').outerWidth();  // 获取当前页面宽度
  let pOriginWidth = $('[is-rt-editor="1"] .page')[0].style.width;  //页面元素设置宽度
  if(pOriginWidth.indexOf('%') === -1) {
    return;
  }
  if(!pW) {
    return;
  }
  let widgetList = $('[is-rt-editor="1"]').find('.rt-layout');
  widgetList.each(function(i, dom) {
    let width = dom.style.width;
    // 只处理百分比的宽度
    if(width.indexOf('%') > -1) {
      let percent = parseInt(width) / 100;  //编辑时占的百分比
      let children = $(dom).children();
      let domWidth = Math.floor(percent * currentPageWidth);
      let pageRate = Number((pW > domWidth ? (domWidth / pW) : (pW / domWidth)).toFixed(3));  //页面间的比例
      children.each(function(i, child) {
        let childWidth = child.style.width;
        let left = parseInt(child.style.left) || 0;
        if(domWidth > 0) {
          // 编辑页比实际页面大才调整left
          if(pW > domWidth) {
            let leftInPage = Math.floor(left / pageRate);
            let newLeft = Math.floor((leftInPage / pW) * 100); 
            child.style.left = newLeft + '%';
          }
          if(childWidth.indexOf('%') > -1 && childWidth !== '100%') {
            if(pW > domWidth) {
              let newWidth = parseInt(childWidth) - pageRate;
              // 尽可能缩小宽度，暂定写5
              if(newWidth > 90) {
                newWidth -= 5;
              }
              child.style.width = Math.floor(newWidth) + '%';
            } else {
              child.style.width = Math.floor(parseInt(childWidth) / 100 * pW) + 'px';
            }
          }
        }
      })
    }
  })
}