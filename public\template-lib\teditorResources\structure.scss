/* 此处的css将在导出模板时同时导出 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
  vertical-align: middle;
  -webkit-print-color-adjust: exact;
}
ul, li {
  list-style: none;
}
.layui-dropdown-menu {
  max-height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
}
.t-pg {
	display: block;
  height: 100%;
  input,
  select,
  textarea {
    outline: none;
  }
  .page {
    position: relative;
    text-align: left;
    box-sizing: border-box;
    font-size: 16px;
    z-index: 0;
    margin: 0 auto;
    background: #fff;
    /* 布局组件 */
    .rt-layout {
      &.v {
        flex-direction: column;
      }
      .ly-con {
        word-break: break-word;
        white-space: pre-wrap;
      }
    }
    .rt-table-wrap {
      overflow-x: auto;
    }
    .rt-table {
      position: absolute;
      width: 100%;
      table-layout: fixed;
      td {
        vertical-align: middle;
      }
    }
    .w-i {
      white-space: pre-wrap;
      word-break: break-word;
      outline: none;
      &.pick-btn {
        &:hover {
          opacity: 0.8;
        }
      }
      &.title {
        display: inline-block; // 处理纯文本行高在管理工作站预览差异问题
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        border: 0px;
        text-align: inherit;
        line-height: inherit;
        color: inherit;
        font-size: inherit;
        font-weight: inherit;
        font-style: inherit;
        font-family: inherit;
        font-weight: inherit;
        background-color: inherit;
        white-space: pre-wrap;
        word-break: break-word;
        resize: none;
      }
      &.input {
        box-sizing: border-box;
        width: 100%;
        outline: none;
        height: 100%;
        margin: 0;
        border: none;
        color: inherit;
        font-size: inherit;
        line-height: inherit;
        font-family: inherit;
        text-align: inherit;
        font-style: inherit;
        font-weight: inherit;
        background-color: inherit;
        resize: none;
      }
      input {
        text-align: inherit;
      }
      &.code-img {
        height: 100%;
      }
      &.r-gp, &.ck-gp {
        color: inherit;
        font-size: inherit;
        line-height: inherit;
        font-family: inherit;
        text-align: inherit;
        font-style: inherit;
        font-weight: inherit;
        background-color: inherit;
        .opt-ls {
          display: inline-block;
        }
        &.v {
          label {
            display: block;
            & + label {
              margin-left: 0;
              margin-top: 5px;
            }
          }
        }
        label {
          display: inline-block;
          margin-right: 15px;
          input[type="radio"], input[type="checkbox"] {
            // margin: 3px 3px 0;
            margin-right: 3px;
            vertical-align: middle;
            display: inline-block;
            line-height: 1;
          }
          .rt-sr-lb {
            vertical-align: baseline;
            display: inline-block;
            line-height: 1;
          }
          input:checked {
            & +.rt-sr-lb {
              color: #1885f2;
            }
          }
        }
      }
      &.image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      &.image-list {
        width: 100%;
        height: 100%;
        padding: 5px;
        overflow: hidden;
        .image-cont {
          padding: 5px;
          // float: left;
          display: inline-block;
        }
        .rpt-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      &.with-icon {
        position: relative;
        height: 100%;
        width: 100%;
        padding-left: 18px;
        .layui-icon {
          position: absolute;
          left: 2px;
          top: 50%;
          margin-top: -5px;
          line-height: normal;
        }
        .rt-sr-w {
          width: 100%;
          height: 100%;
        }
        input {
          outline: none;
          border: 0;
          background: none;
          padding: 0 4px;
        }
        &.right-icon {
          padding-left: 0;
          padding-right: 14px;
          .layui-icon {
            left: inherit;
            right: 2px;
            font-size: 12px;
          }
        }
      }
    }
    .w-con {
      &.rt-sr-inv {
        color:red!important;
        border-color:red!important;
      }
      .sign-lb {
        display: block;
        width: 100%;
      }
      .text-con {
        display: inline-block;
      }
    }
    .sign-pannel-inner {
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
    canvas.sign-pannel-inner {
      position: absolute;
      z-index: 2;
      background-color: #fff;
    }
    img.sign-pannel-inner {
      position: absolute;
      z-index: 1;
      pointer-events: none;
      object-fit: contain;
    }
    input.sign-pannel-inner {
      opacity: 0;
    }
  }
  .w-con[wt="29"] {
    border: none !important;
    padding: 0 !important;
    border-radius: 0 !important;
    .w-i {
      display: inline-block;
    }
  }
  .page-break-line {
    position: absolute;
    z-index: 1000;
    width: 100%;
    &::after {
      content: '';
      position: absolute;
      left: -50px;
      right: -20px;
      height: 1px;
      border-top: 1px dashed rgba(0,0,0,.4);
    }
    .page-no {
      color: #FFF;
      font-size: 14px;
      font-weight: bold;
      position: absolute;
      top: -10px;
      right: -60px;
      height: 1px;
    }
  }
}

[isView="true"] {
  canvas.sign-pannel-inner {
    z-index: 1 !important;
  }
  img.sign-pannel-inner {
    z-index: 2 !important;
  }
}

*::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 10px;
}

*::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 6px;
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #ccc;
}

*::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	position: absolute;
	z-index: 8000;
	box-shadow: inset 0 0 5px #eee;
	background: #f2f2f2;
}
mjx-container,mjx-container * {
  font-family: inherit !important;
  vertical-align: baseline;
}
