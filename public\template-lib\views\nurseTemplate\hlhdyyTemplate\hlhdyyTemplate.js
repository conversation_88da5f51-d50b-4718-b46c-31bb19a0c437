$(function () {
  window.initHtmlScript = initHtmlScript;
})
var ndList = [
  {
    id: '1',
    title: 'mg/ml'
  },
  {
    id: '2',
    title: 'mi/ml'
  }
]
var isSavedReport = false; //模版是否保存
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  console.log(rtStructure)
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      togglePageType('preview')
    } else {
      togglePageType('edit')
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function initPage() {
  $('input').attr('autocomplete','off')
  // 没保存过
  toNurseList()
  // layInit()
  if (window.getCommonUseList) {
    getNURSE_DRUG_SITE()
    getpipeway()
    getUseBefore()
    getUseAfter()
    useAgentUnit()

  }
  if (!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    useServerTime('hlhdyy-rt-0011')
    addDictionaryHandler('', 'drugSite')
    addDictionaryHandler('', 'pipeway')
    addDictionaryHandler('', 'useBefore')
    addDictionaryHandler('', 'useAfter')
  } else {
    displaygyContent();
  }
  // initInpAndSel('hlhdyy-rt-0003-002', ndList)
  // initInpAndSel('hlhdyy-rt-0005-002', ndList)
}
function togglePageType(type) {
  $(`[data-type]`).hide()
  $(`[data-type=${type}]`).show()
  if (type === 'preview') {
    $('[data-key]').each(function () {
      let key = $(this).attr('data-key')
      if (idAndDomMap) {
        let result = []
        Object.keys(idAndDomMap).forEach(idKey => {
          if (idKey.startsWith(key)) {
            let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value : '';
            if (value) {
              result.push(value)
            }
          }
        })
        if (key === 'hlhdyy-rt-0006-') {
          $(this).html(result.join(':'))
        } else if (key === 'hlhdyy-rt-0008-' || key === 'hlhdyy-rt-0009-') {
          $(this).html(result.join('、'))
        } else {
          $(this).html(result.join(','))
        }
        this.style.color = '#000'
      }
    })
  }
}
// 添加过字典内容
function addDictionaryHandler(oldPaneId, type) {
  var newPaneBlock = '';
  if (type === 'drugSite') {
    newPaneBlock = $('#drugSite');
  } else if (type === 'pipeway') {
    newPaneBlock = $('#pipeway');
  } else if (type === 'useBefore') {
    newPaneBlock = $('#useBefore');
  } else if (type === 'useAfter') {
    newPaneBlock = $('#useAfter');
  }
  // console.log('newPaneBlock',newPaneBlock);
  if (rtStructure) {
    if (rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = '';
    if (type === 'drugSite') {
      wCon = $('#drugSite').find("[rt-sc]");
    } else if (type === 'pipeway') {
      wCon = $('#pipeway').find("[rt-sc]");
    } else if (type === 'useBefore') {
      wCon = $('#useBefore').find("[rt-sc]");
    } else if (type === 'useAfter') {
      wCon = $('#useAfter').find("[rt-sc]");
    }
    wCon.each(function (wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';

      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if (groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function (scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if (key) {
          if (key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="' + id + '"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)

      rtStructure.idAndDomMap[id] = { ...scObj };
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initgyTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function initgyTigger(newPaneBlock, oldFlag) {
  if (oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function (i, widget) {
      var id = $(widget).attr('id');
      // console.log('id999',id)
      if (rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  }
}
function displaygyContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions
    && rtStructure.enterOptions.resultData ?
    rtStructure.enterOptions.resultData : [];
  console.log('allResData', allResData);
  if (allResData && allResData.length) {
    let drugSite = allResData.find(item => item.id === 'hlhdyy-rt-0006')
    console.log(drugSite);
    if (drugSite) {
      drugSite.child && drugSite.child.forEach(item => {
        addDictionaryHandler(item.id, 'drugSite')
      })
    }else{
      addDictionaryHandler('', 'drugSite')
    }
    let pipeway = allResData.find(item => item.id === 'hlhdyy-rt-0007')
    if (pipeway) {
      pipeway.child && pipeway.child.forEach(item => {
        addDictionaryHandler(item.id, 'pipeway')
      })
    }else{
      addDictionaryHandler('', 'pipeway')
    }
    let useBefore = allResData.find(item => item.id === 'hlhdyy-rt-0008')
    if (useBefore) {
      useBefore.child && useBefore.child.forEach(item => {
        addDictionaryHandler(item.id, 'useBefore')
      })
    }else{
      addDictionaryHandler('', 'useBefore')
    }
    let useAfter = allResData.find(item => item.id === 'hlhdyy-rt-0009')
    if (useAfter) {
      useAfter.child && useAfter.child.forEach(item => {
        addDictionaryHandler(item.id, 'useAfter')
      })
    }else{
      addDictionaryHandler('', 'useAfter')
    }
  } else {
    curElem.find('.advice-list .advice-item').hide();
  }
  // 监听表单内所有input, textarea, select的input或change事件  
  $('#hlhdyy1').on('input change', 'input, textarea, select', function() { 
    // console.log('input change blur', this) 
    // 向父页面发送消息
    window.parent.postMessage({
      message: 'formChangeNotice',
      data: '表单内容已修改'
    }, '*');
  });  
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: 'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready: function (elePanel, elem) {
      const width = elem[0] && elem[0].clientWidth
      if (elePanel[0]) {
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}
// 获取用药单位
function useAgentUnit(){
  let sdList = window.getCommonUseList({ name: 'NURSE_INJECT_SPEED' }) || []
  let ndList = window.getCommonUseList({ name: 'NURSE_DENSITY' }) || []
  
  initInpAndSel('hlhdyy-rt-0003-002', ndList.map(item=>{
    return {
      title: item.value,
      value: item.value
    }
  }))
  initInpAndSel('hlhdyy-rt-0005-002', sdList.map(item=>{
    return {
      title: item.value,
      value: item.value
    }
  }))
}
// 获取用药部位
function getNURSE_DRUG_SITE() {
  let nurseList = window.getCommonUseList({ name: 'NURSE_DRUG_SITE' });
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="a-center rt-sr-label" for="hlhdyy-rt-0006-${padNumberWithZeros(t + 1, 3)}">
                  <input class="rt-sr-w" id="hlhdyy-rt-0006-${padNumberWithZeros(t + 1, 3)}" name="drug_site" pid="hlhdyy-rt-0006" value="${nurseList[t].value}"  rt-sc="pageId:hlhdyy1;name:${nurseList[t].value};wt:5;desc:${nurseList[t].value};vt:;pvf:;" type="radio" />
                  <span>${nurseList[t].value}</span>
                </label>`
    }
    $('#drugSite').html(html);
  }
  var newPaneBlock = $('#drugSite');
  return newPaneBlock;
}
function getpipeway() {
  let nurseList = window.getCommonUseList({ name: 'NURSE_TUBE_PLACE_MODE' });
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="a-center rt-sr-label" for="hlhdyy-rt-0007-${padNumberWithZeros(t + 1, 3)}">
                  <input class="rt-sr-w" id="hlhdyy-rt-0007-${padNumberWithZeros(t + 1, 3)}" name="pipeway" pid="hlhdyy-rt-0007" value="${nurseList[t].value}"  rt-sc="pageId:hlhdyy1;name:${nurseList[t].value};wt:5;desc:${nurseList[t].value};vt:;pvf:;" type="radio" />
                  <span>${nurseList[t].value}</span>
                </label>`
    }
    $('#pipeway').html(html);
  }
  var newPaneBlock = $('#pipeway');
  return newPaneBlock;
}
// 用药前后
function getUseBefore() {
  let nurseList = window.getCommonUseList({ name: 'BEFORE_AGENT_PERFORMANCE' });
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="a-center rt-sr-label" for="hlhdyy-rt-0008-${padNumberWithZeros(t + 1, 3)}">
                  <input class="rt-sr-w" id="hlhdyy-rt-0008-${padNumberWithZeros(t + 1, 3)}" name="useBefore" pid="hlhdyy-rt-0008" value="${nurseList[t].value}"  rt-sc="pageId:hlhdyy1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" type="checkbox" />
                  <span>${nurseList[t].value}</span>
                </label>`
    }
    $('#useBefore').html(html);
  }
  var newPaneBlock = $('#useBefore');
  return newPaneBlock;
}
function getUseAfter() {
  let nurseList = window.getCommonUseList({ name: 'AFTER_AGENT_PERFORMANCE' });
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="a-center rt-sr-label" for="hlhdyy-rt-0009-${padNumberWithZeros(t + 1, 3)}">
                  <input class="rt-sr-w" id="hlhdyy-rt-0009-${padNumberWithZeros(t + 1, 3)}" name="useAfter" pid="hlhdyy-rt-0009" value="${nurseList[t].value}"  rt-sc="pageId:hlhdyy1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" type="checkbox" />
                  <span>${nurseList[t].value}</span>
                </label>`
    }
    $('#useAfter').html(html);
  }
  var newPaneBlock = $('#useAfter');
  return newPaneBlock;
}
function toNurseList() {
  let list = window.getNurseList({ deptCode: publicInfo.userInfo && publicInfo.userInfo.param && publicInfo.userInfo.param.deptCode || '' });
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
  initInpAndSel('hlhdyy-rt-0010', userList,optHandler);
  if(!isSavedReport){
    // console.log('publicInfo',publicInfo)
    $('#hlhdyy-rt-0010').val(publicInfo&&publicInfo.optName);
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#hlhdyy-rt-0004-001').trigger('click')
  }else{
    let val =  $('#hlhdyy-rt-0004-001').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList,cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style: 'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready: function (elePanel, elem) {
      const width = elem[0] && elem[0].clientWidth
      if (elePanel[0]) {
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}
function layInit() {
  layui.use('laydate', function () {
    var laydate = layui.laydate;
    //执行一个laydate实例
    laydate.render({
      elem: '#hlhdyy-rt-0011', //指定元素
      type: 'datetime'
    });
  });
  setupDateSelection('hlhdyy-rt-0011')
}
function agentSelectOpen() {
  let sickId = publicInfo.sickId
  let pageSize = 100
  let pageNo = 1
  let dateList = [
    {
      label: '本周',
      value: '1',
      id: 'date-1'
    },
    {
      label: '本月',
      value: '2',
      id: 'date-2'
    },
    {
      label: '本季度',
      value: '3',
      id: 'date-3'
    },
    {
      label: '本年',
      value: '4',
      id: 'date-4'
    }
  ]
  let radioContent = dateList.map(item => {
    return `
      <label for="${item.id}">
        <input type="radio" value="${item.value}" name="agent" id="${item.id}">
        <span>${item.label}</span>
      </label>
    `
  }).join('')
  let html = `
    <div style="padding:10px;height:100%;">
      <div class="a-center " style="column-gap:40px;flex-wrap:wrap;">
        <div class="a-center" >
          <span>处方审核日期：</span>
           <div class="radio-buttons "  style="width:200px;">
             ${radioContent}
            </div>
        </div>
        <div style="width:200px;">
          <input class="layui-input" id="agent-date" autocomplete="off"  />
        </div>
      </div>
      <table class="layui-table" id="agent-table" lay-filter="agent-table">
      
      </table>
    </div>
    `
  layer.open({
    type: 1,
    content: html,
    area: ['660px', '500px'],
    shadeClose: true,
    btnAlign: 'r',
    btn: ['关闭'],
    title: '选择造影剂',
    yes: function (index, layero) {
      layer.close(index)
    },
    success: function (layero, index) {
      $('.radio-buttons [name="agent"]').on('change', function (e) {
        const { start, end } = getPeriodDates(this.value)
        $('#agent-date').val(start + ' - ' + end)
        let agentList = getAgent({ sickId, startDate: start, endDate: end, pageSize, pageNo }) || []
        reloadTable(agentList)
      })
      renderTable([])
      if (!window.getAgent) {
        return false
      }
      var laydate = layui.laydate;
      laydate.render({
        elem: '#agent-date'
        , range: true //或 range: '~' 来自定义分割字符
        ,
        done: function (value, date, endDate) {
          if (value) {
            let [startDate, endDate] = value.split(' - ')
            let agentList = getAgent({ sickId, startDate, endDate, pageSize, pageNo }) || []
            reloadTable(agentList)
          }
        }
      });
      $('.radio-buttons #date-1').click()
    }
  })
}
function getPeriodDates(period) {
  const today = dayjs();

  switch (period) {
    case '1': // 本周
      return {
        start: today.startOf('week').format('YYYY-MM-DD'),
        end: today.endOf('week').format('YYYY-MM-DD')
      };
    case '2': // 本月
      return {
        start: today.startOf('month').format('YYYY-MM-DD'),
        end: today.endOf('month').format('YYYY-MM-DD')
      };
    case '3': // 本季度
      const currentMonth = today.month();
      const quarterStartMonth = Math.floor(currentMonth / 3) * 3; // 0-based index
      const quarterEndMonth = quarterStartMonth + 2; // 0-based index

      return {
        start: today.month(quarterStartMonth).startOf('month').format('YYYY-MM-DD'),
        end: today.month(quarterEndMonth).endOf('month').format('YYYY-MM-DD')
      };
    case '4': // 本年
      return {
        start: today.startOf('year').format('YYYY-MM-DD'),
        end: today.endOf('year').format('YYYY-MM-DD')
      };
    default:
      throw new Error('Invalid period. Please use 1 for this week, 2 for this month, 3 for this quarter, or 4 for this year.');
  }
}
function renderTable(agentList) {
  console.log('agentList',agentList)
  layui.use('table', function () {
    var table = layui.table;
    table.render({
      text: {
        none: '暂无相关数据' //默认：无数据。
      },
      elem: '#agent-table',
      height: '300px',
      cols: [[ //表头
        { field: 'agentName', title: '造影剂', width:180 },
        { field: 'username', title: '开处方时间',templet:function(d){
          // console.log(d);
          let { reqDate = '', reqTime = '' } = d
          return reqDate + ' ' + reqTime
        } },
        { field: 'reqDoctorName', title: '处方医生', }, 
        { field: 'city', title: '处方审核时间',templet:function(d){
          // console.log(d);
          let { affirmDate = '', affirmTime = '' } = d
          return affirmDate + ' ' + affirmTime
        }  },
        { field: 'affirmDoctorName', title: '处方审核人',  }, 
        { field: 'experience', title: '操作',templet: `<script type="text/html" id="toolEventDemo">
            <a class="layui-btn layui-btn-xs" lay-event="click">选择</a>
          </script>`,
          width:80
        }
      ]],
      loading:false,
      page: true,
      limit: 20,
      limits: [20, 30, 50, 100],
      id:'agent-table',
      data:agentList
    })
    table.on('tool(agent-table)',function(obj){
      if(obj.data.affirmStatus!='1'){
        layer.msg('造影剂未审核')
        
        return 
      }
      selectAgent(obj.data.agentName,JSON.stringify(obj.data))
    })
  })
}
function reloadTable(list){
  layui.use('table', function () {
    var table = layui.table;
    table.reload('agent-table', {
      data: list
    })
  })
}
function selectAgent(agentName,agentInfo) {
  console.log('agentInfo',agentInfo.doseInjection);
  let agentObj = JSON.parse(agentInfo)
  $('#hlhdyy-rt-0001-001').val(agentName)
  $('#hlhdyy-rt-0002-001').val(agentObj.doseInjection)
  $('#hlhdyy-rt-0003-001').val(agentObj.concValue)
  $('#hlhdyy-rt-0003-002').val(agentObj.concValueUnit)
  $('#hlhdyy-rt-12').val(agentInfo || '')
  layer.closeAll()
}
function setAgentUseMethod(flag){
  $('#hlhdyy-rt-13').val(flag)
}
function optHandler(obj){
 let optInfo = JSON.stringify(obj)
 $('#optInfo').val(optInfo)
}