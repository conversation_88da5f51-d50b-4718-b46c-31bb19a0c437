/**
 * 单病种结构化报告新标准格式相关方法
 */

; (function () {
  var instanceList = [];
  // 以下类型的当做wt=1处理,减少结构化框架的处理,该组件里其实就是input
  var transWtLikeInput = [1, 2, 24, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 29, 38, 39, 40];
  // 以下类型的当做wt=5处理,减少结构化框架的处理,该组件里其实就是radio
  var transWtLikeRadio = [16];
  // 以下类型为图像，不处理高度
  var wtIsImage = [9, 25, 26];
  // 以下类型按code值处理,确保信息是最新的检查信息
  var keepCodeValWt = [15, 16, 17, 18, 19, 20];
  window.HTMLElement = window.HTMLElement || Element;
  // 兼容IE8
  if (typeof Array.prototype.indexOf != 'function') {
    Array.prototype.indexOf = function(searchElement, fromIndex) {
      var k;
      if (this == null) {
        throw new TypeError('"this" is null or not defined');
      }
  
      var o = Object(this);
      var len = o.length >>> 0;
      if (len === 0) {
        return -1;
      }
  
      var n = fromIndex | 0;
  
      if (n >= len) {
        return -1;
      }
      k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
      while (k < len) {
        if (k in o && o[k] === searchElement) {
          return k;
        }
        k++;
      }
      return -1;
    };
  }

  var isTrace = location.href.indexOf('trace') > -1;
  var scriptCount = 0;
  var preURL = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
  window.initHtmlScript && delete window.initHtmlScript;
  window.rtStructure && delete window.rtStructure;
  function addScript(url, type, callBack){
    var origin = url.indexOf('http') === -1 ? location.origin : '';
    if(type === 'style') {
      var link = document.createElement('link');
      link.rel = "stylesheet";
      link.href = origin + (url.indexOf('/sreport') > -1 ? '' : preURL) + url;
      if(callBack !== undefined) {
        link.setAttribute('data-flag', 'curPattern');
      }
      document.getElementsByTagName('head')[0].appendChild(link);
      if(callBack !== undefined) {
        callBack();
      } else {
        scriptCount++;
      }
    } else {
      var scriptUrl = origin + (url.indexOf('/sreport') > -1 ? '' : preURL) + url;
      var existScript = document.querySelector('script[src="'+scriptUrl+'"]');
      if(existScript!== null) {
        if(callBack !== undefined) {
          callBack();
        } else {
          scriptCount++;
        }
        return;
      }
      var script = document.createElement('script');
      script.src = scriptUrl;
      script.setAttribute('defer','')
      if(callBack !== undefined) {
        script.setAttribute('data-flag', 'curPattern');
      }
      document.getElementsByTagName('head')[0].appendChild(script);
      script.onload = script.onreadystatechange = function() {
        if(callBack !== undefined) {
          callBack();
        } else {
          scriptCount++;
        }
      }
    }
  }
  var innerScriptArr = [
    {src: "/template-lib/plugins/jquery.min.js", type: 'script'},
    {src: "/template-lib/plugins/mathJax/tex-mml-chtml.js", type: 'script'},
    {src: "/template-lib/plugins/barcode.js", type: 'script'},
    {src: "/template-lib/plugins/qrcode.min.js", type: 'script'},
    {src: "/template-lib/plugins/jsBarcode.all.min.js", type: 'script'},
    {src: "/template-lib/plugins/crypto-js.min.js", type: 'script'},
    {src: "/template-lib/controls/publicCode.js", type: 'script'},
    {src: "/template-lib/controls/api.js", type: 'script'},
    {src: "/template-lib/utils/layerMsg/layerMsg.js", type: 'script'},
    {src: "/template-lib/utils/layerMsg/layerMsg.css", type: 'style'},
    {src: "/template-lib/plugins/dayjs.min.js", type: 'script'},
    {src: "/template-lib/utils/common/common.js", type: 'script'},
    {src: "/template-lib/utils/rtDialog.js", type: 'script'},
  ];
  for(var inIdx = 0; inIdx < innerScriptArr.length; inIdx++) {
    addScript(innerScriptArr[inIdx].src, innerScriptArr[inIdx].type);
  }

  var removeDisabled = [];   //移除禁用的元素
  // 提前加载模板内的js和CSS
  var templateScriptArr = [];
  var templateCount = 0;
  var templateOptArr = [];  //模板业务js
  function loadTemplateScriptAndCss(htmlContent) {
    var allInnerScriptAndCss = handlerTemplateScript(htmlContent);
    if(allInnerScriptAndCss && allInnerScriptAndCss.length) {
      templateScriptArr = allInnerScriptAndCss;
      for(var sIndex = 0; sIndex < templateScriptArr.length; sIndex++) {
        addScript(templateScriptArr[sIndex].src, templateScriptArr[sIndex].type, function() {
          templateCount++;
        });
      }
    }
  }
  
  // 兼容IE8
  if (typeof Array.prototype.forEach != 'function') {
    Array.prototype.forEach = function(callback){
      for (var i = 0; i < this.length; i++){
        callback.apply(this, [this[i], i, this]);
      }
    };
  }
  String.prototype.trim = function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  }

  if (!("classList" in document.documentElement)) {
    Object.defineProperty(HTMLElement.prototype, 'classList', {
      get: function() {
        var self = this;
        function update(fn) {
          return function(value) {
            var classes = self.className.split(/\s+/g),
            index = classes.indexOf(value);

            fn(classes, index, value);
            self.className = classes.join(" ");
          }
        }

        return {
          add: update(function(classes, index, value) {
            if (!~index) classes.push(value);
          }),

          remove: update(function(classes, index) {
            if (~index) classes.splice(index, 1);
          }),

          toggle: update(function(classes, index, value) {
            if (~index)
              classes.splice(index, 1);
            else
              classes.push(value);
          }),

          contains: function(value) {
            return !!~self.className.split(/\s+/g).indexOf(value);
          },

          item: function(i) {
            return self.className.split(/\s+/g)[i] || null;
          }
        };
      }
    });
  }

  function addListenerHandler(target, type, handler) {
    if(target.addEventListener){
      target.addEventListener(type, handler, false);
    }else if(target.attachEvent){
      target.attachEvent("on"+ type, handler);
    }else {
      target["on"+type] = handler;
    }
  }

  // 合并对象的方法兼容IE8
  function objMerge(target, source) {
    target = JSON.parse(JSON.stringify(target));
    for (var key in source) {
      target[key] = source[key];
    }
    return target;
  }

  // 移除元素
  function removeEle(ele) {
    if(ele.remove) {
      ele.remove();
    } else {
      ele.removeNode(true);
    }
  }

  // 删除带有.rt-hide的元素兼容IE8
  function removeEleBySelector(eleArr) {
    for(var i = 0; i < eleArr.length; i++) {
      var ele = eleArr[i];
      if(ele.classList.contains('rt-hide')) {
        eleArr.splice(i, 1);
        i--;
      }
    }
    return eleArr;
  }

  // 获取选中的radio的值，兼容IE8
  function getCheckedSelector(eleArr) {
    var node = null;
    for(var i = 0; i < eleArr.length; i++) {
      var ele = eleArr[i];
      if(ele.checked) {
        node = ele;
        break;
      }
    }
    return node;
  }

  // 获取所有控件属性
  function getWidgetAttr(code) {
    var _this = this;
    // rt-structure-data
    _this.widgetPropMap = [];
    var reg = /<script id="rt-structure-data">(.*)<\/script>/;
    var matchArr = code.match(reg);
    var srData = []
    if (matchArr && matchArr[1]) {
      srData = JSON.parse(matchArr[1]);
    }
    var widgetPropMap = sortNodeByTop.call(_this, srData);
    _this.widgetPropMap = widgetPropMap;
  }
  // 按节点的top值排序,由于多页的问题top做计算:原top+页面的高度
  function sortNodeByTop(data) {
    var _this = this;
    var pageProp = data[0] || {};
    // 通过dom节点属性遍历存储控件的属性
    var widgetProp = [];
    var dataMap = [];
    var groupIdMap = {};   //缓存同一组的单选/复选数据
    // var pageLi = querySelectorAll.call(_this, 'li.page');
    // nodeListToArray(pageLi).forEach(function (page, index) {
    // })
    var rtScDom = querySelectorAll('[rt-sc]', _this.ele);
    nodeListToArray(rtScDom).forEach(function (node, i) {
      var id = node.id;
      var pid = node.getAttribute('pid') || '';
      var sc = node.getAttribute('rt-sc');
      var scArr = sc.split(';');
      var scObj = {
        id: id,
        pid: pid,
        // rtScPageNo: index + 1,
      };
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          // scObj[key] = eval(decodeURIComponent(value));
          // scObj['groupId'] = node.getAttribute('name') || '';
          // 兼容旧版，itemList不存rt-sr
          if(key !== 'itemList') {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
            if(key === 'wt') {
              if(transWtLikeInput.indexOf(scObj[key]) > -1) {
                if(scObj[key] !== 2 || node.nodeName !== 'SELECT') {
                  scObj['oriWt'] = scObj[key];  //保留来自编辑器的wt，好区分使用
                  scObj[key] = 1;
                }
              }
              if(transWtLikeRadio.indexOf(scObj[key]) > -1) {
                scObj[key] = 5;
              }
            }
          } 
        }
      })
      if([4, 5].indexOf(scObj['wt']) > -1) {
        scObj['itemList'] = [];
        var groupId = node.getAttribute('name') || '';
        if(groupId) {
          scObj['groupId'] = groupId;
          if(!groupIdMap[groupId]) {
            var sameGroupId = nodeListToArray(querySelectorAll('[name="'+groupId+'"]', _this.ele));
            sameGroupId.forEach(function(sibEle, sl) {
              scObj['itemList'].push({
                id: sibEle.id,
                value: sibEle.getAttribute('value')
              })
            })
            groupIdMap[groupId] = scObj['itemList'];
          } else {
            scObj['itemList'] = groupIdMap[groupId];
          }
        }
      }
      widgetProp.push(scObj);
      if(scObj.name) {
        node.setAttribute('data-srname', scObj.name);
      }
      var computedTop = scObj.rtScPageNo > 1 ? scObj.top + (pageProp.pH || 900) : scObj.top;  //用于排序
      var widgetObj = objMerge(scObj, {computedTop: computedTop, pH: pageProp.pH || 900, value: ''});
      dataMap.push(widgetObj);
      _this.idAndDomMap[widgetObj.id] = widgetObj;
      // 编辑器存在多个id拼接的etId
      if(_this.isWriteByEditor) {
        if(id.indexOf(',') > -1) {
          id.split(',').forEach(function(idItem) {
            var etItem = objMerge(widgetObj, {id: idItem});
            _this.idAndDomMap[idItem] = etItem;
            dataMap.push(etItem);
          })
        }
      }
    })
    return dataMap;
  }

  // 判断控件类型
  function getWidgetType(widget) {
    var _this = this;
    var id = widget.id;
    var wt = _this.idAndDomMap[id] ? Number(_this.idAndDomMap[id].wt) : -1;
    return wt;
  }

  function nodeListToArray(nodeList) {
    var list = [];
    for(var i = 0; i < nodeList.length; i++) {
      list.push(nodeList[i]);
    }
    // return Array.prototype.slice.call(nodeList);
    return list;
  }

  function querySelector(selector, rootDom) {
    if(!rootDom && !this.ele) {
      return null;
    }
    return (rootDom || this.ele).querySelector(selector);
  }

  function querySelectorAll(selector, rootDom) {
    if(!rootDom && !this.ele) {
      return [];
    }
    return nodeListToArray((rootDom || this.ele).querySelectorAll(selector));
  }

  function setChildrenEnabled(parent) {
    var _this = this;
    querySelectorAll.call(_this, '[pid="' + parent.id + '"]').forEach(function (child) {
      var wt = getWidgetType.call(_this, child);
      if (wt === 0) {
        setChildrenEnabled.call(_this, child);
      }
      child.disabled = false;
      child.classList.remove('rt-disabled');
    });
  }

  function setChildrenDisabled(parent) {
    var _this = this;
    var parentId = parent.id;
    querySelectorAll.call(_this, '[pid="' + parentId + '"]').forEach(function (child) {
      var wt = getWidgetType.call(_this, child);
      // 单选按钮，复选框
      if ([4, 5].indexOf(wt) > -1) {
        child.checked = false;
        child.classList.remove('rt-checked');
      }
      // 输入框
      if (wt === 1) {
        // 保留在HTML写的默认值
        var hasHtmlDefaultVal;
        if (child.tagName === 'INPUT' && child.getAttribute('value')) {
          hasHtmlDefaultVal = true;
        }
        if (child.tagName === 'TEXTAREA' && child.innerText) {
          hasHtmlDefaultVal = true;
        }
        if (!hasHtmlDefaultVal) {
          child.value = '';
        }
      }
      if(!_this.diffConfig || !_this.diffConfig.nextWidgetEnable) {
        child.disabled = true;
        child.classList.add('rt-disabled');
      }
      var cList = querySelectorAll.call(_this, '[pid="' + child.id + '"]');
      if ((child.children && child.children.length) || (cList && cList.length)) {
        setChildrenDisabled.call(_this, child);
      }
    });
  }

  function init(noFirst) {
    var _this = this;
    runScriptContent.call(_this, _this.enterOptions.htmlContent);
    setValByCode.call(_this, publicCode);
    if(typeof noFirst !== 'boolean' || !noFirst) {
      if(window.initHtmlScript) {
        window.initHtmlScript(_this.ele)
      }
      // 编辑器的模板渲染
      if(_this.isWriteByEditor) {
        renderPatternScript.call(_this);
      }
      _this.ele && (_this.ele.style.display = 'block');
      // 模板渲染完成发送消息通知
      if(_this.ele && _this.ele.querySelector('.t-pg li')) {
        _this.eleProps = {
          height: _this.ele.querySelector('.t-pg li').scrollHeight || _this.ele.querySelector('.t-pg li').offsetHeight,
          width:  _this.ele.querySelector('.t-pg li').scrollWidth || _this.ele.querySelector('.t-pg li').offsetWidth,
        }
      }

      // 非预览下发通知，预览的在预览里loadScriptToShowText通知
      if(_this.enterOptions.type !== 'view') {
        resetPageStyle.call(_this);
        window.top.postMessage({
          message: 'srHtmlLoaded',
          data: {
            winName: _this.enterOptions.winName,
            eleProps: _this.eleProps || {},
          },
        }, '*');
        // 非独立打开结构化，通知结构化完成模板加载
        if(window.top !== window) {
          window.postMessage({
            message: 'srHtmlLoaded',
            data: {
              winName: _this.enterOptions.winName,
              eleProps: _this.eleProps || {},
            },
          }, '*');
        }
      }
      // 编辑器的模板渲染
      if(_this.isWriteByEditor) {
        renderWidgetAfterLoaded.call(_this);
      }
      window.commonSetAbnormalByRule({configType: 1}); // 根据输入判断阴阳性
    }
    initChildDisabled.call(_this);
    if(!isTrace) {
      querySelectorAll.call(_this, 'input[type="checkbox"].rt-sr-w:not([data-os="1"]), input[type="radio"].rt-sr-w:not([data-os="1"])').forEach(function (item) {
        // change不兼容IE8
        addListenerHandler(item, 'click', function (e) {
          // item.onchange = function (ev) {
          var ev = e || window.event;
          if(_this.enterOptions.type === 'view') {
            ev.preventDefault();
          }
          var target = ev.target || ev.srcElement;
          var type = getWidgetType.call(_this, target);
          var parentId = target.id;
          _this.onWidgetChange(type, ev);
          if (target.checked) {
            target.classList.add('rt-checked');
            setChildrenEnabled.call(_this, item);
          }
          if (type === 4 && !target.checked) {
            target.classList.remove('rt-checked');
            setChildrenDisabled.call(_this, target);
          }
          if (type === 5) {
            querySelectorAll.call(_this, '[name="' + target.name + '"]').forEach(function (radioItem) {
              if(!radioItem.checked){
                radioItem.classList.remove('rt-checked');
              }
              if(radioItem.getAttribute('id') === parentId && radioItem.checked) {
                return;
              }
              setChildrenDisabled.call(_this, radioItem);
            });
          }
        });
        // }
      });
      
      // 子节点填写了，父节点自动勾选上
      setParentValAfterChildIsVal.call(_this);

      querySelectorAll.call(_this, 'select.rt-sr-w:not([data-os="1"])').forEach(function (item) {
        addListenerHandler(item, 'change', function (e) {
          var ev = e || window.event;
          _this.onWidgetChange('select', ev);
        });
      });
      openChildPattern.call(_this);
    }
    if(isTrace) {
      // 添加痕迹标识
      var widget = querySelectorAll('.rt-sr-w:not([data-os="1"])', _this.ele);
      nodeListToArray(widget).forEach(function (node, i) {
        var id = node.id;
        var targetNode = node;
        addTraceFlagInDom(_this.idAndDomMap[id], targetNode);
      })
      addTraceHandler('.traceStatus', _this.idAndDomMap);
    }
    if(!_this.diffConfig || !_this.diffConfig.nextWidgetEnable) {
      // 延时是为了先加载模板出来，再处理节点
      // setTimeout(function() {
      for(var i = 0; i < removeDisabled.length; i++) {
        var childEle = $(_this.ele).find('[pid="'+removeDisabled[i]+'"][disabled]');
        if(!childEle || childEle.length === 0) {
          continue;
        }
        // 不含‘dis-remove-disabled’直接移除禁用(来源于模板内的业务)，表明父元素已填过
        childEle.each(function(ele) {
          if(!$(ele).hasClass('dis-remove-disabled')) {
            $(ele).removeAttr('disabled');
          }
        })
      }
      // }, 3000)
    }
    // setTimeout(function() {
    // }, _this.enterOptions.oldDataText ? 0 : 100)
    // 加载完成
    if(window.loadedHtmlByJsHandler && typeof window.loadedHtmlByJsHandler === 'function') {
      window.loadedHtmlByJsHandler(_this.ele);
    }

    // 转换特殊字符，如上下标
    if(_this.enterOptions.type !== 'view') {
      transMathJaxText();
    }
  }

  // 处理带子模板的数据做缓存其patDocId
  function setPatDocIdStorageFun() {
    if(location.href.indexOf('child=1') > -1) {
      return;
    }
    var _this = this;
    var arr = [];
    querySelectorAll.call(_this, '.rt-sr-w[data-pdid]:not([data-os="1"])').forEach(function (item) {
      var id = item.getAttribute('id');
      if(_this.idAndDomMap[id] && _this.idAndDomMap[id].value) {
        arr.push(_this.idAndDomMap[id].pdId);
      }
    });
    window.localStorage.setItem('sr_all_patDocId', JSON.stringify(arr));
  }

  // 子节点填写了，父节点自动勾选上
  function setParentValAfterChildIsVal() {
    var _this = this;
    if(!_this.diffConfig || !_this.diffConfig.nextWidgetEnable) {
      return;
    }
    $('.rt-sr-w[pid]:not([data-os="1"])').change(function() {
      var target = $(this);
      var type = getWidgetType.call(_this, this);
      if(([4,5].indexOf(type) > -1 && target.is(":checked")) || (type === 1 && target.val())) {
        setParentVal.call(_this, this);
      }
    })
  }

  // 给父节点赋值
  function setParentVal(curItem) {
    var _this = this;
    var pid = curItem.getAttribute('pid');
    querySelectorAll.call(_this, '[id="' + pid + '"]').forEach(function (child) {
      var wt = getWidgetType.call(_this, child);
      // 单选按钮，复选框
      if ([4, 5].indexOf(wt) > -1) {
        child.checked = true;
        child.classList.add('rt-checked');
      }
      child.disabled = false;
      child.classList.remove('rt-disabled');

      var lastLevelEle =  querySelectorAll.call(_this, '[pid="' + child.id + '"]');
      if (lastLevelEle && lastLevelEle.length) {
        setParentVal.call(_this, child);
      }
    });
  }

  // 赋值公共节点的值,对应字段名取值
  function setValByCode(noValiVal) {
    var _this = this;
    var isSaveData = _this.enterOptions.resultData && _this.enterOptions.resultData.length;  //保存过
    if(!_this.enterOptions || !_this.enterOptions.publicInfo) {
      return;
    }
    var fromWebpacs = _this.enterOptions.publicInfo.webpacs === '1';  //来自病理pacs
    var resetCodeStr = $(_this.ele).find('[reset-code]').attr('reset-code') || '';  //是否一直按code重置为最新的值
    var resetCodes = resetCodeStr ? resetCodeStr.split(',') : [];  //多个
    // 审核的报告就情况重置的
    var reportStatus = _this.enterOptions.publicInfo.reportStatus;
    var examStatus = _this.enterOptions.publicInfo.examStatus;
    if((reportStatus && reportStatus === '70') || (!fromWebpacs && !reportStatus && examStatus === '70')) {
      resetCodes = [];
    }
    querySelectorAll.call(_this, '[code]').forEach(function (item, i){
      var id = item.id;
      var value = _this.idAndDomMap[id].value;
      var type = getWidgetType.call(_this, item);
      var codeStr = item.getAttribute('code');
      var originWt = Number(item.getAttribute('wt')) || '';  //原控件wt属性，非替代1后的
      // 保存过，且不属于纯文本，条形码、二维码的不处理
      if(isSaveData && ([0, 6, 7].indexOf(type) === -1 && keepCodeValWt.indexOf(originWt) === -1 && resetCodes.indexOf(codeStr) === -1)) {
        return;
      }
      if((typeof noValiVal !== 'boolean' || !noValiVal) && value && ([0, 6, 7].indexOf(type) === -1 && keepCodeValWt.indexOf(originWt) === -1 && resetCodes.indexOf(codeStr) === -1)) {
        return;
      }
      // 病理pacs页面，不处理101检查项目字段，都是模板内的自选项目
      if(fromWebpacs && codeStr === '101' && item.getAttribute('init-val') !== '1') {
        return;
      }
      // 单选
      if(type === 5) {
        var radioName = item.getAttribute('name');
        if(querySelectorAll.call(_this, '[name="'+radioName+'"]:checked').length) {
          return;
        }
      }
      
      if(codeStr) {
        var codeList = codeStr.split(',');
        for(var cI = 0; cI < codeList.length; cI++) {
          var code = codeList[cI];
          if(publicCode[code]) {
            var widget = item;
            var flag = false;
            var codeName = publicCode[code].split(',');
            for(var i = 0; i < codeName.length; i++) {
              var resVal = _this.enterOptions.publicInfo[codeName[i]];
              if(typeof resVal === 'string') {
                var codeValMap = {
                  '4': {'0': '未婚', '1': '已婚'},
                  '26': {'0': '未生育', '1': '已生育', '2': '未知'},
                  '31': {'0': '未月经', '1': '正常月经', '2': '已绝经', '3': '未知'},
                  '131': {'0': '否', '1': '是'},
                  '310': {'0': '否', '1': '是'},
                  '311': {'0': '否', '1': '是'},
                  '317': {'0': '否', '1': '是'},
                }
                if(codeValMap[code]) {
                  resVal = codeValMap[code][resVal] || ''
                }
                if(resVal) {
                  if((type === 5 || type === 4) && item.getAttribute('value') !== resVal) {
                    continue;
                  }
                  var formatVal = widget.getAttribute('val-format');
                  if(formatVal) {
                    resVal = dayjs(resVal).format(formatVal);
                  }
                  _this.idAndDomMap[id].value = resVal;
                  setFormItemValue.call(_this, widget, resVal);
                  flag = true;
                  break;
                }
              }
            }
            if(flag) {
              break;
            }
          }
        }
      }
    })
  }

  // 将单选和复选下的项置为disabled
  function initChildDisabled(dom) {
    var _this = this;
    if(_this.diffConfig && _this.diffConfig.nextWidgetEnable) {
      return;
    }
    var domList = !dom ? querySelectorAll.call(_this, 'input[type="checkbox"].rt-sr-w:not([data-os="1"]), input[type="radio"].rt-sr-w:not([data-os="1"])') : 
    querySelectorAll.call(_this, 'input[type="checkbox"].rt-sr-w:not([data-os="1"]), input[type="radio"].rt-sr-w:not([data-os="1"])', dom);
    domList.forEach(function (item, i) {
      var type = getWidgetType.call(_this, item);
      var parentId = item.id;
      if (item.checked) {
        setChildrenEnabled.call(_this, item);
      }
      if (type === 4 && !item.checked) {
        setChildrenDisabled.call(_this, item);
      }
      if (type === 5) {
        var radioVal = getCheckedSelector(querySelectorAll.call(_this, '[name="' + item.name + '"]')) ? 
          getCheckedSelector(querySelectorAll.call(_this, '[name="' + item.name + '"]')).value : '';
        querySelectorAll.call(_this, '[name="' + item.name + '"]').forEach(function (radioItem) {
          // if(radioItem.getAttribute('id') === parentId) {
          //   return;
          // }
          if(radioVal !== radioItem.value) {
            setChildrenDisabled.call(_this, radioItem);
          }
        });
      }
    });
  }

  function setWidgetInvalidStyle(widget) {
    var _this = this;
    // 病理pacs不处理必填标红
    if(_this.enterOptions && _this.enterOptions.publicInfo && _this.enterOptions.publicInfo.webpacs === '1') {
      return;
    }
    widget.className += ' rt-sr-inv';
    if(_this.isWriteByEditor && $(widget).closest('.w-con').length) {
      $(widget).closest('.w-con').addClass('rt-sr-inv');
    }
  }

  function isWidgetValid(widget) {
    var _this = this;
    var widgetType = getWidgetType.call(_this, widget);
    if (widgetType === 1 && widget.value.trim() === '') {
      return false;
    }
    if(widgetType === 0) {
      checkValid.call(_this, widget);
    }
    // if (widgetType === 4 && !widget.checked) {
    //   return false;
    // }
    if (widgetType === 4 || widgetType === 5) {
      var radioGroupName = widget.name;
      var radioGroup = getCheckedSelector(removeEleBySelector(querySelectorAll.call(_this, '[name="'+radioGroupName+'"]')));
      return radioGroup;
    }
    if (widgetType === 2 && widget.value.trim() === '') {
      return false;
    }
    return true;
  }

  // 消息提示框
  function $wfMessage(option) {
    var defaultOption = {
      "type":"err",  //类型，err,success,warn
      "position":"top-center",   //位置，top-left,top-center,top-right,center-left,center-center,center-right,bottom-left,bottom-center,bottom-right
      "showicon":true,  //是否显示图标
      "content":"提示",
      "shadow":false,
      "shadowclickclose":true,
      "autoclose":true,
    }
    option = $.extend(defaultOption, option)
    if(WfMsg) {
      WfMsg(option)
    }
  }

  function setInvalidError(widget) {
    var _this = this;
    setWidgetInvalidStyle.call(_this, widget);
    var widgetId = widget.id;
    var errorMsg = '控件' + (widgetId) + '为必填项';
    _this.errorMsg = errorMsg;
    if($wfMessage) {
      var name = _this.idAndDomMap[widgetId] && _this.idAndDomMap[widgetId].name ? 
        _this.idAndDomMap[widgetId].name.replace('label', '') : '';
      $wfMessage({
        // content: '请填写必填项'
        content: !_this.enterOptions.oldDataText && name ? ('【' + name + '】不能为空') : '请填写必填项'
      });
    }
    window.top.postMessage({
      message: 'valiResponse',
      data: {isVail: false}
    }, '*');
    if(window.errorCallBack) {
      window.errorCallBack(widgetId);
    }
    if (widget.scrollIntoView) {
      widget.scrollIntoView({behavior: 'smooth', block: 'nearest'}); // 定位到提醒项
    }
    throw new Error(errorMsg);
  }

  // 当父元素为必填时校验子元素填写是否符合要求
  function checkChildrenValid(widget, root) {
    var _this = this;
    var children = removeEleBySelector(querySelectorAll.call(_this, '[pid="' + (widget.id) + '"]'));
    var requiredChildren = removeEleBySelector(querySelectorAll.call(_this, '[pid="' + (widget.id) + '"][rt-req]'));
    if (children.length > 0) {
      if (requiredChildren.length > 0) {
        requiredChildren.forEach(function (child) {
          var wt = getWidgetType.call(_this, child);
          if(wt === 5 && !child.checked) {
            return;
          }
          checkChildrenValid.call(_this, child, child);
        });
      } else {
        var results = [];
        children.forEach(function (child) {
          if(isWidgetValid.call(_this, child)) {
            results.push(child);
          }
        })
        if (results.length > 0) {
          // results.forEach(function (res) {
          //   checkChildrenValid.call(_this, res, res);
          // });
        } else {
          setInvalidError.call(_this, root);
        }
      }
    } else {
      if (!isWidgetValid.call(_this, widget)) {
        setInvalidError.call(_this, root);
      }
    }
  }

  function checkValid(widget) {
    var _this = this;
    if(widget.className.indexOf('rt-hide') > -1) {
      return;
    }
    querySelectorAll.call(_this, '.rt-sr-inv').forEach(function (widget) {
      widget.className = widget.className.replace('rt-sr-inv', '');
    });
    checkChildrenValid.call(_this, widget, widget);
  }

  // 当填了父元素，且本身为必填项时校验
  function checkSelfValid(widget) {
    var _this = this;
    if(widget.className.indexOf('rt-hide') > -1) {
      return;
    }
    querySelectorAll.call(_this, '.rt-sr-inv').forEach(function (widget) {
      widget.className = widget.className.replace('rt-sr-inv', '');
    });
    var parentId = widget.getAttribute("pid");
    var parentWidgetRequired = querySelector.call(_this, '[id="' + parentId + '"][rt-req]');
    if(parentWidgetRequired && parentWidgetRequired.length > 0) {
      return;
    }
    var parentWidget = querySelector.call(_this, '[id="' + parentId + '"]');
    if(['checkbox', 'radio'].indexOf(parentWidget.type) > -1) {
      if(isWidgetValid.call(_this, parentWidget) && parentWidget.checked && !isWidgetValid.call(_this, widget)) {
        setInvalidError.call(_this, widget);
      }
    } else {
      // if (isWidgetValid.call(_this, parentWidget) && !isWidgetValid.call(_this, widget)) {
      //   setInvalidError.call(_this, widget);
      // }
      if (isWidgetValid.call(_this, parentWidget)) {
        var widgetType = getWidgetType.call(_this, widget);
        if (widgetType === 0) {
          checkValid.call(_this, widget);
        } else {
          if(!isWidgetValid.call(_this, widget)) {
            setInvalidError.call(_this, widget);
          }
        }
      }
    }
  }

  // 对二次确认的控件进行提示
  function checkConfirm(params) {
    var _this = this;
    var confirmTipIds = _this.diffConfig.confirmTipIds;
    var passFlag = true;
    var noPassName = '';
    for(var i = 0; i < confirmTipIds.length; i++) {
      var selector = confirmTipIds[i];
      var node = $(_this.ele).find(selector);
      if(node.length) {
        var nodeType = getWidgetType.call(_this, node[0]);
        var id = node.attr('id');
        if([4, 5].indexOf(nodeType) > -1) {
          if(!node[0].checked) {
            passFlag = false;  //未填，进行二次确认
            noPassName = _this.idAndDomMap[id] ? _this.idAndDomMap[id].name : '';
            break;
          }
        } else {
          if(!$(node).val()) {
            passFlag = false;  //未填，进行二次确认
            noPassName = _this.idAndDomMap[id] ? _this.idAndDomMap[id].name : '';
            break;
          }
        }
      }
      
    }
    if(!passFlag) {
      // confirmWidget验证失败的控件
      window.top.postMessage({
        message: 'valiResponse',
        data: {isVail: false, type: 'confirmTip', typeName: '二次确认校验', confirmWidget: noPassName, params}
      }, '*');
      throw new Error('二次校验中');
    }
  }

  // 导出结果集-保存用
  function exportStructureData(toRedis, params) {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    _this.errorMsg = '';
    if(!toRedis) {
      // 需要二次确认的选填项，值为空时提示
      if((!params || !params.ignoreConfirmTip) && _this.diffConfig && _this.diffConfig.confirmTipIds && _this.diffConfig.confirmTipIds.length) {
        checkConfirm.call(_this, params);
      }
      querySelectorAll.call(_this, '[rt-req]').forEach(function (widget) {
        if(widget.getAttribute('pid')) {
          return;
        }
        checkValid.call(_this, widget);
      });
      querySelectorAll.call(_this, '[rt-req][pid]').forEach(function (widget) {
        checkSelfValid.call(_this, widget);
      });
    }
    var parentList = querySelectorAll.call(_this, '.rt-sr-w:not([data-os="1"])');
    for(var i = 0; i < parentList.length; i++) {
      var wItem = parentList[i];
      if(wItem.getAttribute('pid') || wItem.classList.contains('rt-hide')) {
        parentList.splice(i, 1);
        i--;
      }
    }
    _this.curKeyValData = {};
    function collectChildren(parentList) {
      var pList = [];
      parentList.forEach(function (parent) {
        if(parent.classList.contains('rt-hide')) {
          return;
        }
        var parentItem = {
          id: parent.id,
          pid: parent.getAttribute('pid') || ''
        }
        var widgetType = getWidgetType.call(_this, parent);
        if ([4, 5].indexOf(widgetType) > -1) {
          if (!parent.checked) {
            return;
          }
          parentItem.val = parent.value || '';
          parentItem.desc = idAndDomMap[parentItem.id].desc || '';
          parentItem.htmlValue = idAndDomMap[parentItem.id].htmlValue || '';
          parentItem.name = idAndDomMap[parentItem.id].name || '';
          parentItem.code = idAndDomMap[parentItem.id].code || '';
          pList.push(parentItem);
          _this.curKeyValData[parent.id] = parentItem;
          _this.curKeyValData[parent.id].value = parentItem.val;
          // var children = querySelectorAll.call(_this, '[pid="' + parentItem.id + '"]:not(.rt-hide)');
          var children = removeEleBySelector(querySelectorAll.call(_this, '[pid="' + parentItem.id + '"]'));
          if (children.length > 0) {
            parentItem.child = collectChildren.call(_this, children);
          }
        } else {
          parentItem.val = widgetType === 0 ? (parent.innerText || parent.value) : parent.value;
          if($(parent).find('.isk').length) {
            parentItem.val = parentItem.val.replace('*', '');
          }
          parentItem.desc = widgetType === 0 ? 
            ((idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].desc : '') || parentItem.val) : (idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].desc : '');
          parentItem.name = idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].name : '';
          parentItem.htmlValue = idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].htmlValue : '';
          if(idAndDomMap[parentItem.id] && idAndDomMap[parentItem.id].code) {
            parentItem.code = idAndDomMap[parentItem.id].code;
          }
          // var children = querySelectorAll.call(_this, '[pid="' + parentItem.id + '"]:not(.rt-hide)');
          var children = removeEleBySelector(querySelectorAll.call(_this, '[pid="' + parentItem.id + '"]'));
          if (children.length > 0) {
            parentItem.child = collectChildren.call(_this, children);
          }
          // 处理签名图片
          if (widgetType == '26' && idAndDomMap[parentItem.id]) {
            parentItem.val = idAndDomMap[parentItem.id].value || '';
          }
          // /\S/判断不收集纯空格节点
          if(parentItem.val && /\S/.test(parentItem.val) && (!parentItem.child || parentItem.child.length > 0 || (widgetType !== 0 && !parentItem.child.length))) {
            pList.push(parentItem);
            _this.curKeyValData[parent.id] = parentItem;
            _this.curKeyValData[parent.id].value = parentItem.val;
          }
        }
      });
      return pList;
    }
    var attrData = collectChildren.call(_this, parentList);
    // var resultData = sortDataByTopHandler.call(_this, attrData);
    // saveSrResult(resultData);
    _this.curResultData = attrData;

    // 导出结果同时生成必要的报告标准数据存库用
    if(_this.isWriteByEditor) {
      saveReportInfoByPattern.call(_this, _this.curKeyValData);
    }

    return attrData;
  }

  // 结果集排序
  function sortDataByTopHandler(attrData) {
    var _this = this;
    getWidgetAttr.call(_this, _this.ele.innerHTML);
    var widgetProp = _this.widgetPropMap;
    var orderPropMap = [];
    widgetProp.forEach(function(d){
      orderPropMap.push(d.id);
    })
    var sortHandler = function(data) {
      data.sort(function(a,b){
        return orderPropMap.indexOf(a.id) - orderPropMap.indexOf(b.id);
      });
    }
    sortHandler(attrData);
    attrData.forEach(function(item){
      if(item.child && item.child.length) {
        sortHandler(item.child);
      }
    })
    return attrData;
  }
  // 通过标准code获取id
  function getItemIdByCodeHandler(idAndDomMap, code) {
    for(var key in idAndDomMap) {
      if(idAndDomMap[key].code !== code) {
        continue;
      }
      if(idAndDomMap[key].code === code) {
        return key;
      }
    }
    return '';
  }

  /**      回显部分            */

  // 列举id值和控件的关系属性表
  function idAndDomMapHandler(htmlContent, resultData) {
    var _this = this;
    _this.idAndDomMap = {};
    getWidgetAttr.call(_this, htmlContent);
    var widgetPropData = JSON.parse(JSON.stringify(_this.widgetPropMap));
    // var idAndDomMap = _this.idAndDomMap;
    var lastValMap = {};
    var handler = function (data, lastChild, parentLastVal) {
      var attrValue = {};
      var delIndex = -1;
      var lastChildValList = null;
      data.forEach(function (item, i) {
        var getItemIdByCode = '';
        // ai诊断，不存在id，但存在标准字段code
        if(!item.id && item.code) {
          getItemIdByCode = getItemIdByCodeHandler(_this.idAndDomMap, item.code)
        }
        attrValue = _this.idAndDomMap[item.id || getItemIdByCode] || {};
        if(item.id || getItemIdByCode) {
          // _this.idAndDomMap[item.id || getItemIdByCode].value = item.val;
          // _this.idAndDomMap[item.id || getItemIdByCode].lastVal = item.lastVal;
          _this.idAndDomMap[item.id || getItemIdByCode] = objMerge(attrValue, {value: item.val, lastVal: item.lastVal, htmlValue: item.htmlValue});
          // 预览下，有样式值的取样式值，req17332
          // if(_this.enterOptions.type === 'view' && item.htmlValue) {
          //   _this.idAndDomMap[item.id || getItemIdByCode].value = item.htmlValue;
          // }
          var lastVal = item.lastVal;
          var lastValArr = null;
          if(attrValue.itemList) {
            delete _this.idAndDomMap[item.id || getItemIdByCode].style;
            if(lastChild && lastChild.length && !lastVal) {
              var tempItems = JSON.parse(JSON.stringify(attrValue.itemList));
              lastChild.forEach(function(lVal) {
                tempItems.forEach(function(itemCld, lIdx) {
                  if(lVal.id === itemCld.id && lVal.val) {
                    lastValArr ? lastValArr.push(lVal.val) : lastValArr = [lVal.val];
                    tempItems.splice(lIdx, 1);
                    lastValMap[item.id || getItemIdByCode] ? 
                      lastValMap[item.id || getItemIdByCode].push(lVal.id) : 
                      lastValMap[item.id || getItemIdByCode] = [lVal.id];
                    return;
                  }
                })
              })
            }
          }
          if(attrValue.wt === 5 && lastValArr && lastValArr.length === 1 && lastValArr[0] === item.val) {
            lastValArr = null;
          }
          if(lastValArr || lastVal !== undefined) {
            if(lastValArr) {
              _this.idAndDomMap[item.id || getItemIdByCode]['lastVal'] = lastValArr.join('、') || '-';
            } else {
              _this.idAndDomMap[item.id || getItemIdByCode]['lastVal'] = lastVal || '-';
            }
          }
          if(item.child && item.child.length) {
            handler(item.child, item.lastChild, item.lastVal);
          }
        }
      });
    }
    handler(resultData);

    for(var key in lastValMap) {
      if(!_this.idAndDomMap[key].lastVal) {
        continue;
      }
      var arr = lastValMap[key];
      var itemList = _this.idAndDomMap[key].itemList;
      var itemCheckArr = JSON.parse(JSON.stringify(arr));
      var itemCheckLen = 0;
      for(var i = 0; i < itemList.length; i++) {
        if(_this.idAndDomMap[itemList[i].id] && _this.idAndDomMap[itemList[i].id].value) {
          var index = itemCheckArr.indexOf(itemList[i].id);
          itemCheckLen++;
          if(index > -1) {
            itemCheckArr.splice(index, 1);
          }
        }
      }
      if(itemCheckLen===arr.length && !itemCheckArr.length) {
        delete _this.idAndDomMap[key].lastVal;
      }
    }
    delete _this.idAndDomMap[''];
    delete _this.idAndDomMap['undefined'];
  }

  // 创建原报告内容
  function createContainer(htmlContent, options) {
    var _this = this;
    // 提取模板编辑器模板的属性
    if(_this.isWriteByEditor) {
      getWidgetInnerData.call(_this, htmlContent);
    }

    var container = document.createElement('div');
    htmlContent = htmlContent.replace(/<script.*?<\/script>/ig, '');
    htmlContent = htmlContent.replace(/<link.*?>/ig, '');
    container.innerHTML = htmlContent;
    $(container).find('input[type="text"]:not([autocomplete="on"])').attr('autocomplete', 'off');
    container.style.display = "none";
    _this.ele && (_this.ele.style.display = "none");
    if(options.type === 'view') {
      nodeListToArray(container.querySelectorAll('.w-isk')).forEach(function (ele) {
        removeEle(ele)
      })
    }
    $(container).find('input[type="checkbox"],input[type="radio"]').each(function(i, ck) {
      if(location.href.indexOf('/template-lib/layouts') > -1) {
        $(ck).parent('label[for]').addClass('rt-cus-ckr');
        if($(ck).parent('label[for]').find('.rt-cus').length===0){
          $(ck).after($('<span class="rt-cus"></span>'))
        }
      }
    })
    return container;
  }
  
  // 回显报告内容进行编辑
  // htmlContent, resultData, srcUrl, examInfo, type, loadViewScript
  function rebuildStructureReport(options) {
    var _this = this;
    if(scriptCount + templateCount < innerScriptArr.length + templateScriptArr.length) {
      // 判断额外js是否加载完成
      setTimeout(function() {
        rebuildStructureReport.call(_this, options)
      }, 10)
      return;
    }
    var options = options || {};
    var htmlContent = options.htmlContent || '';
    var resultData = options.resultData || [];
    var srcUrl = options.srcUrl || preURL || '';
    var examInfo = options.examInfo || {};
    var type = options.type || '';
    var loadViewScript = options.loadViewScript || false;
    _this.enterOptions = options;
    _this.examInfo = examInfo;
    _this.srcUrl = srcUrl;
    _this.isWriteByEditor = htmlContent.indexOf('is-rt-editor="1"') > -1;  //通过编辑器生成的模板
    var container = createContainer.call(_this, htmlContent, options);
    _this.ele && (_this.ele.innerHTML = container.innerHTML);
    if(resultData && resultData.length) {
      _this.ele && _this.ele.setAttribute('data-edited', true);  //编辑过的标识
    }
    removeEle(container);
    idAndDomMapHandler.call(_this, htmlContent, resultData);
    var idAndDomMap = _this.idAndDomMap || {};
    var widget = querySelectorAll('.rt-sr-w:not([data-os="1"])', _this.ele);
    _this.ele && _this.ele.setAttribute('isView', type === 'view');
    if(_this.enterOptions && _this.enterOptions.publicInfo && _this.enterOptions.publicInfo.entryType) {
      _this.ele && _this.ele.setAttribute('entry-type', _this.enterOptions.publicInfo.entryType);
    }
    // 模板编辑器生成的模版元素自行滚动
    if(_this.isWriteByEditor) {
      _this.ele.style.overflow = 'auto';
    }
    var setValInParent = [];   //子节点有值的父节点
    removeDisabled = [];   //移除禁用的元素
    nodeListToArray(widget).forEach(function (node, i) {
      var id = node.id;
      var targetNode = node;
      if(idAndDomMap[id] && idAndDomMap[id].pdId) {
        targetNode.setAttribute('data-pdid', idAndDomMap[id].pdId);
      }
      if(idAndDomMap[id] && idAndDomMap[id].onlyShow==='1') {
        targetNode.setAttribute('data-os', '1');
      }
      // 模板管理模板预览时使用示例数据
      var wtAttr = getWtAttrByIdFromWtData.call(_this, id);
      // 将存在图像中为base64的节点地址回显
      if(wtAttr && wtAttr.imgUrl && wtAttr.imgUrl.indexOf('data:image') > -1) {
        targetNode.setAttribute('src', wtAttr.imgUrl);
        $(targetNode).show();
      }
      if(_this.enterOptions.previewTemplate && idAndDomMap[id]) {
        // 二维码、条形码
        if(!idAndDomMap[id].value && [6, 7].indexOf(idAndDomMap[id].wt) > -1) {
          idAndDomMap[id].value = wtAttr && wtAttr.preText ? wtAttr.preText : '123456';
        }
        // 静态图9，报告图25，签名图26，此类图不存在base64的或无图的，先用temp做预览
        if(wtAttr && 
          ((wtAttr.imgUrl && wtAttr.imgUrl.indexOf('temp') > -1) || 
          (wtIsImage.indexOf(idAndDomMap[id].wt) > -1 && 
          (!wtAttr.imgUrl || wtAttr.imgUrl.indexOf('data:image') === -1)))
        ) {
          if(idAndDomMap[id].wt === 25) {
            $(targetNode).find('.rpt-image').attr('src', './template-lib/teditorResources/temp.png');
            $(targetNode).find('.rpt-image').show();
          } else {
            $(targetNode).attr('src', './template-lib/teditorResources/temp.png');
            $(targetNode).show();
          }
        }
      }
      if (idAndDomMap[id] && idAndDomMap[id].value) {
        // 赋值
        setFormItemValue.call(_this, targetNode, idAndDomMap[id].value);
        targetNode.removeAttribute('disabled');
        if(idAndDomMap[id].code) {
          targetNode.setAttribute('code', idAndDomMap[id].code);
        }
        removeDisabled.push(id)
        if(idAndDomMap[id].pid && !idAndDomMap[idAndDomMap[id].pid].pid) {
          setValInParent.push(idAndDomMap[id].pid)
        }
      } else {
        // 非设备数据且保存过将默认值清空
        if(options.resDataSource !== '1' && resultData && resultData.length) {
          if(_this.isWriteByEditor) {
            // 模板编辑器生成的模板只有在编辑页面才清空
            if(_this.enterOptions.type !== 'view') {
              setFormItemValue.call(_this, targetNode, '');
            }
          } else {
            setFormItemValue.call(_this, targetNode, '');
          }
        }
        if(idAndDomMap[id] && idAndDomMap[id].code) {
          targetNode.setAttribute('code', idAndDomMap[id].code);
        }

        /**
         * 非控件预览格式下，存在nt(空值预览文本)则显示
         * 当options.setNull=true时，未填写的内容显示‘无’--旧数据格式的判断
         * 是否有父节点，有的才做处理,或者没有父节点，但是以文本格式预览的节点
         * 有父节点，但是父节点时纯文本的
         * 单选项先判断其他项是否有值
         */
        // && ((idAndDomMap[id].pid && !idAndDomMap[idAndDomMap[id].pid].pid ) || (!idAndDomMap[id].pid && !idAndDomMap[id].pvf))
        var condFlag = idAndDomMap[id] && options.type === 'view' && (options.setNull || idAndDomMap[id].nt) && setValInParent.indexOf(idAndDomMap[id].pid) === -1
        && ((idAndDomMap[id].pid && (idAndDomMap[idAndDomMap[id].pid].wt === 0 || !idAndDomMap[idAndDomMap[id].pid].pid)) || (!idAndDomMap[id].pid && !idAndDomMap[id].pvf))
        && ([4,5].indexOf(idAndDomMap[id].wt) == -1 || !findSiblingVal(idAndDomMap, id));  
        if(condFlag) {
          delete idAndDomMap[id].pvt;
          idAndDomMap[id].value =  idAndDomMap[id].nt || (options.setNull ? '无' : '');
          idAndDomMap[id].tempValue = true;
          idAndDomMap[id].pid && setValInParent.push(idAndDomMap[id].pid)
        }
      }
    })
    handlerOptScript.call(_this, _this.enterOptions.htmlContent, type, loadViewScript);
    // handlerScript.call(_this, _this.enterOptions.htmlContent, type, loadViewScript)
  }

  // 找出单选项中是否有选中的
  function findSiblingVal(idAndDomMap, id) {
    var bool = false;
    var itemList = idAndDomMap[id].itemList;
    if(itemList && itemList.length) {
      for(var i = 0; i < itemList.length; i++) {
        if(itemList[i] && idAndDomMap[itemList[i].id] && idAndDomMap[itemList[i].id].value) {
          bool = true;
          break;
        }
      }
    }
    return bool;
  }

  // 加载模板内部操作的脚本
  function handlerOptScript(content, type, loadViewScript) {
    var _this = this;
    if(templateOptArr && templateOptArr.length) {
      var loadCount = 0;
      for(var i = 0; i < templateOptArr.length; i++) {
        addScript(templateOptArr[i].src, 'script', function(){
          loadCount++;
        });
      }
      var nextStepHandler = function() {
        if(loadCount < templateOptArr.length) {
          // 判断额外js是否加载完成
          setTimeout(function() {
            nextStepHandler();
          }, 10)
          return;
        }
        if(loadCount === templateOptArr.length) {
          if(type === 'view') {
            if(loadViewScript) {
              init.call(_this);
            }
            setTimeout(function(){
              loadScriptToShowText.call(_this);
            }, 100)
          } else {
            init.call(_this);
          }
        }
      }
      nextStepHandler();
      
    } else {
      if(type === 'view') {
        if(loadViewScript) {
          init.call(_this);
        }
        setTimeout(function(){
          loadScriptToShowText.call(_this);
        }, 100)
      } else {
        init.call(_this);
      }
    }
  }

  // 加载模板内的脚本和css
  function handlerTemplateScript(content) {
    if(!content) {
      return [];
    }
    var _this = this;
    templateScriptArr = [];
    templateCount = 0;
    templateOptArr = [];
    templateOptCount = 0;
    querySelectorAll('[data-flag="curPattern"]', document).forEach(function(lastNode) {
      removeEle(lastNode);
    })
    var cssReg = /rel="stylesheet".*?\/?>/ig;
    var cssList = content.match(cssReg);
    var scriptReg = /<script src\=.*?<\/script>/ig;
    var scriptList = content.match(scriptReg);
    var allInnerScriptAndCss = [];
    if(cssList && cssList.length) {
      for(var i = 0; i < cssList.length; i++) {
        var hrefReg = /(href\=\").*(?=\")/ig;
        var urls = cssList[i].match(hrefReg);
        if(urls && urls.length) {
          var url = urls[0].replace('href="', '');
          url = url.replace(/[.]+\//g, '/');
          allInnerScriptAndCss.push({src: url, type:'style'});
        }
      }
    }
    if(scriptList && scriptList.length) {
      for(var i = 0; i < scriptList.length; i++) {
        var sItem = scriptList[i];
        var srcReg = /(src\=\").*(?=\")/ig;  //IE不兼容?<=
        var urls = sItem.match(srcReg);
        if(urls && urls.length) {
          var url = urls[0].replace('src="', '');
          url = url.replace(/[.]+\//g, '/');
          // var src = url + '?v=' + new Date().getTime();
          var src = url;
          if(sItem.indexOf('/views/') > -1 || sItem.indexOf('/diseaseFileStore/js') > -1) {
            templateOptArr.push({src: src, type:'script'});
          } else {
            allInnerScriptAndCss.push({src: src, type:'script'});
          }
        }
      }
    }

    return allInnerScriptAndCss;
  }

  // 执行内容js
  function runScriptContent(content) {
    if(!content) {
      return;
    }
    var _this = this;
    var scriptContentReg = /<script.*?>([\s\S]*?)(?=<\/script>)/ig;  //内部script
    var scriptContentRegList = content.match(scriptContentReg);
    // 忽略执行的脚本，来自模板编辑器的数据存储
    var excludeScript = ['rt-wt-data', 'rt-pg-data', 'rt-wt-rule'];  //rt-wt-event为脚本区域
    if(scriptContentRegList && scriptContentRegList.length) {
      for(var k = 0; k < scriptContentRegList.length; k++) {
        if($(scriptContentRegList[k]).attr('src')) {
          continue;
        }
        // 忽略执行的脚本，来自模板编辑器的数据存储
        if(excludeScript.indexOf($(scriptContentRegList[k]).attr('id')) > -1) {
          continue;
        }
        var code = scriptContentRegList[k].replace(/<script.*?>/ig, '');
        if(code) {
          if($(scriptContentRegList[k]).attr('id') === 'rt-wt-event') {
            _this.widgetEvent = code;
          } else {
            eval(code);  //执行脚本
          }
        }
      }
    }
  } 

  // 动态加载css,兼容IE8
  function handlerCss(content) {
    var _this = this;
    var cssReg = /rel="stylesheet".*?\/?>/ig;
    var cssList = content.match(cssReg);
    var resHtml = content;
    var headContent = document.querySelector('head').innerHTML;
    headContent = headContent.replace(/\/sreport/ig, '');
    if(cssList && cssList.length) {
      for(var i = 0; i < cssList.length; i++) {
        var hrefReg1 = /(href\=\").*(?=\")/ig;  //IE不兼容?<=
        var url = cssList[i].match(hrefReg1);
        if(url && url.length && headContent.indexOf(url[0]) > -1) {
          cssList.splice(i, 1);
          i--;
        }
      }
      for(var i = 0; i < cssList.length; i++) {
        var hrefReg = /(href\=\").*(?=\")/ig;
        var urls = cssList[i].match(hrefReg);
        if(urls && urls.length) {
          var url = urls[0].replace('href="', '');
          var htmlLink = querySelector('[href="'+url+'"]', document);
          if(htmlLink) {
            removeEle(htmlLink);
          }
          var link = document.createElement('link');
          link.setAttribute('data-flag', 'curPattern');
          link.rel = "stylesheet";
          url = url.replace(/[.]+\//g, '/');
          link.href = _this.srcUrl + url + '?v=' + new Date().getTime();
          document.getElementsByTagName('head')[0].appendChild(link);
        }
      }
    }
  }

  /**
   * 提取结果数据转成描述文本
   */
  function resToDescription(res, rtStrcuture) {
    var _this = this;
    var list = JSON.parse(JSON.stringify(res));
    var strList = [];
    var getText = function(data, temp) {
      for(var i = 0; i < data.length; i++) {
        var item = data[i];
        if([6, 7].indexOf(rtStrcuture.idAndDomMap[item.id].wt) > -1) {
          continue;
        }
        var childStr = [];
        if(item.child && item.child.length) {
          getText(item.child, childStr);
        }
        temp.push(item.val + childStr.join(';'))
      }
    }
    getText(list, strList);
    var str = strList.join(';\n');
    _this.description = str;
    return str;
  }


  /**
   * 预览报告
   * 预览规则：
   * 1.只展示已填写值得控件节点
   * 2.pvf预览格式，1为控件，2为不显示，其他值为文本
   * 3.当pvf!=1，即以文本显示时，如果存在pvt(预览文本内容)，则按pvt格式显示，$rt{AA}
   * 4.不存在pvt(预览文本内容)，则按格式：val/desc
   * @param {string} htmlContent html内容
   * @param {array} resultData 结果集
   * @param {boolean} loadViewScript 是否需要加载相关js
   * @param {string} srcUrl 静态资源的存在位置
   * @param {object} examInfo 检查信息
  */
  //  htmlContent, resultData, srcUrl, examInfo, loadViewScript
  function previewStructureReport(options) {
    var _this = this;
    options = options || {};
    options.type = 'view';
    // 先回显所有内容，再结合显示方式及值处理
    rebuildStructureReport.call(_this, options);
  }
  
  function loadScriptToShowText() {
    var _this = this;
    var disabledEle = nodeListToArray(querySelectorAll.call(_this, '.rt-sr-w[disabled]'));
    disabledEle.forEach(function(ele) {
      ele.removeAttribute('disabled');
    })
    var srWon = nodeListToArray(querySelectorAll.call(_this, '.rt-sr-w:not([data-os="1"])'));
    var labelCon = nodeListToArray(querySelectorAll.call(_this, 'label[for]:not(.lb-row)'));
    var eleList = srWon.concat(labelCon);
    eleList.forEach(function(ele) {
      var id = ele.getAttribute('for') || ele.getAttribute('id');
      if(_this.idAndDomMap[id] && !_this.idAndDomMap[id].pdId) {
        if([0].indexOf(_this.idAndDomMap[id].wt) === -1) {
          ele.style.pointerEvents = 'none';  //防止编辑
          // 兼容IE8
          ele.onclick = function() {
            return false;
          }
        }
      }
      if(_this.isWriteByEditor && id.indexOf(',') > -1 && _this.idAndDomMap[id]) {
        var value = getJoinValue(id, _this.idAndDomMap);
        setFormItemValue.call(_this, ele, value);
      }
      // if(_this.isWriteByEditor) {
      //   renderPatternStyle.call(_this, ele);
      // }
    })
    widgetTransToText.call(_this); //提取文本

    nodeListToArray(querySelectorAll('.view-none', _this.ele)).forEach(function(vItem) {
      vItem.style.display = "none";
    })

    nodeListToArray(querySelectorAll('.hight-block', _this.ele)).forEach(function(hItem) {
      hItem.style.display = "block";
    })

    nodeListToArray(querySelectorAll('.p-item', _this.ele)).forEach(function(pItem) {
      if(pItem.innerText.trim() === '') {
        removeEle(pItem);
      }
    })

    // 针对特殊的交互操作，sreport会用到，不通用所有项目
    // nodeListToArray(querySelectorAll('[contect-id]', _this.ele)).forEach(function(cItem) {
    //   var cId = cItem.getAttribute('contect-id');
    //   if(_this.idAndDomMap[cId] && _this.idAndDomMap[cId].value) {
    //     cItem.style.display = "block";
    //   }
    // })
    _this.ele && (_this.ele.style.display = 'block');
    if(_this.ele && _this.ele.querySelector('.t-pg li')) {
      _this.eleProps = {
        height: _this.ele.querySelector('.t-pg li').scrollHeight || _this.ele.querySelector('.t-pg li').offsetHeight,
        width:  _this.ele.querySelector('.t-pg li').scrollWidth || _this.ele.querySelector('.t-pg li').offsetWidth,
      }
    }
    // 转换特殊字符，如上下标
    transMathJaxText();
    resetViewPatternPositionAndStyle.call(_this);
    window.top.postMessage({
      message: 'srHtmlLoaded',
      data: {
        winName: _this.enterOptions.winName,
        eleProps: _this.eleProps || {},
      },
    }, '*');
    // 非独立打开结构化，通知结构化完成模板加载
    if(window.top !== window) {
      window.postMessage({
        message: 'srHtmlLoaded',
        data: {
          winName: _this.enterOptions.winName,
          eleProps: _this.eleProps || {},
        },
      }, '*');
    }
  }

  // 转换特殊字符，如上下标
  function transMathJaxText() {
    try {
      if(MathJax) {
        // 指定元素document.querySelectorAll('.math-text')
        MathJax.typeset(document.querySelectorAll('.rt-sr-body'));
      }
    } catch (error) {
      console.error(error);
    }
  }

  // 将不以控件类型预览的转成文本
  function widgetTransToText() {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    // 已处理的同gruopId的列
    var exitGroupIds = [];
    //pvf预览格式，1为控件，其他值为文本，2不显示，3子节点有值时按控件显示
    var textFlag = false;  //是否有文本格式
    for(var id in idAndDomMap) {
      var curItem = idAndDomMap[id];
      if(curItem.pvf === '2' ||
        (curItem.pvf === '1' && curItem.pid && idAndDomMap[curItem.pid] && idAndDomMap[curItem.pid].pvf === '2')
        || (['1', '3'].indexOf(curItem.pvf) === -1 && !curItem.value && id.indexOf(',') === -1)
        || (curItem.pvf === '3' && !judgeChildHasValue.call(_this, curItem, idAndDomMap))
      ) {
      // pvf='2'则移除节点
        querySelectorAll('.rt-sr-w[id="'+id+'"]', _this.ele).forEach(function(wConNode) {
          if(wConNode) {
            if(wConNode.parentNode && wConNode.parentNode.nodeName === 'LABEL') {
              wConNode = wConNode.parentNode;
            }
            renderPatternStyle.call(_this, wConNode, true);
            removeEle(wConNode);
          }
        })
        continue;
      }
      if(['1', '3'].indexOf(curItem.pvf) > -1) {
        // 编辑器下，按当前格式显示的多行文本，内容放不下时自动撑开
        if(_this.isWriteByEditor) {
          if(curItem.pvf === '1') {
            // 24  多行文本,按当前格式显示（pvf=1）是内容自动撑开，其他格式不处理
            if([24].indexOf(curItem.oriWt) > -1 && idAndDomMap[curItem.id] && idAndDomMap[curItem.id].value) {
              var oriHeight = $('[id="'+curItem.id+'"]').outerHeight();
              var conHeight = $('[id="'+curItem.id+'"]')[0].scrollHeight;
              if(conHeight > oriHeight) { 
                $('[id="'+curItem.id+'"]').closest('.w-con').css('height', conHeight+'px');
              }
            }
          }
        }
        continue;
      }
      if(exitGroupIds.indexOf(curItem.groupId) > -1 && curItem.wt === 4) {
        querySelectorAll('[name="'+curItem.groupId+'"]', _this.ele).forEach(function(exitNode, i){
          if(exitNode) {
            if(exitNode.parentNode && exitNode.parentNode.nodeName === 'LABEL') {
              exitNode = exitNode.parentNode;
            }
            renderPatternStyle.call(_this, exitNode, true);
            removeEle(exitNode);
            if(!idAndDomMap[curItem.id] || !idAndDomMap[curItem.id].value) {
            }
          }
        })
      }
      // 找到id的节点，处理已赋值的
      var node = querySelector('.rt-sr-w[id="'+id+'"]:not(.rt-unview)', _this.ele);
      if(node) {
        var str = '';
        // if([4, 5].indexOf(curItem.wt) !== 2 && curItem.itemList && curItem.itemList.length) {
        if([4, 5].indexOf(curItem.wt) > -1) {
          var strArr = [];
          var willDelIndex = -1;
          var sortNo = 0;
          if(curItem.itemList && curItem.itemList.length) {
            curItem.itemList.forEach(function(item, sI) {
              if(item && idAndDomMap[item.id] && idAndDomMap[item.id].value) {
                var tempStr = '';
                idAndDomMap[item.id].groupId && exitGroupIds.push(idAndDomMap[item.id].groupId)
                if(!idAndDomMap[item.id].pvt) {
                  var value = idAndDomMap[item.id].value || '';
                  tempStr = value;
                  if(value === '无' && idAndDomMap[item.id].tempValue) {
                    willDelIndex = sI;
                  } else {
                    sortNo++;
                  }
                  // 是否包含子节点
                  var curItemNode = querySelector('.rt-sr-w[id="'+item.id+'"]', _this.ele);
                  if(curItemNode) {
                    var innerChild = querySelectorAll('[pid="'+item.id+'"]', curItemNode.parentNode);
                    var innerContent = [];
                    innerChild.forEach(function(innerItem) {
                      var innerId = innerItem.id;
                      if(idAndDomMap[innerId] && idAndDomMap[innerId].value) {
                        innerContent.push(idAndDomMap[innerId].value);
                      }
                    })
                    if(innerContent.length) {
                      tempStr += '：' + innerContent.join(',');
                    }
                  }
                } else {
                  var formatPvt = formatPvtHandler.call(_this, idAndDomMap[item.id].pvt, idAndDomMap, item.id);
                  tempStr = formatPvt;
                }
                if(tempStr) {
                  var sortNoText = (_this.enterOptions.checkboxSort || _this.diffConfig.checkboxSort) && curItem.wt===4 && sortNo >=1 ? '('+sortNo+')' : '';
                  strArr.push(sortNoText + tempStr);
                }
              } else {
                querySelectorAll('.rt-sr-w[id="'+item.id+'"]', _this.ele).forEach(function(delNode){
                  if(delNode) {
                    if(delNode.parentNode && delNode.parentNode.nodeName === 'LABEL') {
                      delNode = delNode.parentNode;
                    }
                    renderPatternStyle.call(_this, delNode, true);
                    removeEle(delNode);
                  }
                })
                // if(curItem.wt === 5) {
                // }
              }
            })
          }
          if(strArr.length > 1 && willDelIndex > -1) {
            strArr.splice(willDelIndex, 1);
          }
          str += strArr.join('；');
        } else {
          if(curItem.value || id.indexOf(',') > -1) {
            if(['6', '7'].indexOf(curItem.wt) === -1) {
              if(!curItem.pvt) {
                var value = curItem.value || '';
                if(_this.isWriteByEditor && id.indexOf(',') > -1) {
                  value = getJoinValue(id, idAndDomMap);
                }
                str += value;
              } else {
                var formatPvt = formatPvtHandler.call(_this, curItem.pvt, idAndDomMap, curItem.id);
                str += formatPvt;
              }
              if(curItem.wt === 4) {
                str += '；';
              }
            }
          }
        }
        str = str.replace(/；；/g, '；');
        if(str) {
          textFlag = true;
          var tempDom = document.createElement('span');
          // tempDom.innerText = str;
          tempDom.innerHTML = str;
          tempDom.className = "text-con";
          tempDom.style.whiteSpace = 'pre-wrap';
          tempDom.style.wordBreak = 'break-word';
          // node.insertAdjacentHTML('afterEnd', '<span>'+str+'</span>')
          if(node.parentNode && node.parentNode.nodeName === 'LABEL') {
            node = node.parentNode;
          } 
          if(_this.isWriteByEditor) {
            tempDom.id = id;
          }
          renderPatternStyle.call(_this, node);
          node.insertAdjacentElement('afterEnd', tempDom);
          if(curItem.wt) {
            if(tempDom.clientWidth && tempDom.clientWidth > 0) {
              if(curItem.wt === 1) {
                tempDom.style.minWidth = tempDom.clientWidth + 'px';
              }
              // tempDom.style.maxWidth = tempDom.clientWidth + 'px';
            }
          }
        } 
        removeEle(node);
      }
    }
    if(textFlag) {
      alignLeftHandler.call(_this);
      // 调整top
      // resizeTopHandler.call(_this, idAndDomMap);
    }
  }

  // 判断子级节点是否填写内容，或者是否以控件显示，若是返回true
  function judgeChildHasValue(curItem, idAndDomMap) {
    var _this = this;
    var id = curItem.id;
    var flag = false;
    if(id) {
      $(_this.ele).find('[pid="'+id+'"]').each(function(i, dom) {
        var childId = dom.id;
        // 当前id有值，或者多个id连着的其中一个有值
        if(idAndDomMap[childId] && ((idAndDomMap[childId].value || idAndDomMap[childId].pvf === '1') || (childId.indexOf(',') > -1 && getJoinValue(childId, idAndDomMap)))) {
          flag = true;
          return;
        }
      })
    }
    return flag;
  }
  
  // 获取多个id连接起来的节点值
  function getJoinValue(ids, idAndDomMap) {
    var strArr = [];
    ids.split(',').forEach(function(id) {
      if(idAndDomMap[id] && idAndDomMap[id].value) {
        strArr.push(idAndDomMap[id].value);
      }
    })
    return strArr.length ? strArr.join('、') : '';
  }

  // 处理预览文本格式的内容、编辑器内推导规则转换
  function formatPvtHandler(pvt, idAndDomMap, curId) {
    // $if($rt{rt-f369z-24-3}?`结论：$rt{rt-f369z-24-3}`:"")\n$rt{rt-f369z-24-3}
    var _this = this;
    if(pvt.indexOf('$if') > -1) { 
      return renderTemplate.call(_this, pvt, idAndDomMap);
    }
    var keyReg = /\$rt\{.*?\}/ig;  //匹配$rt{AA}
    // var valReg = /(?<=\$rt\{).*?(?=\})/ig;  //匹配$rt{AA}中间的id->AA IE不兼容?<=
    var valReg = /\$rt\{.*?(?=\})/ig;  //匹配$rt{AA}中间的id->$rt{AA，兼容IE再做处理
    var keyList = pvt.match(keyReg);
    var valList = pvt.match(valReg);
    if(!keyList || keyList.length === 0) {
      return pvt;
    }
    keyList.forEach(function(keyStr, index) {
      var id = valList[index].replace('$rt{', '');
      if(id === 'this') {
        if((!idAndDomMap[curId] || !idAndDomMap[curId].value) && keyList.length === 1) {
          pvt = '';
        } else {
          pvt = pvt.replace(keyStr, idAndDomMap[curId] && idAndDomMap[curId].value ? idAndDomMap[curId].value : '');
        }
      } else {
        if(curId && idAndDomMap[id] && idAndDomMap[curId] && !idAndDomMap[curId].value) {
          if(idAndDomMap[id].groupId !== idAndDomMap[curId].groupId) {
            var replaceStr = idAndDomMap[id].value ? (idAndDomMap[id].pvt || idAndDomMap[id].value) : '';
            pvt = pvt.replace(keyStr, replaceStr);
          } else {
            pvt = pvt.replace(keyStr, '');
          }
        } else {
          var replaceStr = '';
          if(_this.isWriteByEditor) {
            var isGp = $('#' + id).hasClass('gp');  //判断是否为选项组
            if(isGp) {
              var groupName = $('#' + id + '> span').text() || '';
              var itemList = [];
              var option = $('#' + id).find('label[for]');
              if(option.length && option[0].getAttribute('for')) {
                var optId = option[0].getAttribute('for');
                itemList = _this.idAndDomMap[optId].itemList || [];
              }
              var groupVal = [];
              itemList.forEach(function(item, index) {
                if(idAndDomMap[item.id] && idAndDomMap[item.id].value) { 
                  var oneVal = idAndDomMap[item.id].pvt || idAndDomMap[item.id].value;
                  if(oneVal) {
                    groupVal.push(oneVal);
                  }
                }
              });
              if(groupVal.length) {
                replaceStr = groupName + groupVal.join('、');
              }
            } else {
              if(idAndDomMap[id] && idAndDomMap[id].value) {
                replaceStr = idAndDomMap[id].pvt || idAndDomMap[id].value || '';
              }
            }
          } else {
            if(idAndDomMap[id] && idAndDomMap[id].value) {
              replaceStr = idAndDomMap[id].pvt || idAndDomMap[id].value || '';
            }
          }
          pvt = pvt.replace(keyStr, replaceStr);
        } 
       
      }
    })
    if(_this.isWriteByEditor) {
      pvt = cleanString(pvt);
    }
    return pvt;
  }

  // 获取id值，区分模板编辑器
  function getWtValueById(id, idAndDomMap) { 
    var _this = this;
    var valRes = '';
    if(_this.isWriteByEditor) {
      var isGp = $('#' + id).hasClass('gp');  //判断是否为选项组
      if(isGp) {
        var groupName = $('#' + id + '> span').text() || '';
        var itemList = [];
        var option = $('#' + id).find('label[for]');
        if(option.length && option[0].getAttribute('for')) {
          var optId = option[0].getAttribute('for');
          itemList = _this.idAndDomMap[optId].itemList || [];
        }
        var groupVal = [];
        itemList.forEach(function(item, index) {
          if(idAndDomMap[item.id] && idAndDomMap[item.id].value) { 
            var oneVal = idAndDomMap[item.id].pvt || idAndDomMap[item.id].value;
            if(oneVal) {
              groupVal.push(oneVal);
            }
          }
        });
        if(groupVal.length) {
          valRes = groupName + groupVal.join('、');
        }
      } else {
        if(idAndDomMap[id] && idAndDomMap[id].value) {
          valRes = idAndDomMap[id].pvt || idAndDomMap[id].value || '';
        }
      }
    } else {
      valRes = idAndDomMap[id] && idAndDomMap[id].value ? idAndDomMap[id].value : '';
    }
    return valRes;
  }

  // 带条件语句的文本转换
  function renderTemplate(pvt, idAndDomMap) {
    var _this = this;
    pvt = pvt.replace(/\$if\(([^?]+)\?([^:]*):([^\)]*)\)/g, (_, condition, trueExpr, falseExpr) => {
      // 提取变量名
      const match = condition.match(/\$rt\{(.+?)\}/);
      if (match) {
        const id = match[1];
        var value = getWtValueById.call(_this, id, idAndDomMap);
        // const value = idAndDomMap[id] && idAndDomMap[id].value ? idAndDomMap[id].value : '';
        return value ? trueExpr : (falseExpr !== "''" && falseExpr !== '""' ? falseExpr : '');
      }
      return '';
    });
    // 替换 $rt{id} 变量
    pvt = pvt.replace(/\$rt\{(.+?)\}/g, (_, id) => {
      var replaceStr = getWtValueById.call(_this, id, idAndDomMap);
      return replaceStr;
    });
    if(_this.isWriteByEditor) {
      pvt = cleanString(pvt);
    }
    return pvt;
  }

  // 去掉多余符号
  function cleanString(input) {
    // 去除开头和结尾的多余分号或中文分号
    var cleaned = input.replace(/^[\;\；\n\r]+|[\;\；\n\r]+$/g, '');
    // 去除中间连续的多余分号或中文分号，只保留一个
    cleaned = cleaned.replace(/[\;\；]{2,}/g, ';');
    return cleaned;
  }

  // 向父元素靠齐
  function alignLeftHandler() {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    var textConList = nodeListToArray(querySelectorAll('.text-con', _this.ele));
    var handler = function(node) {
      if(!node.classList.contains('text-con')) {
        return;
      }
      var pid = node.getAttribute('pid');
      if(pid) {
        // 找到同一级最近的那一个
        var siblingProp = findSiblingIdFun.call(_this, pid, node.id);
        var left = 0;
        var top = 0;
        var siblingNode = null;
        var nodeTop = idAndDomMap[node.id].top;
        var nodeLeft = idAndDomMap[node.id].left;
        var lastLevelNodeTop = 0;
        if(siblingProp) {
          var sibId = siblingProp.id;
          // 单选框 复选框
          if(idAndDomMap[sibId].wt === 5 || idAndDomMap[sibId].wt === 4) {
            sibId = idAndDomMap[sibId].groupId;
          }
          siblingNode = querySelector('.rt-sr-w[id="'+siblingProp.id+'"]', _this.ele);
          
          if(siblingNode) {
            if(siblingNode.classList.contains('text-con')) {
              handler(siblingNode);
            }
            var siblingNodeLeft = idAndDomMap[siblingNode.id].left;
            left = Number(siblingNodeLeft) + Number(siblingNode.offsetWidth) + 5;

            var siblingNodeTop = idAndDomMap[siblingNode.id].top;
            lastLevelNodeTop = siblingNodeTop;
            top = Number(siblingNodeTop) + Number(siblingNode.offsetHeight) + 10;
          }
        }
        if(!siblingNode) {
          // 单选框 复选框
          if(idAndDomMap[pid].wt === 5 || idAndDomMap[pid].wt === 4) {
            pid = idAndDomMap[pid].groupId;
          }
          
          var parentNode = querySelector('.rt-sr-w[id="'+pid+'"]', _this.ele);
          if(parentNode) {
            if(parentNode.classList.contains('text-con')) {
              handler(parentNode);
            }
            var parentNodeLeft = idAndDomMap[parentNode.id].left;
            if(nodeLeft - parentNodeLeft >= Number(parentNode.offsetWidth)) {
              left = Number(parentNodeLeft) + Number(parentNode.offsetWidth) + 5;
              var parentNodeTop = idAndDomMap[parentNode.id].top;
              lastLevelNodeTop = parentNodeTop;
              top = Number(parentNodeTop) + 5;
            }
          }
        }
        
        if(top > 0) {
          if(nodeTop - lastLevelNodeTop > 15) {
            left = 0;  //不在同样的Y上则不处理x
            // 5-10之间的差值不进行调整
            if(top < Number(nodeTop) - 5 || top > Number(nodeTop) + 10 ) {
              node.style.top = top + 'px';
              idAndDomMap[node.id].top = top;
            }
          }
        }
        if(left > 0) {
          node.style.left = left + 'px';
          idAndDomMap[node.id].left = left;
          node.classList.remove('text-con');
        }
      }
    }
    textConList.forEach(function(node) {
      handler(node);
    })
  }

  // 找到同一级兄弟最近的那一个，用于调整xy坐标
  function findSiblingIdFun(pid, selfId) {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    var selfLeft = idAndDomMap[selfId].left;
    var selfTop = idAndDomMap[selfId].top;
    var siblingIds = [];
    for(var key in idAndDomMap) {
      var curItem = idAndDomMap[key];
      var bool = false;
      if(curItem.pvf !== '1' && curItem && curItem.itemList) {
        curItem.itemList.forEach(function(item) {
          // if(idAndDomMap[item.id].value) {
          if(item && item.id && idAndDomMap[item.id]) {
            bool = true;
            return;
          }
        })
      }
      if(key === selfId || curItem.pid !== pid || curItem.isItemList || 
        (curItem.pvf !== '1' && !bool)) {
        // (curItem.pvf !== '1' && !curItem.value && !bool)) {
        continue;
      }
      // 位于左边且上方的才进行对比
      if(selfLeft - curItem.left >= -10 && selfTop - curItem.top >= -10) {
        siblingIds.push({
          id: curItem.id,
          left: curItem.left,
          top: curItem.top,
          leftDiff: selfLeft - curItem.left,
        })
      }
    }
    if(siblingIds && siblingIds.length) {
      // 排序取出最近的兄弟元素
      siblingIds = siblingIds.sort(function(a, b) {
        if(a.leftDiff - b.leftDiff >= -10) {
          return b.top - a.top;
        } else {
          return a.leftDiff - b.leftDiff;
        }
      })
      return siblingIds[0];
    }
    return null;
  }

  // 调整元素的top以完整显示超出模板设置的高度的内容
  function resizeTopHandler(idAndDomMap) {
    var _this = this;
    var handler = function(allId, curId) {
      for(var i = 0; i < allId.length; i++) {
        var curNode = querySelector.call(_this, '[id="'+curId+'"]');
        if(!curNode) {
          break;
        }
        var lastNode = querySelector.call(_this, '[id="'+allId[i]+'"]');
        // 找不到上一节点或者是子节点就继续找
        if(!lastNode || !idAndDomMap[curId] || idAndDomMap[curId].pid === allId[i] || !idAndDomMap[allId[i]]) {
          continue;
        }
        if((idAndDomMap[curId].top - idAndDomMap[allId[i]].top <= 15)) {
          continue;
        }
        var lastTopAndH = Number((lastNode.style.top).replace('px', '')) + Number(lastNode.offsetHeight);
        var curTop = Number((curNode.style.top).replace('px', ''));
        if(curTop < lastTopAndH + 10) {
          // 为了尽可能对齐，wt=2或3的情况加15,因为其他类型控件存在5px上内边距
          curNode.style.top = (lastTopAndH + (idAndDomMap[curId].wt === 5 || idAndDomMap[curId].wt === 4 ? 15 : 10)) + 'px';
          break;
        }
      }
    }
    var pageLi = querySelectorAll.call(_this, 'li.page');
    var widgetProp = _this.widgetPropMap;
    nodeListToArray(pageLi).forEach(function (page, index) {
      var temp = [];
      for(var j = 0; j < widgetProp.length; j++) {
        var item = widgetProp[j];
        var id = item.id;
        // 按页处理
        if(item.rtScPageNo === index + 1 && temp.indexOf(id) === -1) {
          if(item.groupId) {
            if(temp.indexOf(item.groupId) === -1) {
              temp.push(item.groupId);
            }
          } else {
            temp.push(id);
          }
        }
      }
      for(var i = 0; i < temp.length; i++) {
        if(i === 0) {
          continue;
        }
        var siblingIds = temp.slice(0, i).reverse();
        handler(siblingIds, temp[i])
      }
      // 超出当前页面高度，则赋内容高度+15px作为留白
      if(page.offsetHeight < page.scrollHeight) {
        page.style.height = (page.scrollHeight + 15) + 'px';
      }
    })
  }

  // 判断表单类型及赋值
  function setFormItemValue(widget, value) {
    var _this = this;
    var type = getWidgetType.call(_this, widget);
    if (type === 1 || (!this.enterOptions.oldDataText && type === 0)) {  //纯文本、文本框
      if(value || (!value && type === 1)) {
        if(widget.nodeName.toLowerCase() === 'input') {
          var hasHtmlDefaultVal = widget.getAttribute('value'); // 处理HTML写的默认值
          if (value || !hasHtmlDefaultVal) {
            widget.setAttribute('value', value);
          }
        } else {
          var hasHtmlDefaultVal = widget.innerText; // 处理HTML写的默认值
          if (value || !hasHtmlDefaultVal) {
            widget.innerText = value;
            widget.value = value;
          }
        }
      } 
    } else if ([4, 5].indexOf(type) > -1) {  //单选、复选
      if(value) {
        if(type === 5 && getCheckedSelector(querySelectorAll.call(_this, '[name="'+widget.name+'"]'))) {
          getCheckedSelector(querySelectorAll.call(_this, '[name="'+widget.name+'"]')).removeAttribute('checked');
        }
        widget.classList.add('rt-checked');
        widget.setAttribute('checked', true);
      } else {
        widget.classList.remove('rt-checked');
        widget.removeAttribute('checked');
      }
    } else if (type === 2) {  //下拉
      if(value) {
        if(querySelector('option[value="' + value + '"]', widget)) {
          querySelector('option[value="' + value + '"]', widget).setAttribute('selected', true);
        }
      } else {
        $(widget).val('');
      }
    } else if([6, 7].indexOf(type) > -1) {  //二维码、条形码
      if(type === 6) {
        if(createBarcode || JsBarcode) {
          if(widget.nodeName === 'IMG') {
            if(JsBarcode) {
              var codeFontSize = 14;
              var imgH = $(widget).closest('.w-con').length ? $(widget).closest('.w-con').height() : (parseInt(getComputedStyle(widget).height) || 20);
              if(value) {
                var wtAttr = getWtAttrByIdFromWtData.call(_this, widget.id);
                var displayValue = false;
                if(wtAttr) {
                  displayValue = wtAttr.codeText === '1';
                  if(displayValue) {
                    imgH = imgH - codeFontSize;
                  }
                }
                JsBarcode(widget, value, {
                  format: "code128",
                  width: 1,
                  height: imgH,
                  fontOptions: 'bolder',
                  displayValue: displayValue,
                  margin: 0,
                  textMargin: 0,
                  fontSize: codeFontSize,
                });
                $(widget).show();
              }
            }
          } else {
            createBarcode(widget, value, 'C');
            $(widget).show();
          }
        }
      }
      if(type === 7 && QRCode) {
        var qrcode = new QRCode(widget, {
          text: '',
          width: $(widget).closest('.w-con').length ? $(widget).closest('.w-con').width() : 100,
          height: $(widget).closest('.w-con').length ? $(widget).closest('.w-con').height() : 100,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        });
        qrcode.makeCode(value);
        // 找到需要转换的canvas标签
        var myCanvas = widget.getElementsByTagName('canvas')[0];
        // 放入图片
        $(widget).attr("src", myCanvas.toDataURL('image/png'));
        $(widget).html('');
        $(widget).show();
      }
    }
  }

  // 获取当前控件的属性
  function getWtAttrByIdFromWtData(id) {
    var _this = this;
    if(!_this.wtData.length) {
      return null;
    }
    for(var i = 0; i < _this.wtData.length; i++) {
      var item = _this.wtData[i];
      if(item.id === id) {
        return item;
      }
    }
  }

  function findCurStructure(ele, instanceList) {
    var curInstance = null;
    for (var i = 0; i < instanceList.length; i++) {
      var instance = instanceList[i];
      if (instance.ele === ele) {
        curInstance = instance;
        break;
      }
    }
    return curInstance;
  }

  // 增加痕迹标识data-trace
  function addTraceFlagInDom(item, widget) {
    // 纯文本不进行添加，可编辑的内容才存在痕迹
    if(item.wt !== 0 && item.lastVal) {
      if([4,5].indexOf(item.wt) > -1) {
        widget.parentNode.setAttribute('data-trace', item.id)
      } else {
        widget.setAttribute('data-trace', item.id)
      }
    }
  }

  // 添加报告痕迹的交互
  function addTraceHandler(selector, data) {
    if(scriptCount < innerScriptArr.length) {
      // 判断额外js是否加载完成
      setTimeout(function() {
        addTraceHandler(selector, data)
      }, 100)
      return;
    }
    if(!$(selector).length) {
      return;
    }
    // 延时是为了确保模板的内部交互加载完成
    setTimeout(function() {
      // 处理单复选框的禁用切换，不禁用事件
      $(selector + ' input:not([type="text"])').attr("onclick", "return false");
      $(selector).find('label[for]').attr("onclick", "return false");
      $(selector + ' select option').attr('disabled', true);
      $(selector + ' button').attr('disabled', true);
      $(selector + ' textarea').attr('readonly', 'readonly');
      $(selector + ' input[type="text"]').attr('readonly', 'readonly');
      $(selector + ' input').off('change').off('click');
      // 针对element-ui的处理is-disabled
      // $(selector).find('.is-checked').addClass('is-disabled');
      $(selector + ' .el-date-editor input').attr('disabled', true);
  
      var proverDom = null;
      $(selector).find('[data-trace]:not([data-trace="no"])').off('mouseenter').off('mouseleave');
      $(selector).find('[data-trace]:not([data-trace="no"])').on('mouseenter', function(e) {
        var traceKey = $(this).attr('data-trace')
        if(data[traceKey] && data[traceKey].lastVal) {
          proverDom = createProver($(this), data[traceKey].lastVal)
        }
      }).on('mouseleave', function(){
        if(proverDom) {
          proverDom.remove();
        }
      })
    }, 500);
  }

  function createProver(vm, content) {
    var width = vm.outerWidth();
    var height = vm.outerHeight();
    var top = vm.offset().top;
    var left = vm.offset().left;
    var html = '<div id="trace-prover" style="z-index:10;line-height:20px;white-wrap:pre-wrap;border-radius:4px;padding:4px 8px;position:absolute;background:#303133;top:'+top+'px;left:'+left+'px">';
    html += '<div style="font-size:12px;color:#FFF;word-break:break-all;">前一版本：'+content+'</div>';
    html += '<div style="width:0;height:0;position:absolute;top:100%;left:20px;border-width:8px 8px;border-style:solid;border-color:#303133 transparent transparent"></div>';
    html += '</div>';
    var proverDom = $(html);
    $('body').append(proverDom);
    if(width < 220) {
      proverDom.css('max-width', '220px');
    } else {
      proverDom.css('max-width', width+'px');
    }
    var proverHeight = proverDom.outerHeight();
    proverDom.css('top', (top - proverHeight - 8)+'px');
    return proverDom;
  }
  window.addTraceHandler = addTraceHandler;

  // 展开子模板
  function openChildPattern() {
    var _this = this;
    $('[data-pdid].rt-sr-w:not([data-os="1"])').off('click');
    $('[data-pdid].rt-sr-w:not([data-os="1"])').click(function(e) {
      var target = this;
      if(target && target.type === 'checkbox' && !target.checked) {
        return;
      }
      var patDocId = target.getAttribute('data-pdid');
      if(!patDocId) {
        if($wfMessage) {
          $wfMessage({
            content: '模板不存在'
          });
        }
        return;
      }
      var id = this.getAttribute('id');
      var data = getOrSetCurReportLocal('get');
      var mainPattern = JSON.parse(JSON.stringify(data));
      var pageKey = mainPattern['busId']+'_'+patDocId;
      var childObj = $.extend(data, {
        source: '1',
        child: '1',
        patDocId: patDocId,
        pageKey: encryptFun(pageKey)
      });
      getOrSetCurReportLocal('set', childObj, pageKey);
      var query = 'frontendParam=' + encryptFun(pageKey);
      var iframeSrc = './#/srPage?child=1&' + query;
      var iframeHtml = '<iframe patDocId="'+id+'" src="'+iframeSrc+'" frameborder="0" style="width:100%;height:600px" id="rt-iframe-dig"></iframe>';
      try {
        drawDialog({
          title: _this.idAndDomMap[id] && _this.idAndDomMap[id].name ? _this.idAndDomMap[id].name : '查看',
          modal: true,
          content: iframeHtml,
          clickModalToClose: false,
          style: {
            'width': '100%',
          },
          bodyStyle: {
            'border-top': '1px solid #DCDFE6',
            'padding': '0'
          },
          closeFun: saveWidgetJson
        });
      } catch (error) {
        console.error('弹框rtDialog不存在->', error);
      }
    })
  }

  // 触发保存弹框里节点
  function saveWidgetJson() {
    var iframeWin = document.querySelector("#rt-iframe-dig").contentWindow;
    if(iframeWin.srCommonOuterOpt && typeof iframeWin.srCommonOuterOpt === 'function') {
      // onlySetStorage 仅缓存起来，不请求接口
      iframeWin.srCommonOuterOpt({type:'1', status: '0', onlySetStorage: true});
    }
    if(window.closeRtDialogCbFun && typeof window.closeRtDialogCbFun === 'function') {
      var id = document.querySelector("#rt-iframe-dig").getAttribute('id');
      window.closeRtDialogCbFun(id);
    }
  }

  // 编辑器：预览页面提取除所有定位元素，存放top，按lyId分组
  function getAllAbsoluteElement() {
    var _this = this;
    // 非编辑器生成模板、非预览页面，不处理
    if(!_this.isWriteByEditor) {
      return;
    }
    var wtData = _this.wtData;
    if(!wtData.length) {
      return;
    }
    _this.absoluteElePosition = [];
    var absoluteObj = {};
    for(var i = 0; i < wtData.length; i++) {
      var item = wtData[i];
      if(item.style.top !== '' && parseInt(item.style.top) === 0) {
        continue;
      }
      // 数据推导忽略
      if(item.wt === '-1') {
        continue;
      }
      if(item.style.top === '' || isNaN(parseInt(item.style.top))) {
        continue;
      }
      var wt = item.wt;
      var id = item.id;
      var etId = item.etId;
      var lyId = item.lyId;
      var obj = {
        wt: wt,
        id: etId || id,
        name: item.name,
        left: item.style.left !== '' ? parseInt(item.style.left) : '',
        top: item.style.top !== '' ? parseInt(item.style.top) : '',
        bottom: item.style.bottom !== '' ? parseInt(item.style.bottom) : '',
        height: item.style.height
      }
      // 布局、表格
      if(['-2', '-3'].indexOf(wt) > -1) {
        if(!absoluteObj[id]) {
          absoluteObj[id] = objMerge(obj, {children: []});
        } else {
          absoluteObj[id].id = id;
          absoluteObj[id].name = item.name;
          absoluteObj[id].top = item.style.top !== '' ? parseInt(item.style.top) : '';
          absoluteObj[id].bottom = item.style.bottom !== '' ? parseInt(item.style.bottom) : '';
          absoluteObj[id].height = item.style.height;
        }
        continue;
      }

      // 其他普通控件
      if(lyId) {
        // obj.top = Number(obj.top) + $('[id="'+obj.id+'"]').outerHeight();
        if(!absoluteObj[lyId]) {
          absoluteObj[lyId] = {
            children: [],
            id: lyId
          };
        }
        absoluteObj[lyId].children.push(objMerge(obj, {lyId: lyId}));
      }

    }
    for (var key in absoluteObj) {
      if(!absoluteObj[key].id) {
        continue;
      }
      if(absoluteObj[key].children.length) {
        absoluteObj[key].children = absoluteObj[key].children.sort(function(a, b){
          // 相差10说明大致同行
          if(Math.abs(a.top - b.top) <= 10) {
            return Number(a.left) - Number(b.left);
          }
          return Number(a.top) - Number(b.top);
        })
      }
      _this.absoluteElePosition.push(absoluteObj[key]);
    }
    if(_this.absoluteElePosition.length) {
      _this.absoluteElePosition = _this.absoluteElePosition.sort(function(a, b){
        if(Math.abs(a.top - b.top) <= 10) {
          return Number(a.left) - Number(b.left);
        }
        return Number(a.top) - Number(b.top);
      })
    }
  }

  // 编辑器：预览页面调整定位的元素高度，和定位上移
  function resetViewPatternPositionAndStyle() {
    var _this = this;
    // 非编辑器生成模板、非预览页面，不处理
    if(!_this.isWriteByEditor || _this.enterOptions.type !== 'view') {
      return;
    }
    // 调整行高，编辑器和实际页面相差了3px（未找到原因，暂时减去）
    $(_this.ele).find('*').each(function() {
      if(this.style.lineHeight && !isNaN(parseInt(this.style.lineHeight))) {
        // this.style.lineHeight = (parseInt(this.style.lineHeight) - 3) + 'px';
      }
    })
    
    // 位置调整
    adjustTopPosition.call(_this, _this.absoluteElePosition)
    // 高度调整，获取layout容器元素
    if(_this.absoluteElePosition.length) {
      // 只处理容器元素
      var layoutElePosition = _this.absoluteElePosition.filter(item => !item.lyId);
      layoutElePosition.forEach(function(item, i) {
        if(!item.lyId) {
          var lyDom = document.querySelector('[id="'+item.id+'"]');
          if(lyDom) {
            var originHeight = $(lyDom).outerHeight();
            var newHeight = adjustParentHeight(lyDom, originHeight);
            var ignoreHeightAdjust = false;  //忽略调整高度
            if(i < layoutElePosition.length - 1) {
              // 上下元素间距大于60，则忽略调整高度
              if(Math.abs(item.top + originHeight - layoutElePosition[i + 1].top) > 60) {
                ignoreHeightAdjust = true;
              }
            }
            // 25大概是一行文字的高度，在这个范围内不改变
            if(!ignoreHeightAdjust && newHeight && Math.abs(newHeight - originHeight) > 25) {
              lyDom.style.height = newHeight + 'px';
              item.eleHeightDiff = newHeight - originHeight;
            } else {
              lyDom.style.height = originHeight + 'px';
              item.eleHeightDiff = 0;
            }
            // newHeight && (lyDom.style.height = newHeight + 'px');
            lyDom.style.minHeight = 'unset';
            // item.eleHeightDiff = newHeight - originHeight + 8;
          }
        }
      })
      adjustFollowingElements(_this.absoluteElePosition, 0);
    }

    // 调整整个页面的高度
    var pageDom = $('[is-rt-editor="1"] .page');
    var originPageHeight = pageDom.outerHeight();
    var newHeight = adjustParentHeight(pageDom[0], originPageHeight);
    if(originPageHeight < newHeight) {
      pageDom.css('height', newHeight + 'px');
    }
    // 将无rt-sr-header\rt-sr-footer的内容归入rt-sr-body,整体页面只保留一个rt-sr-body，兼容原生成PDF分页逻辑
    var layoutDom  = pageDom.find('.rt-layout');
    var newBodyContainer = $(`<div class="rt-sr-body"></div>`);
    var topOneIndex = -1;
    if(layoutDom.length) {
      layoutDom.each(function(i, dom) {
        var $dom = $(dom);
        var id = $(dom).attr('id');
        // rt-st-header错误的历史数据
        if(!$dom.hasClass('rt-st-header') && !$dom.hasClass('rt-sr-header') && !$dom.hasClass('rt-sr-footer')) {
          // 移除原有body类名
          if($dom.hasClass('rt-sr-body')) {
            $dom.removeClass('rt-sr-body');
          }
          if(topOneIndex === -1) {
            topOneIndex = _this.absoluteElePosition.findIndex(item => item.id === id && item.top !== '');
          }
          var newDom = $dom.clone(true);
          $dom.remove();
          newBodyContainer.append(newDom);
        }
      })
      if(topOneIndex > -1) {
        var left = parseInt(pageDom[0].style.paddingLeft || 0);
        var right = parseInt(pageDom[0].style.paddingRight || 0);
        var originTopDiff = _this.absoluteElePosition[topOneIndex].top;
        var top = originTopDiff - parseInt(pageDom[0].style.paddingTop || 0);
        newBodyContainer.css({
          top: top + 'px',
          position: 'relative',
          width: `calc(100% + ${left + right}px)`,
          left: -left + 'px',
        });
        // 调整布局位置到原来的位置
        newBodyContainer.find('.rt-layout').each(function(i, lyDom) {
          if(lyDom.style.top) {
            var selfTop = parseInt(lyDom.style.top);
            if(lyDom.id === _this.absoluteElePosition[topOneIndex].id) {
              $(lyDom).css('top', (selfTop - originTopDiff) + 'px');
            } else {
              //  - parseInt(pageDom[0].style.paddingTop || 0)
              // 定位元素不考虑paddingTop
              $(lyDom).css('top', (selfTop - originTopDiff) + 'px');
            }
          }
        })
      }
      pageDom.append(newBodyContainer);
    }
    transMathJaxText();
  }
  // 编辑器：编辑页面页面调整整体页面高度，避免出现Y滚动条
  function resetPageStyle() {
    var _this = this;
    // 非编辑器生成模板、非预览页面，不处理
    if(!_this.isWriteByEditor || _this.enterOptions.type === 'view') {
      return;
    }

    // 调整整个页面的高度
    var pageDom = $('[is-rt-editor="1"] .page');
    var originPageHeight = pageDom.outerHeight();
    var newHeight = adjustParentHeight(pageDom[0], originPageHeight);
    pageDom.css('height', newHeight + 'px');
    pageDom.css('min-height', 'unset');

    var pageBgColor = pageDom.css('background-color');
    if(pageBgColor) {
      $('[is-rt-editor="1"]').css('background', pageBgColor);
    }
  }

  // 编辑器：含定位子元素进行调整父元素的高度
  function adjustParentHeight(parentElement, originalHeight) {
    var children = Array.from(parentElement.children); // 获取所有子元素
    if (!children?.length) {
      return originalHeight;
    }
    if(children[0].nodeName === 'TABLE') {
      return originalHeight;
    }
    var totalHeight = 0;  //所有子元素高度之和
    var notAbsElementHeight = 0;  // 非定位元素高度之和
    var bottomHeight = 0;  // 底部元素高度
    for (var child of children) {
      var style = window.getComputedStyle(child);
      // 找出非定位的元素大小
      if (style.position !== 'absolute' && style.position !== 'fixed') {
        notAbsElementHeight += child.offsetHeight;
      } else {
        // 找出定位元素的最大底部位置
        var top = child.offsetTop;
        var height = child.offsetHeight;
        // 设置了bottom的元素
        if(child.style.bottom) {
          bottomHeight += $(child).outerHeight(true);
        }
        totalHeight = Math.max(totalHeight, top + height);
      }
    }
    // var marginH = parseInt(parentElement.style.marginTop || 0) + parseInt(parentElement.style.marginBottom || 0);
    totalHeight = Math.max(totalHeight, notAbsElementHeight) + bottomHeight;
    return totalHeight;
  }

  // 编辑器：位置调整上移，按top处理
  function adjustTopPosition(elements, isChild) {
    var _this = this;
    if(!isChild) {
      elements.forEach((element, index) => {
        // 如果有子节点，递归遍历子节点
        if (element.children && element.children.length > 0) {
          adjustTopPosition(element.children, true);
        }
        // 检查父节点本身
        checkAndAdjust(element, elements, index);
      });
    } else {
      checkAndAdjust(elements[0], elements, 0);
    }

    function checkAndAdjust(element, allElements, elementIndex) {
      adjustFollowingElements(allElements, elementIndex);
    }
  }
  function adjustFollowingElements(allElements, startIndex) {
    var adjustTop = {}; //top已调整，同行的不处理
    var moveDiff = 0;
    // 遍历当前元素之后的所有兄弟节点，并向上移动
    for (var i = startIndex + 1; i < allElements.length; i++) {
      var nextElement = allElements[i];
      var nextElementDom = document.getElementById(nextElement.id);
      var lastElement = allElements[i - 1];
      // 左右相差300，则不处理
      if(Math.abs(lastElement.left - nextElement.left) > 300) {
        // 尝试最近的两个元素，找不到则停止
        lastElement = allElements[i - 2];
        if(!lastElement || !document.getElementById(lastElement.id)) {
          lastElement = allElements[i - 3];
        }
        // continue;
      }
      if(!lastElement) {
        continue;
      }
      var lastElementDom = document.getElementById(lastElement.id);
      if(lastElement.originTop === undefined) {
        lastElement.originTop = lastElement.top;
      }
      if(lastElement.top !== '' && nextElement.top !== '' && !isNaN(Number(lastElement.top)) && !isNaN(Number(nextElement.top))) {
        var originTop = Number(nextElement.top);  //原本的top
        nextElement.originTop = originTop;
        // 如果上一个元素不存在，则直接放到该位置
        // 如果上一个元素存在，则 = 元素位置 - 上一个元素与上一元素的实际位置差 + 上一个元素实际高度差
        if(!adjustTop[lastElement.top]) {
          // 相差15说明大致同行，除矩形wt=27外
          if(lastElement.wt !== '27' && Math.abs(lastElement.originTop - nextElement.top) <= 15) {
            nextElement.top = lastElement.top;
          } else {
            nextElement.top = !lastElementDom ? lastElement.top : (originTop - moveDiff + (lastElement.eleHeightDiff || 0));
          }
          // adjustTop[lastElement.top] = '1';
        }
        // 当前元素实际高度-设计的高度，为下一个元素应加上此高度，避免重叠，误差5忽略
        if($('#' + nextElement.id).closest('.w-con').outerHeight() > nextElement.height + 5) {
          nextElement.eleHeightDiff = ($('#' + nextElement.id).closest('.w-con').outerHeight() || 0) - nextElement.height;
        } 
        // else {
        //   nextElement.eleHeightDiff = 0;
        // }
        moveDiff = Number(originTop) - Number(nextElement.top);
      }
      if (nextElementDom) {
        if(nextElement.lyId) {
          // 获取下一个元素的 top 值，并将其减少（上移）
          $(nextElementDom).closest('.w-con').css('top', `${nextElement.top}px`);
        } else {
          // 布局容器
          $(nextElementDom).css('top', `${nextElement.top}px`);
        }
      }
    }
  }

  // 编辑器：提取模板内部推导数据，组件属性等
  function getWidgetInnerData(htmlContent) {
    var _this = this;
    var tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    var wtData = JSON.parse($(tempDiv).find('#rt-wt-data').text() || '""');  //控件属性
    _this.wtData = wtData && wtData.length ? wtData[0].widgets : [];
    // 数据推导规则
    var inferRuleData = JSON.parse($(tempDiv).find('#rt-wt-rule').text() || '""');
    _this.inferRuleData = inferRuleData;
    getAllAbsoluteElement.call(_this);
  }
  
  // 模板编辑器生成的模板调用方法渲染图或签名等脚本
  function renderPatternScript() {
    var _this = this;
    window.rtStructure = window.findCurStructure(_this.ele, window.instanceList);
    window.curElem = $(_this.ele);
    // 来自编辑器生成模板的容器，兼容原逻辑
    if(window.rtStructure) {
      window.rtStructure.diffConfig = {
        pdfContainer: document.querySelector('[is-rt-editor="1"] .page')
      }
    }
    try {
      if(widgetRender) {
        widgetRender();
      }
    } catch(e) {
      console.error(e);
    }
    var selfRptImageList = _this.enterOptions ? (_this.enterOptions.rptImageList || []) : [];  //报告图像
    var publicInfo = _this.enterOptions ? (_this.enterOptions.publicInfo || {}) : {};  //全局检查信息
    _this.reportInfo && (_this.reportInfo.rptImageList = selfRptImageList);
    // 渲染报告图像,wt=25
    if($(_this.ele).find('.rt-sr-w[wt="25"]').length) {
      // 不存在图像则移除整个控件
      if(!selfRptImageList || !selfRptImageList.length) {
        var imgLayout = $(_this.ele).find('.rt-sr-w[wt="25"]').closest('.rt-layout');
        $(_this.ele).find('.rt-sr-w[wt="25"]').closest('.w-con').remove();
        if(imgLayout.children().length == 0) {
          imgLayout.remove();
        }
      } else {
        var rptImageSrcList = [];
        for(var i = 0; i < selfRptImageList.length; i++) {
          var item = selfRptImageList[i];
          rptImageSrcList.push(item.src);
        }
        $(_this.ele).find('.rt-sr-w[wt="25"]').each(function() {
          widgetRptImageRender(rptImageSrcList, this.id);
        })
      }
    }
    // 渲染签名图像,wt=26
    if($(_this.ele).find('.rt-sr-w[wt="26"]').length) {
      $(_this.ele).find('.rt-sr-w[wt="26"]').each(function(signDom) {
        var signId = this.id;
        var wtAttr = getWtAttrByIdFromWtData.call(_this, signId);
        // 有绑定对应节点显示对应节点的签名
        if(signId && _this.idAndDomMap[signId] && _this.idAndDomMap[signId].value) {
          // var signUrl = getSignImgHandler({ staffNo: _this.idAndDomMap[signId].value });
          var signUrl = getSignImgHandler_sr({ staffNo: _this.idAndDomMap[signId].value }); // 使用结构化的接口
          widgetSignImageRender(signUrl, signId);
          return;
        }
        if(wtAttr && wtAttr.signDoctorType && publicInfo[wtAttr.signDoctorType]) {
          var signUrl = getSignImgHandler({ staffNo: publicInfo[wtAttr.signDoctorType] });
          widgetSignImageRender(signUrl, signId);
        }
      })
    }
    // 渲染多行文本域只读
    if($(_this.ele).find('.rt-sr-w[wt="24"]').length) {
      $(_this.ele).find('.rt-sr-w[wt="24"]').each(function() {
        var wtAttr = getWtAttrByIdFromWtData.call(_this, this.id);
        if(wtAttr && wtAttr.readonly) {
          $(this).attr('readonly', true);
        }
      })
    }
    if(_this.widgetEvent) {
      eval(_this.widgetEvent);
    }
  }

  // 模板编辑器生成的模板渲染完成后处理元素等
  function renderWidgetAfterLoaded() {
    var _this = this;
    try {
      // 初始化随内容撑开事件
      if(_renderHeightResizeByContent) {
        _renderHeightResizeByContent();
      }

      // 百分比宽度转换
      if(_renderWidgetWidthByPercent) {
        _renderWidgetWidthByPercent();
      }
    } catch(e) {
      console.error(e)
    }
  }
  
  // 编辑器：推导描述和诊断等推导规则内容处理
  function saveReportInfoByPattern(curKeyValData) {
    var _this = this;
    if(!_this.inferRuleData || !_this.inferRuleData.length) {
      return;
    }
    var inferRuleData = _this.inferRuleData;
    console.log('inferRuleData', inferRuleData);
    for(var i = 0; i < inferRuleData.length; i++) {
      var item = inferRuleData[i];
      var dependAttr = ['description', 'impression', 'recommendation'];  //独立挂在this上的值，其他放在this.reportInfo
      if(dependAttr.indexOf(item.inferName) > -1) {
        _this[item.inferName] = formatPvtHandler.call(_this, item.inferRule, curKeyValData);
      } else {
        if(!_this.reportInfo) {
          _this.reportInfo = {};
        }
        _this.reportInfo[item.inferName] = formatPvtHandler.call(_this, item.inferRule, curKeyValData);
      }
    }
  }

  // 模板编辑器生成的模板-预览样式处理,isRemove是否为移除节点
  function renderPatternStyle(widget, isRemove) {
    var _this = this;
    if(!_this.isWriteByEditor) {
      return;
    }
    var parentWon = $(widget).closest('.w-con');   //编辑器元素均包含在w-con中
    var widgetId = $(widget).attr('id');
    // if(transWtLikeInput.indexOf(Number($(widget).attr('wt'))) > -1) {
    //   var tempDom = document.createElement('span');
    //   tempDom.innerHTML = $(widget).val() || '';
    //   tempDom.className = "text-con";
    //   tempDom.style.whiteSpace = 'pre-wrap';
    //   tempDom.style.wordBreak = 'break-word';
    //   if(parentWon.length) {
    //     parentWon.attr('id', widgetId);
    //   } else {
    //     tempDom.id = widgetId;
    //   }
    //   $(widget).after($(tempDom));
    //   $(widget).remove();
    // }
    if(parentWon.length) {
      if(isRemove) {
        parentWon.remove();
      } else {
        parentWon.css({
          'border': 'none',
          'backgroundColor': 'transparent',
        });
        // 图像的高度不转
        if(_this.idAndDomMap[widgetId] && wtIsImage.indexOf(_this.idAndDomMap[widgetId].wt) === -1) {
          parentWon.css({'height': 'auto'});
        }
      }
    }
    if(isRemove) {
      var layoutCon = $(widget).closest('.rt-layout');
      // 布局容器无内容同时移除
      if(layoutCon.length && layoutCon.children().length == 0) {
        layoutCon.remove();
      }
    }
  }

  // 获取idAndDomMap中节点的值
  function getNodeVal(id) {
    if (!id) {
      return;
    }
    var _this = this;
    var node = _this.idAndDomMap[id];
    if (!node) {
      return;
    }
    var val = node.value;
    return val || '';
  }
  // 覆盖idAndDomMap中节点的值
  function setNodeVal(id, val) {
    if (!id) {
      return;
    }
    var _this = this;
    var node = _this.idAndDomMap[id];
    if (!node) {
      return;
    }
    node.value = val;
  }
  // 获取多个节点的拼接内容
  function getMultiVal(ids, separator) {
    if (!ids || ids.constructor !== Array || ids.length === 0) {
      return;
    }
    var _this = this;
    var arr = [];
    for (var i = 0; i < ids.length; i++) {
      var node = _this.idAndDomMap[ids[i]];
      var val = node && node.value;
      val && arr.push(val);
    }
    return arr.join(separator || '');
  }
  // 删除接对对象记录中的节点
  function delNodesData(ids) {
    if (!ids || ids.constructor !== Array || ids.length === 0) {
      return;
    }
    var _this = this;
    for (var i = 0; i < ids.length; i++) {
      $('#' + ids[i]).parent().hide();
    }
  }

  // 判断签名组件是否已签名（至少签有一个就算已签）
  function isSignPannelDraw() {
    var _this = this;
    var isSigned;
    var signPannelType = _this.checkSignPannelType();
    if (signPannelType === 38) {
      $('.rt-sr-w[wt="38"]').each(function () {
        if ($(this).val()) {
          isSigned = true;
        }
      });
    }
    if (signPannelType === 40) {
      $('.rt-sr-w[wt="40"]').each(function () {
        if ($(this).val()) {
          isSigned = true;
        }
      });
    }
    return isSigned;
  }

  // 保存staffNo到告知医生签名组件
  function setSignImageCurDoctor(staffNo) {
    var _this = this;
    var isSet;
    $(_this.ele).find('.rt-sr-w[wt="26"]').each(function() {
      var signId = this.id;
      var wtAttr = getWtAttrByIdFromWtData.call(_this, signId);
      if(wtAttr && wtAttr.signDoctorType && wtAttr.signDoctorType === '_SIGN_CUR_DOCTOR_') {
        _this.idAndDomMap[signId].value = staffNo;
        isSet = true;
      }
    });
    isSet && _this.init(true);
  }

  // 设置签署时间
  function setSignDateTime(type, isClear) {
    var _this = this;
    $(_this.ele).find('.rt-sr-w[wt="39"]').each(function() {
      var signId = this.id;
      var wtAttr = getWtAttrByIdFromWtData.call(_this, signId);
      if(wtAttr && wtAttr.signDateTimeType && wtAttr.signDateTimeType === type) {
        this.value = isClear ? '' : window.dayjs().format('YYYY-MM-DD HH:mm:ss');
      }
    });
  }

  // 检测模板包含的签名类型
  function checkSignPannelType() {
    var _this = this;
    var ylzSign = $('.rt-sr-w[wt="38"]').length > 0; // 手写签名板
    var bjcaSign = $('.rt-sr-w[wt="40"]').length > 0; // BJCA方案签名
    if (ylzSign) {
      return 38;
    }
    if (bjcaSign) {
      return 40;
    }
  }

  // 获取信手书签名组件的渠道号
  function getSignBusinessParam() {
    var _this = this;
    var $els = $('.rt-sr-w[wt="40"]');
    if ($els.length === 0) {
      return;
    }
    var wtAttr = getWtAttrByIdFromWtData.call(_this, $els[0].id);
    if(wtAttr && wtAttr.businessParam) {
      return wtAttr.businessParam;
    }
  }

  // 获取信手书签名组件列表
  function getSignWidgetList() {
    var _this = this;
    var $els = $('.rt-sr-w[wt="40"]');
    if ($els.length === 0) {
      return [];
    }
    let arr = [];
    $els.each(function () {
      arr.push({
        id: this.id,
        name: this.getAttribute('data-srname'),
        isSigned: !!this.value
      });
    });
    return arr;
  }

  // 设置信手书签名组件图片
  function setBjcaSignimage(base64, ids) {
    var _this = this;
    if (ids && ids.length > 0) {
      ids.forEach(function(id) {
        $('#' + id).val(base64);
        $('#' + id).siblings('img').attr('src', base64);
      });
    } else {
      $('.rt-sr-w[wt="40"]').each(function () {
        $(this).val(base64);
        $(this).siblings('img').attr('src', base64);
      });
    }
  }

  function RtStructure(id) {
    var isInstanceExist;
    var ele = document.querySelector(id);
    for (var i = 0; i < instanceList.length; i++) {
      var instance = instanceList[i];
      if (instance.ele === ele) {
        return instance;
      }
    }
    this.ele = ele;
    this.widgetPropMap = null;  //模板中的控件属性集合
    this.idAndDomMap = null;  //id和dom的Json对应关系表
    this.curResultData = null;  //最新的结果集合
    this.curKeyValData = null;  //最新的结果JSON对象
    this.errorMsg = null;  //错误提示文本
    this.description = null;  //报告描述
    this.impression = null;  //报告印象
    this.recommendation = null;  //报告建议
    this.reportInfo = {};  //报告信息
    this.examInfo = null;  //检查信息
    this.docOrign = null;  //额外的设备数据
    this.srcUrl = null;  //资源文件存放地址
    this.enterOptions = null;  //调取这个文件的所有入参
    this.diffConfig = {};    //其他模板配置条件
    this.eleProps = {};    //元素属性
    this.wtData = [];  //来自模板编辑器的属性集合
    this.isWriteByEditor = false;  //通过编辑器生成的模板标识
    this.widgetEvent = null;  //通过编辑器生成的事件
    this.inferRuleData = [];  //来自编辑器的推导数据规则
    this.absoluteElePosition = [];  //来自模板编辑器的定位元素的集合，按lyId分组
    instanceList.push(this);
    window.instanceList = instanceList;
    window.findCurStructure = findCurStructure;
    window.adjustParentHeight = adjustParentHeight;  //计算定位元素高度调整父级高度
  }

  RtStructure.prototype.onWidgetChange = function () { };
  RtStructure.prototype.setChildrenDisabled = setChildrenDisabled;
  RtStructure.prototype.exportStructureData = exportStructureData; //生成保存结果
  RtStructure.prototype.rebuildStructureReport = rebuildStructureReport;  //绘制编辑页面
  RtStructure.prototype.previewStructureReport = previewStructureReport;  //绘制预览页面
  RtStructure.prototype.createDescAndImpText = function () { };  //生成报告的描述和诊断文本内容
  RtStructure.prototype.resToDescription = resToDescription;
  RtStructure.prototype.initChildDisabled = initChildDisabled;
  RtStructure.prototype.init = init;
  RtStructure.prototype.setFormItemValue = setFormItemValue;  //控件赋值方法
  RtStructure.prototype.setValByCode = setValByCode;
  RtStructure.prototype.getNodeVal = getNodeVal;
  RtStructure.prototype.setNodeVal = setNodeVal;
  RtStructure.prototype.getMultiVal = getMultiVal;
  RtStructure.prototype.delNodesData = delNodesData;
  RtStructure.prototype.setSignImageCurDoctor = setSignImageCurDoctor;
  RtStructure.prototype.setSignDateTime = setSignDateTime;
  RtStructure.prototype.checkSignPannelType = checkSignPannelType;
  RtStructure.prototype.isSignPannelDraw = isSignPannelDraw;
  RtStructure.prototype.getSignBusinessParam = getSignBusinessParam;
  RtStructure.prototype.setBjcaSignimage = setBjcaSignimage;
  RtStructure.prototype.getSignWidgetList = getSignWidgetList;

  window.RtStructure = RtStructure;
  window.loadTemplateScriptAndCss = loadTemplateScriptAndCss;
  
})();
