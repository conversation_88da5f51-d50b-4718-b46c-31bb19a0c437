$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#gdfytlzd1', 'edit');
  // initHtmlScript('#gdfytlzd1', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var staffType16List = []; // 检查者列表
var staffType0List = []; // 审核医师
var userInfo = null; // 当前登录用户
var reqPhysician; // 审核医师名字
// var userInfo = {
//   deptCode: "011013",
//   deptName: "信息管理科(番禺)",
//   isLoginOther: true,
//   loginId: "ADMIN",
//   name: "关礼源",
//   reload: false,
//   staffNo: "0000",
//   userId: "ADMIN",
//   userName: "关礼源",
//   workNo: "ADMIN",
// };
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('gdfytlzd1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    userInfo = publicInfo.userInfo;
    reqPhysician = rtStructure.examInfo.reqPhysician;
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '', //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '', //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '', //审核医生
    affirmReporterNo: '', //审核医生流水号
    affirmDate: '', //审核日期
    reDiagReporter: '', //复诊报告医生
    reDiagReporterNo: '', //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
}

function initPage(type) {
  if (type === 'edit') {
    staffType16List = getDictUsersBaseList({staffType: '1,6'});
    staffType16List = transformOptList(staffType16List, 'name');
    staffType0List = getDictUsersBaseList({staffType: '0'});
    staffType0List = transformOptList(staffType0List, 'name');
    setCurrUser();
    initDropdown();
    initDatePicker();
    // 清空选择
    $('.clear-handler').on('click', function() {
      var names = $(this).attr('data-for-names').split(',');
      names.forEach(function(na) {
        $('[name="'+ na +'"]').removeClass('rt-checked').attr('checked', false);
      });
    });
  }
  if (type === 'view') {
    checkItemValue();
    showCaImg();
  }
}

// 默认用户
function setCurrUser() {
  // 检查者
  if (userInfo && !$('.staffType16').val()) {
    $('.staffType16').val(userInfo.name);
    $('.staffType16StaffNo').text(userInfo.staffNo);
  }

  // 审核医师，默认读取开单医生
  var reqPhysicianStaffNo;
  if (reqPhysician) {
    for (var i = 0; i < staffType0List.length; i++) {
      if (staffType0List[i].name === reqPhysician) {
        reqPhysicianStaffNo = staffType0List[i].staffNo;
        break;
      }
    }
  }
  if (reqPhysician && reqPhysicianStaffNo && !$('.staffType0').val()) {
    $('.staffType0').val(reqPhysician);
    $('.staffType0StaffNo').text(reqPhysicianStaffNo);
  }
}
// 初始化下拉
function initDropdown() {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: '.staffType16',
    data: staffType16List,
    className: 'laySelLab',
    click: function(obj) {
      this.elem.val(obj.title);
      $('.staffType16StaffNo').text(obj.staffNo);
    },
  });
  dropdown.render({
    elem: '.staffType0',
    data: staffType0List,
    className: 'laySelLab',
    click: function(obj) {
      this.elem.val(obj.title);
      $('.staffType0StaffNo').text(obj.staffNo);
    },
  });
}
// 转换下拉数据格式
function transformOptList(list, titleName) {
  let arr = [];
  list.forEach((item,index) => {
    if(titleName) {
      arr.push({ ...item,title:item[titleName],id:index,templet: `<span title='${item[titleName]}'>${item[titleName]}</span>` });
    }else {
      arr.push({ title:item,id:index,templet: `<span title='${item}'>${item}</span>` });
    }
  })
  return arr;
}
// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0];
  layui.config({dir: path + 'template-lib/plugins/layui/'});
  !$('.date-wrap01').val() && $('.date-wrap01').val(layui.util.toDateString(new Date(), 'yyyy-MM-dd'));
  layui.use('laydate', function () {
      var laydate = layui.laydate;
      laydate.render({
        elem: '.date-wrap01',
        type: 'date',
        trigger: 'click',
        format: 'yyyy-MM-dd',
        ready: function() {

        },
        done: function(value){

        }
      });
  });
}
// 打印时显示ca签名图像
function showCaImg() {
  $('.staffType16').hide();
  $('.staffType16Ca').attr('src', api.getExamRptSignImage + '?staffNo=' + $('.staffType16StaffNo').text()).show();
  $('.staffType0').hide();
  $('.staffType0Ca').attr('src', api.getExamRptSignImage + '?staffNo=' + $('.staffType0StaffNo').text()).show();
}
// 打印时隐藏未填写的节点
function checkItemValue() {
  var groupList = [
    'zxjc', // 专项检查
    'qdabr', // 气导ABR
    'gdabr',
    'dcyabr',
    'assr',
    'zdxdpoae',
    'ewwydw',
    'hz1000',
    'hz260',
    'zdjl',
    'rmk',

    'abr-yz',
    'abr-qfq-ms',
    'abr-qfq',
    'abr-bxfh',
    'gdabr-left',
    'gdabr-right',
    'dcyabr-left',
    'dcyabr-right',
    'assr-left',
    'assr-right'
  ];
  groupList.forEach(function(g) {
    var isFilled;
    var curr = $('.' + g);
    curr.each(function() {
      var chb = $(this).find('input[type="checkbox"]:checked');
      var rdo = $(this).find('input[type="radio"]:checked');
      var txt = $(this).find('input[type="text"]');
      var txa = $(this).find('textarea');
      if (chb.length > 0) { isFilled = true; }
      if (rdo.length > 0) { isFilled = true; }
      txt.each(function() { if (this.value.trim()) { isFilled = true; } });
      txa.each(function() { if (this.value.trim()) { isFilled = true; } });
    });
    if (!isFilled) {
      curr.hide();
    }
  });

  // 隐藏特定未填写的列
  var colList = [
    'qdabr1', 'qdabr2', 'qdabr3', 'qdabr4', 'qdabr5', 'qdabr6',
    'dcyabr500', 'dcyabr1000', 'dcyabr2000', 'dcyabr4000',
    'assr500', 'assr1000', 'assr2000', 'assr4000',
    'hz260_1', 'hz260_2', 'hz260_3', 'hz260_4', 'hz260_5',
  ];
  colList.forEach(function(name) {
    var val = getVal('.' + name + ' input');
    if (!val) {
      $('.' + name).remove();
    }
  });
}
// 获取不定向选择
function getImpression() {
  var zdjlText = [];
  var zdjlLeft = $('[name="zdjl-left"]:checked');
  var zdjlRight = $('[name="zdjl-right"]:checked');
  if (zdjlLeft.length > 0) {
    zdjlText.push('左耳' + zdjlLeft.val());
  }
  if (zdjlRight.length > 0) {
    zdjlText.push('右耳' + zdjlRight.val());
  }
  return zdjlText.join(',');
}