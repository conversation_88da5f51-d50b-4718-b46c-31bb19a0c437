#mcgxbact1 {
  font-size: 14px
}

#mcgxbact1 * {
  box-sizing: border-box;
}

#mcgxbact1 .sel-light {
  color: #1885F2;
  background: #ECF5FF;
}

#mcgxbact1 .pl-10 {
  padding-left: 10px;
}

#mcgxbact1 .pl-12 {
  padding-left: 12px;
}

#mcgxbact1 .pl-42 {
  padding-left: 42px;
}

#mcgxbact1 .mb-2 {
  margin-bottom: 2px;
}

#mcgxbact1 .mr-12 {
  margin-right: 12px;
}

#mcgxbact1 .ml-8 {
  margin-left: 8px;
}

#mcgxbact1 .ml-12 {
  margin-left: 12px;
}

#mcgxbact1 .ml-24 {
  margin-left: 24px;
}

#mcgxbact1 .ml-26 {
  margin-left: 26px;
}

#mcgxbact1 .ml-28 {
  margin-left: 28px;
}

#mcgxbact1 .ml-74 {
  margin-left: 74px;
}

#mcgxbact1 .ml-102 {
  margin-left: 102px;
}

#mcgxbact1 .wid-70 {
  min-width: 70px;
}

#mcgxbact1 .wid-84 {
  width: 84px;
}

#mcgxbact1 .wid-96 {
  width: 96px;
}

#mcgxbact1 .wid-98 {
  width: 98px;
}

#mcgxbact1 .wid-186 {
  width: 186px;
}

#mcgxbact1 .top-bar {
  border-bottom: 1px solid #c8d7e6;
  background: #e8f3ff
}

#mcgxbact1 .top-bar .bar-inner {
  width: 960px;
  margin: 0 auto
}

#mcgxbact1 .top-bar span {
  margin-left: 12px;
  font-weight: bold;
  font-size: 18px;
  color: #000;
  line-height: 39px
}

#mcgxbact1 .body-wrap {
  width: 960px;
  margin: 0 auto;
  padding: 12px 12px 12px 0;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  background: #fff
}

#mcgxbact1 .body-wrap .p-row {
  overflow: hidden;
  margin-bottom: 8px;
  color: #000
}

#mcgxbact1 .body-wrap .p-row:last-child {
  margin-bottom: 0
}

#mcgxbact1 .body-wrap .lb {
  width: 118px;
  text-align: right;
  margin-top: 3px;
  float: left;
  line-height: 28px;
  color: #303133
}

#mcgxbact1 label {
  display: inline-block;
  cursor: pointer;
}

#mcgxbact1 label+label {
  margin-left: 12px;
}

#mcgxbact1 input[type="text"] {
  vertical-align: top;
  padding: 0 4px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

#mcgxbact1 input[type="radio"], #mcgxbact1 input[type="checkbox"] {
  vertical-align: middle;
  cursor: pointer;
}

#mcgxbact1 textarea, #mcgxbact1 select {
  padding: 4px 10px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

#mcgxbact1 .body-wrap .g-item {
  padding: 3px 12px;
  margin-left: 118px;
  min-height: 36px;
  line-height: 28px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  overflow: hidden
}

#mcgxbact1 .body-wrap .g-item.view-no {
  padding: unset;
  margin: unset;
  min-height: unset;
  line-height: unset;
  background: unset;
  border: unset;
  overflow: unset;
}

#mcgxbact1 .body-wrap .g-item.with-textarea {
  padding: 0;
  border: none
}

#mcgxbact1 .bz-item {
  display: inline-block;
  width: 140px;
  height: 32px;
  padding: 3px 8px 3px 12px;
  line-height: 22px;
  border-right: 1px solid #dcdfe6;
}

#mcgxbact1 .visit-status {
  color: #1885F2;
}

#mcgxbact1 .bz-item .edit-btn {
  display: inline-block;
  width: 44px;
  height: 24px;
  background: #ECF5FF;
  border-radius: 3px;
  border: 1px solid #B3D8FF;
  color: #1885F2;
  text-align: center;
}

#mcgxbact1 .cont {
  display: inline-block;
  width: 727px;
  background: #EBEEF5;
  padding: 3px 8px 3px 12px;
  border: 1px solid #DCDFE6;
}

#mcgxbact1 .lb-wid {
  display: inline-block;
  text-align: right;
  vertical-align: top;
}

#mcgxbact1 .left-cont {
  display: inline-block;
  background: #F5F7FA;
  border-right: 1px solid #DCDFE6;
  vertical-align: top;
}

#mcgxbact1 .right-cont {
  display: inline-block;
  background: #EBEEF5;
  vertical-align: top;
}

#mcgxbact1 .lb-box {
  width: 100%;
  height: 28px;
  padding-left: 10px
}

#mcgxbact1 .lb-box+.lb-box {
  margin: 0;
}

#mcgxbact1 .l1-box {
  background-color: unset;
  width: 102px;
  padding-top: 8px;
}

#mcgxbact1 .r1-box {
  width: 698px;
  height: 174px;
  padding: 8px 12px;
}

#mcgxbact1 .r2-box {
  width: 128px;
  height: 100%;
  border-right: 1px solid #DCDFE6;
}

#mcgxbact1 .r3-box {
  width: 724px;
  height: 104px;
  padding: 8px 12px;
}

#mcgxbact1 .r2-right {
  width: 565px;
  height: 100%;
  display: inline-block;
}

#mcgxbact1 .footer-wrap {
  background: #f5f7fa;
  border-top: 1px solid #dcdfe6;
  width: 100%;
  position: fixed;
  bottom: 0;
}

#mcgxbact1 .footer-wrap .footer-inner {
  width: 960px;
  margin: 0 auto;
  padding: 8px 0
}

#mcgxbact1 .footer-wrap .info-item {
  overflow: hidden
}

#mcgxbact1 .footer-wrap .info-item .lb {
  width: 70px;
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
  font-size: 18px;
  color: #000;
  text-align: right;
  margin-right: 8px;
}

#mcgxbact1 .footer-wrap .info-item textarea {
  width: 100%;
  height: 186px;
  padding: 0 4px;
  background: #fff;
  border-radius: 3px;
  line-height: 24px;
  border: 1px solid #c0c4cc
}

#mcgxbact1 .view-show {
  display: none;
}
#mcgxbact1 .kj-item {
  border-bottom: 1px solid #dcdfe6;
  display: inline-block;
  width: 548px;
  height: 32px;
  vertical-align: top;
}

#mcgxbact1 .bracket {
  display: inline-block;
  vertical-align: middle;
}
#mcgxbact1 .view-none1 {
  display: none;
}
#mcgxbact1 .yfz-view {
  display: none;
}
#mcgxbact1 .md-cont {
  border: 1px solid #DCDFE6;
  display: inline-block;
  width: 630px;
}

#mcgxbact1 .layui-inline input{
  width: 100%;
  height: 28px;
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
  border: 1px solid #C0C4CC;
}
#mcgxbact1 .showInt {
  background: #fff;
  width: 96px;
  display: inline-block;
  margin-right: 8px;
}
#mcgxbact1 .showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}

[isview="true"] #mcgxbact1 .view-show {
  display: block;
}
[isview="true"] #mcgxbact1 .kj-item {
  display: none;
}
[isview="true"] #mcgxbact1 .yfz-show,
[isview="true"] #mcgxbact1 .zz-show,
[isview="true"] #mcgxbact1 .zfg-view,
[isview="true"] #mcgxbact1 .gzt-view {
  display: none;
}
[isview="true"] #mcgxbact1 .yfz-view,
[isview="true"] #mcgxbact1 .zz-view {
  display: inline-block;
}

[isview="true"] #mcgxbact1 .view-show.inline {
  display: inline-block;
}

[isview="true"] #mcgxbact1 .view-none {
  display: none;
}

[isview="true"] #mcgxbact1 .showInt {
  background: none
}
[isview="true"] #mcgxbact1 .showInt::after {  
  display: none;
}

[isview="true"] #mcgxbact1 .sel-light {
  color: unset;
  background: unset;
}

[isview="true"] #mcgxbact1 .edit-btn {
  display: none;
}

[isview="true"] #mcgxbact1 .visit-status {
  color: unset;
}

[isview="true"] #mcgxbact1 .bz-item {
  display: none;
}

[isview="true"] #mcgxbact1 .zz-block {
  display: block !important;
}

[isview="true"] #mcgxbact1 .cont,
[isview="true"] #mcgxbact1 .md-cont,
[isview="true"] #mcgxbact1 .left-cont,
[isview="true"] #mcgxbact1 .right-cont {
  background: unset;
  border: unset;
  padding: unset !important;
  width: unset !important;
  height: unset !important;
}

[isview="true"] #mcgxbact1 .mb-8 {
  margin: 0 !important;
}

[isview="true"] #mcgxbact1 .body-wrap .g-item {
  padding: 3px 12px !important;
  border-bottom: 1px solid #dcdfe6 !important;
}

[isview="true"] #mcgxbact1 .body-wrap .g-item.view-no {
  padding: 3px 12px;
  margin-left: 118px;
  min-height: 36px;
  line-height: 28px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  overflow: hidden
}

[isview="true"] #mcgxbact1 .lb-wid {
  display: inline;
}

[isview="true"] #mcgxbact1 .view-nomg {
  margin: 0 !important;
}

[isview="true"] #mcgxbact1 .mjm-view,
[isview="true"] #mcgxbact1 .gjm-view,
[isview="true"] #mcgxbact1 .xqjm-view{
  display: inline-block !important;
  width: 827px;
}
[isview="true"] #mcgxbact1 .gjm-view,
[isview="true"] #mcgxbact1 .xqjm-view{
  margin-left:  0;
}
[isview="true"]  #mcgxbact1 .pl-12{
  padding: 0 !important;
}
#mcgxbact1 .view-con{
  display: none !important;
}
[isview="true"]  #mcgxbact1 .edit-con{
  display: none !important;
}
[isview="true"]  #mcgxbact1 .view-con{
  display: inline-block !important;
}

.red-tip {
  display: none;
  color: red;
  font-size: 12px;
}

#mcgxbact1 .lbjyc-wrap .right-cont {width: 100px;}
#mcgxbact1 .lbjyc-wrap .r2-right {width: 618px; padding-top: 10px;}
#mcgxbact1 .pre-req-mark:before {
  content: "*";
  color: red;
}