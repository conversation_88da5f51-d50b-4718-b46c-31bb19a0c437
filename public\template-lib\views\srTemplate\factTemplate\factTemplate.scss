// 肺癌CT报告模板样式
#fact1 {
    font-size: 14px;
    background-color: #fff;
    height: 100%;
}
.preview {
    .preview-item {
        display: flex;
        margin-bottom: 4px;
        .preview-item-label {
            width: 100px;
            text-align: right;
            font-weight: 600;
            font-size: 14px;
            color: #303133;
        }
        .preview-item-content {
            flex: 1;
        }
    }
}
.container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .container-header {
        height: 40px;
        line-height: 40px;
        background: #E8F3FF;
        box-shadow: inset 1px 0px 0px 0px #C8D7E6, inset -1px -1px 0px 0px #C8D7E6;

        .pat-info {
            width: 960px;
            margin: 0 auto;
            font-size: 18px;
            font-weight: bold;
        }
    }

    .container-content {
        flex: 1;
        overflow: auto;
        width: 960px;
        margin: 0 auto;
        background: #fff;
        border-right: 1px solid #DCDFE6;
        border-left: 1px solid #DCDFE6;

        .lb-container {
            display: flex;
            flex-direction: column;
            row-gap: 2px;
            .lb-item {
                display: flex;
                align-items: center;

                .lb-col-1 {
                    width: 84px;
                    text-align: right;
                    color: #303133;
                    font-weight: 600;
                }
                .lb-col-2 {
                    width: 154px;
                    color: #000;
                }
                .lb-col-3 {
                    width: 160px;
                    margin:0 8px;
                }
                .lb-col-4 {
                    width: 160px;
                    margin-right: 8px;
                }
            }

        }

        .bz-item {
            padding: 8px;
        }

        .bz-form-item {
            display: flex;
            margin-top: 8px;

            .bz-form-item-label {
                width: 110px;
                text-align: right;
                padding-top: 2px;
            }

            .bz-form-item-content {
                flex: 1;

            }
        }

        .base-card {
            background: #F5F7FA;
            border: 1px solid #DCDFE6;
        }

        .sub-card {
            background: #EBEEF5;
            border: 1px solid #C8D7E6;
        }

        .bz-wrap {
            margin-top: 8px;
            background: #F5F7FA;
            border: 1px solid #DCDFE6;
        }

        .bz-tit-ls::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 6px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 4px;
        }

        .close-icon {
            color: #303133;
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-weight: normal;
            font-size: 18px;
            font-family: "Microsoft YaHei";
            text-align: center;
            cursor: pointer;
        }

        .close-icon:hover {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #e1e1e1;
            color: #fff;
        }

        .bz-tit-i.act {
            position: relative;
            background: #EBEEF5;
            color: #1885F2;
            font-weight: bold;
        }

        .bz-tit-i {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 5px 20px;
            color: #303133;
            border-right: 1px solid #DCDFE6;
        }

        .bz-tit-ls {
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            height: 32px;
            border-bottom: 1px solid #DCDFE6;
            display: flex;
            display: -webkit-box;
            display: -moz-box;
        }
    }

    .container-footer {
        height: 104px;
        background: #E8F3FF;
        border: 1px solid #DCDFE6;
        padding: 8px 0;

        .footer-impression {
            height: 100%;
            display: flex;
            width: 960px;
            margin: 0 auto;

            .impression-label {
                display: inline-block;
                font-size: 18px;
                font-weight: bold;
                // margin-left: 72px;
                padding-right: 16px;
                width: 114px;
                text-align: right;
            }

            .impression-label-preview {
                text-align: center;
            }
        }

        .footer-impression-preview {
            border-left: 1px solid #DCDFE6;
            border-right: 1px solid #DCDFE6;
            padding: 8px;
        }
    }

    textarea {
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #C0C4CC;
        padding: 6px 12px;
        width: 100%;
        height: 120px;
    }

    .rt-sr-r {
        margin-right: 4px;
    }

    .p-8 {
        padding: 8px;
    }

    .pl-24 {
        padding-left: 24px;
    }

    .pr-4 {
        padding-right: 4px;
    }

    .pr-24 {
        padding-right: 24px;
    }

    .pt-8 {
        padding-top: 8px;
    }

    .pb-8 {
        padding-bottom: 8px;
    }

    .mt-12 {
        margin-top: 12px;
    }

    .mt-8 {
        margin-top: 8px;
    }

    .mt-6 {
        margin-top: 6px;
    }

    .mt-4 {
        margin-top: 4px;
    }

    .m-0-6 {
        margin: 0 6px;
    }

    .w-10 {
        width: 10px;
    }

    .w-15 {
        width: 15px;
    }

    .w-20 {
        width: 20px;
    }

    .w-25 {
        width: 25px;
    }

    .w-30 {
        width: 30px;
    }

    .w-35 {
        width: 35px;
    }

    .w-40 {
        width: 40px;
    }

    .w-45 {
        width: 45px;
    }

    .w-50 {
        width: 50px;
    }

    .w-55 {
        width: 55px;
    }

    .w-60 {
        width: 60px;
    }

    .w-65 {
        width: 65px;
    }

    .w-70 {
        width: 70px;
    }

    .w-75 {
        width: 75px;
    }

    .w-80 {
        width: 80px;
    }

    .w-85 {
        width: 85px;
    }

    .w-90 {
        width: 90px;
    }

    .w-95 {
        width: 95px;
    }

    .w-100 {
        width: 100px;
    }

    .w-105 {
        width: 106px;
    }

    .w-110 {
        width: 110px;
    }

    .w-115 {
        width: 115px;
    }

    .w-120 {
        width: 120px;
    }

    .w-125 {
        width: 125px;
    }

    .w-130 {
        width: 130px;
    }

    .w-135 {
        width: 135px;
    }

    .w-140 {
        width: 140px;
    }

    .w-145 {
        width: 145px;
    }

    .w-150 {
        width: 150px;
    }

    .w-155 {
        width: 155px;
    }

    .w-160 {
        width: 160px;
    }

    .w-165 {
        width: 165px;
    }

    .f-1 {
        flex: 1;
    }

    .f-1-5 {
        flex: 1.5;
    }

    .f-1-6 {
        flex: 1.6;
    }

    .f-2 {
        flex: 2;
    }

    .f-3 {
        flex: 3;
    }

    .fw-600 {
        font-weight: 600;
    }

    .a-center {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .a-start {
        display: flex;
        align-items: flex-start;
    }

    .flex {
        display: flex;

        span {
            &:first-child {
                white-space: nowrap;
            }
        }
    }

    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .text-r {
        text-align: right;
    }

    .fs-36 {
        font-size: 36px;
    }

    .fs-24 {
        font-size: 24px;
    }

    .fs-20 {
        font-size: 20px;
    }

    .mr-20 {
        margin-right: 20px;
    }
}

.form-item {
    display: flex;
    margin-top: 8px;
    padding-right: 8px;
}

.form-item-label {
    width: 108px;
    text-align: right;

}

.form-item-content {
    flex: 1;
    overflow: hidden;
}

.add-btn {
    display: inline-block;
    line-height: 30px;
    height: 30px;
    padding: 0px 8px;
    background: #1885F2;
    border-radius: 3px;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
}

img {
    vertical-align: middle;
}

.border {
    border: 1px solid #DCDFE6;
}

.layui-input {
    height: 28px !important;
}
[isView="true"] #fact1 .edit {
  display: none;
}

[isView="true"] #fact1 .preview {
  display: block;
  padding: 8px;
} 