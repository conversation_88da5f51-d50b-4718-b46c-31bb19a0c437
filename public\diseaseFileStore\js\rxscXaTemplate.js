$(function() {
  window.initHtmlScript = initHtmlScript;
  window.errorCallBack = errorCallBack;
})

var vueComp = null;
var curElem = null;
var rtStructure = null;
var over2Level = false;   //病灶出现超过2类
var firstIn = true;
var sideMapKy = {
  '左侧': 'l',
  '右侧': 'r',
  '双侧': 'db',
}
var shqcType = {   //术后类型，影响假体的结论推导
  '左侧': '',
  '右侧': '',
  '双侧': '',
}
var aiValAndSrMap = {
  '椭圆': ['椭圆形'],
  '不规则': ['不规则形'],
  '大分叶': ['分叶状'],
  '平行': ['水平位'],
  '不平行': ['垂直位'],
  '清晰': ['边界清楚'],
  '不清': ['边界不清楚'],
  '粗大钙化': ['有钙化'],
  '混合钙化': ['有钙化'],
  '微小钙化': ['微钙化', '有钙化'],
  '囊性混合回声': ['囊性', '混合回声'],
  '无回声透声差': ['无回声', '透声差'],
  '无回声透声好': ['无回声', '透声好'],
  '无回声透声尚可': ['无回声', '透声尚可'],
}
// 错误反馈回调处理
function errorCallBack(id) {
  var detailDom = $('[id="'+id+'"]').parents('.rxmb-detail');
  if(detailDom && detailDom.length) {
    if(!detailDom.is(":visible")) {
      var pid = detailDom.attr('data-pid');
      detailDom.show().siblings('.rxmb-detail').hide();
      $('[id="'+pid+'"]').addClass('act').siblings().removeClass('act');
    }
  }
  var curDom = $('[id="'+id+'"]').parents('.rt-tabs');
  if(curDom && curDom.length) {
    var key = id.split('.')[0] + '.001';
    vueComp.activeTab = key;
  }
}
function initVueComp() {
  vueComp = new Vue({
		el: '#vuePg',
		data() {
			return {
        bzTl: [  //病灶图例
          { color: '#303133', levelText: '0', fontColor: '#FFF'},
          { color: '#40C71A', levelText: '2', fontColor: '#000', sameColor: ['2', '3']},
          { color: '#40C71A', levelText: '3', fontColor: '#000', isHide: true},
          { color: '#F3F055', levelText: '4A', fontColor: '#000'},
          { color: '#F5222D', levelText: '4B', fontColor: '#000', sameColor: ['4B', '4C', '5', '6']},
          { color: '#F5222D', levelText: '4C', fontColor: '#000', isHide: true},
          { color: '#F5222D', levelText: '5', fontColor: '#000', isHide: true},
          { color: '#F5222D', levelText: '6', fontColor: '#000', isHide: true},
        ],
        bzLevel: [],
        bzList: [],  //具体病灶情况
        bzPaneList: [],
        activeTab: '',
        bzDescription: [],
        isBzFlag: false
			}
		},
    computed: {
      hasBzFlag() {
        let level = this.bzTl.map(item => item.levelText);
        this.bzLevel = level;
        return this.bzList.some(item => item.bzDesc && level.includes(item.levelText));
      }
    },
		methods: {
      // 获取病灶图例上的信息
      getBzListInfo(isUpdate) {
        this.$nextTick(() => {
          this.isBzFlag = $('[id="rxscRm-rt-1-cItem--3"]').is(':checked');
          this.bzList = [];
          !isUpdate && (this.activeTab = '');
          let pane = curElem.find('.pane-title');
          if(pane && pane.length) {
            pane.each((i, paneItem) => {
              let prefix = $(paneItem).attr('data-pre');
              if(!this.activeTab) {
                this.activeTab = prefix + '.001'
              }
              let item = getBzDetail(curElem, prefix);
              item.showDetail = false;
              item.showPointDetail = false;
              item.bzIndex = i+1;
              let desc = `病灶${i+1}：BI-RADS:${item.levelText}类；${item.propText}\n`;
              if(item.posWay === '象限定位法') {
                desc += `处于：${item.xxPos || '-'}；\n`;
              } else {
                desc += `点/距乳头(cm)：${item.clockPos || '-'}/${item.diffRt || '-'}；\n`;
              }
              if(item.bzSide) {
                desc += '大小约：' + item.bzSide + '；\n';
              }
              // desc += `大小约：${curElem.find(`[id="${prefix}.001.03.04"] .rt-sr-w`).val()}mm×${curElem.find(`[id="${prefix}.001.03.05"] .rt-sr-w`).val()}mm×${curElem.find(`[id="${prefix}.001.03.06"] .rt-sr-w`).val()}mm；\n`;
              desc += `形态：${item.shape || '-'}；\n`;
              desc += `方向：${item.fx || '-'}；\n`;
              desc += `边缘：${item.by || '-'}；\n`;
              desc += `回声类型：${item.hsType || '-'}`;
              let hsOtherArr = [];
              if(['无回声', '低回声'].indexOf(item.hsType) > -1) {
                if(item.hsBjVal) {
                  hsOtherArr.push(item.hsBjVal);
                }
                if(item.hsTsVal) {
                  hsOtherArr.push(item.hsTsVal);
                }
              }
              if(hsOtherArr.length) {
                desc += `(${hsOtherArr.join('、')})`;
              }
              desc += '；\n';
              desc += `后方回声：${item.hfhs || '-'}；\n`;
              desc += `钙化：${item.isGh || '-'}；\n`;
              desc += `血流：${item.isXl || '-'}；`;
              if(item.pos) {
                if(item.posWay === '象限定位法') {
                  if(item.xxPos) {
                    item.bzDesc = desc;
                  }
                } else {
                  if(item.clockPos) {
                    item.bzDesc = desc;
                  }
                }
              }
              // item.style = {
              //   background: this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ? 
              //     this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].color : '#F5F7FA',
              //   color: this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ? 
              //     this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].fontColor : '#FFF',
              //   top: '',
              //   left: '',
              // }
              item.style = [];  //象限改成多选
              curElem.find('.bz-name').eq(i).text(`病灶${i+1}${item.pos ? '-' + item.pos : ''}`);
              // if(item.pos === '双侧') {
              //   item.pos = curElem.find(`[name="${prefix}.001.03.01"]:checked`).val() || item.pos;
              // }
              // 包含腺体内这个选项时，该病灶则不标注到示意图
              if(!item.xxPosArr.includes('腺体内')) {
                this.setPointPosition2(item);
              }
              this.bzList.push(item);
            })
          }
          if(this.bzList.length) {
            curElem.find("#bz-area").show();
            inpNumberHandler(curElem);
          } else {
            curElem.find("#bz-area").hide();
          }
          if(rtStructure) {
            rtStructure.bzListInfo = this.bzList;
          }
        })
      },

      // 确定点的位置
      setPointPosition(item) {
        if(!$('[id="rxscRm-rt-1-cItem--3"]').is(':checked')) {
          item.style = [];
          return;
        }
        let xxObj = {
          '左侧': {
            '外上象限': {clockPos: 1.5, diffRt: 3.5},
            '外下象限': {clockPos: 4.5, diffRt: 3.5},
            '内上象限': {clockPos: 10.5, diffRt: 3.5},
            '内下象限': {clockPos: 7.5, diffRt: 3.5},
            '上象限': {clockPos: 12, diffRt: 3.5},
            '下象限': {clockPos: 6, diffRt: 3.5},
            '内象限': {clockPos: 9, diffRt: 3.5},
            '外象限': {clockPos: 3, diffRt: 3.5},
            '乳头区': {clockPos: 12, diffRt: 0},
            '中间区': {clockPos: 6, diffRt: -1.6},
          },
          '右侧': {
            '外上象限': {clockPos: 10.5, diffRt: 3.5},
            '外下象限': {clockPos: 7.5, diffRt: 3.5},
            '内上象限': {clockPos: 1.5, diffRt: 3.5},
            '内下象限': {clockPos: 4.5, diffRt: 3.5},
            '上象限': {clockPos: 12, diffRt: 3.5},
            '下象限': {clockPos: 6, diffRt: 3.5},
            '内象限': {clockPos: 3, diffRt: 3.5},
            '外象限': {clockPos: 9, diffRt: 3.5},
            '乳头区': {clockPos: 12, diffRt: 0},
            '中间区': {clockPos: 6, diffRt: -1.6},
          }
        }
        //clockPos点的方向, diffRt距离乳头的距离即半径
        let {pos, clockPos, diffRt, posWay = '时钟定位法', xxPos, xxPosArr, maxSize} = item;
        if(Number(diffRt) !== 99 && Number(diffRt) !== 0) {
          // mm换成cm
          diffRt = diffRt/10;
        }
        maxSize > 0 && (maxSize = Number((maxSize/10).toFixed(2)));
        if(!pos) {
          item.style = [];
          return;
        }
        if(posWay === '时钟定位法' && (!diffRt || Number(diffRt) === 99)) {
          diffRt = !diffRt ? 3.5 : 7;
        }
        let background = this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ? 
            this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].color : '#F5F7FA';
        let color = this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ? 
            this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].fontColor : '#FFF';

        let dataArr = posWay === '象限定位法' ? xxPosArr : [diffRt];
        if(posWay === '象限定位法' && dataArr.length >= 2) {
          dataArr = this.rebuildDataArr(dataArr, xxObj, pos);
        }
        if(dataArr && dataArr.length) {
          dataArr.forEach(xxItem => {
            if(posWay === '象限定位法') {
              if(pos && xxItem && xxObj[pos] && xxObj[pos][xxItem]) {
                clockPos = xxObj[pos][xxItem]['clockPos'];
                diffRt = xxObj[pos][xxItem]['diffRt'];
              }
            } else {
              diffRt = xxItem;
            }
            if(String(pos) && String(clockPos) && String(diffRt)) {
              let Ox = pos === '左侧' ? 225 : 81;
              let Oy = 64;  //圆心x和y
              let degreesM = 360/(12*60); //每分钟占的度数
              let totalDegree = degreesM * clockPos * 60;  //总的度数，点(时)->分
              let angle = (2*Math.PI / 360) *(totalDegree);
              // let x = Ox + Math.sin(angle) * (diffRt * 7+11);
              // let y = Oy - Math.cos(angle) * (diffRt * 7+11);
              // 判断大小范围，扩大病灶半径确定圆心
              let defaultX = Math.sin(angle) * (diffRt * 7+11);  //默认圆心
              let defaultY = Math.cos(angle) * (diffRt * 7+11);  //默认圆心
              let bzStyle = '';
              if(maxSize >= 5) {  //大号
                if(posWay === '象限定位法' && xxObj[pos][xxItem]['rtFlag']) {
                  diffRt = 2;
                }
                defaultX = Math.sin(angle) * (diffRt * 7 + 11) - 12;
                defaultY = Math.cos(angle) * (diffRt * 7 + 11) + 12;
                bzStyle = 'large';
              } else if(maxSize >=2 && maxSize < 5) {  //中号
                defaultX = Math.sin(angle) * (diffRt * 7 + 14) - 7;
                defaultY = Math.cos(angle) * (diffRt * 7 + 14) + 7;
                bzStyle = 'mediumn'
              }

              let x = Ox + defaultX;
              let y = Oy - defaultY;
              item.style.push({
                background,
                color,
                left: x.toFixed(2) + 'px',
                top: y.toFixed(2) + 'px',
                bzStyle: bzStyle
              })
            }
          })
        }
        // if(posWay === '象限定位法') {
        //   if(xxPosArr && xxPosArr.length) {
        //     xxPosArr.forEach(xxItem => {
        //       if(pos && xxItem && xxObj[pos] && xxObj[pos][xxItem]) {
        //         clockPos = xxObj[pos][xxItem]['clockPos'];
        //         diffRt = xxObj[pos][xxItem]['diffRt'];
        //       }
        //     })
        //   }
        // } else {
        //   if(Number(diffRt) === 99) {
        //     diffRt = 3.5;
        //   }
        // }
        
        // if(!pos || !clockPos || !diffRt) {
        //   return;
        // }
        // let Ox = pos === '左侧' ? 225 : 81;
        // let Oy = 64;  //圆心x和y
        // let degreesM = 360/(12*60); //每分钟占的度数
        // let totalDegree = degreesM * clockPos * 60;  //总的度数，点(时)->分
        // let angle = (2*Math.PI / 360) *(totalDegree);
        // let x = Ox + Math.sin(angle) * (diffRt * 7+11);
        // let y = Oy - Math.cos(angle) * (diffRt * 7+11);
        // if($('[id="rxscRm-rt-1-cItem--3"]').is(':checked')) {
        //   item.style.left = x.toFixed(2) + 'px';
        //   item.style.top = y.toFixed(2) + 'px';
        // } else {
        //   // item.style.left = '';
        //   // item.style.top = '';
        //   item.style = [];
        // }
      },
      // 确定点的位置
      setPointPosition2(item) {
        if(!$('[id="rxscRm-rt-1-cItem--3"]').is(':checked')) {
          item.style = [];
          return;
        }
        let xxObj = {
          '左侧': {
            '外上象限': {clockPos: 1.5, diffRt: 3.5},
            '外下象限': {clockPos: 4.5, diffRt: 3.5},
            '内上象限': {clockPos: 10.5, diffRt: 3.5},
            '内下象限': {clockPos: 7.5, diffRt: 3.5},
            '上象限': {clockPos: 12, diffRt: 3.5},
            '下象限': {clockPos: 6, diffRt: 3.5},
            '内象限': {clockPos: 9, diffRt: 3.5},
            '外象限': {clockPos: 3, diffRt: 3.5},
            '乳头区': {clockPos: 12, diffRt: 0},
            '中间区': {clockPos: 6, diffRt: -1.6},
          },
          '右侧': {
            '外上象限': {clockPos: 10.5, diffRt: 3.5},
            '外下象限': {clockPos: 7.5, diffRt: 3.5},
            '内上象限': {clockPos: 1.5, diffRt: 3.5},
            '内下象限': {clockPos: 4.5, diffRt: 3.5},
            '上象限': {clockPos: 12, diffRt: 3.5},
            '下象限': {clockPos: 6, diffRt: 3.5},
            '内象限': {clockPos: 3, diffRt: 3.5},
            '外象限': {clockPos: 9, diffRt: 3.5},
            '乳头区': {clockPos: 12, diffRt: 0},
            '中间区': {clockPos: 6, diffRt: -1.6},
          }
        }
        //clockPos点的方向, diffRt距离乳头的距离即半径
        let {pos, clockPos, diffRt, posWay = '时钟定位法', xxPos, xxPosArr, maxSize} = item;

        // 修改1: 移除 mm 到 cm 的转换，因为输入已经是 cm 单位
        // if(Number(diffRt) !== 99 && Number(diffRt) !== 0) {
        //   // mm换成cm
        //   diffRt = diffRt/10;
        // }

        // 修改2: 移除 maxSize 的转换，因为输入已经是 cm 单位
        // maxSize > 0 && (maxSize = Number((maxSize/10).toFixed(2)));

        if(!pos) {
          item.style = [];
          return;
        }
        if(posWay === '时钟定位法' && (!diffRt || Number(diffRt) === 99)) {
          diffRt = !diffRt ? 3.5 : 7;
        }
        let background = this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ?
            this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].color : '#F5F7FA';
        let color = this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ?
            this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].fontColor : '#FFF';

        let dataArr = posWay === '象限定位法' ? xxPosArr : [diffRt];
        if(posWay === '象限定位法' && dataArr.length >= 2) {
          dataArr = this.rebuildDataArr(dataArr, xxObj, pos);
        }
        if(dataArr && dataArr.length) {
          dataArr.forEach(xxItem => {
            if(posWay === '象限定位法') {
              if(pos && xxItem && xxObj[pos] && xxObj[pos][xxItem]) {
                clockPos = xxObj[pos][xxItem]['clockPos'];
                diffRt = xxObj[pos][xxItem]['diffRt'];
              }
            } else {
              diffRt = xxItem;
            }
            if(String(pos) && String(clockPos) && String(diffRt)) {
              let Ox = pos === '左侧' ? 225 : 81;
              let Oy = 64;  //圆心x和y
              let degreesM = 360/(12*60); //每分钟占的度数
              let totalDegree = degreesM * clockPos * 60;  //总的度数，点(时)->分
              let angle = (2*Math.PI / 360) *(totalDegree);
              // let x = Ox + Math.sin(angle) * (diffRt * 7+11);
              // let y = Oy - Math.cos(angle) * (diffRt * 7+11);
              // 判断大小范围，扩大病灶半径确定圆心
              let defaultX = Math.sin(angle) * (diffRt * 7+11);  //默认圆心
              let defaultY = Math.cos(angle) * (diffRt * 7+11);  //默认圆心
              let bzStyle = '';
              if(maxSize >= 5) {  //大号
                if(posWay === '象限定位法' && xxObj[pos][xxItem]['rtFlag']) {
                  diffRt = 2;
                }
                defaultX = Math.sin(angle) * (diffRt * 7 + 11) - 12;
                defaultY = Math.cos(angle) * (diffRt * 7 + 11) + 12;
                bzStyle = 'large';
              } else if(maxSize >=2 && maxSize < 5) {  //中号
                defaultX = Math.sin(angle) * (diffRt * 7 + 14) - 7;
                defaultY = Math.cos(angle) * (diffRt * 7 + 14) + 7;
                bzStyle = 'mediumn'
              }

              let x = Ox + defaultX;
              let y = Oy - defaultY;
              item.style.push({
                background,
                color,
                left: x.toFixed(2) + 'px',
                top: y.toFixed(2) + 'px',
                bzStyle: bzStyle
              })
            }
          })
        }
      },


      // 多象限方法数据重构处理
      rebuildDataArr(dataArr, xxObj, pos) {
        let addBzMap = {
          '外上象限': [],
          '外下象限': [],
          '内上象限': [],
          '内下象限': [],
          '上象限': ['外上象限+内上象限', '外上象限+内上象限+上象限', '外上象限+上象限', '内上象限+上象限'],
          '下象限': ['外下象限+内下象限', '外下象限+内下象限+下象限', '外下象限+下象限', '内下象限+下象限'],
          '内象限': ['内上象限+内下象限', '内上象限+内下象限+内象限', '内上象限+内象限', '内下象限+内象限'],
          '外象限': ['外上象限+外下象限', '外上象限+外下象限+外象限', '外上象限+外象限', '外下象限+外象限'],
          '中间区': [
            '上象限+下象限',
            '内象限+外象限'
          ],
        }
        let dataStr = dataArr.join('+');
        dataStr = dataStr.replace('+乳头区', '');   //先忽略乳头区
        let xxPosArr = [];
        for(let key in addBzMap) {
          let item = addBzMap[key];
          if(item.indexOf(dataStr) > -1 || key === dataStr) {
            if(key === '中间区') {
              if(dataArr.indexOf('乳头区') > -1) {
                xxPosArr = [key];
              }
            } else {
              xxPosArr = [key];
            }
            break;
          }
        }
        if(xxPosArr.length) {
          xxObj[pos][xxPosArr[0]]['rtFlag'] = false;
          // 非中间区的处理乳头区数据
          if(xxPosArr.indexOf('中间区') === -1 && dataArr.indexOf('乳头区') > -1) {
            xxObj[pos][xxPosArr[0]]['diffRt'] = 0;  //靠近乳头区
            xxObj[pos][xxPosArr[0]]['rtFlag'] = true;  //靠近乳头区
          }
        }
        return xxPosArr;
      },

      // 删除病灶
      delTab(e) {
        this.$confirm('确认删除该病灶?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let tabTitle = $(e.target).closest('.el-tabs__item');
          let tabIndex = tabTitle.index();
          let tabContent = tabTitle.parents('#bz-area').find('.el-tabs__content').find('.rt-pane').eq(tabIndex);
          let curAct = tabTitle.attr('aria-controls').replace('pane-', '')
          if(this.activeTab === curAct && this.bzList.length >= 2) {
            let siblingEle = tabTitle.next().length ? tabTitle.next() : tabTitle.prev();
            let actName = siblingEle.attr('aria-controls').replace('pane-', '');
            this.activeTab = actName;
          }
          tabTitle.remove();
          tabContent.remove();
          this.getBzListInfo(true);
          // bzDetailHandler();
          getAllDescAndImp();
        })
      },

      // 添加病灶
      addBzHandler(aiData) {
        var html = $(window.rxbzTemplate).find('.t-content').html();
        var flagId = 'rxscRm-bz-' + createUUidFun();
        html = html.replace(/rxscRm-0003\./g, flagId + '.');
        html = html.replace(/RG-0003\./g, flagId + '.');
        html = html.replace(/data-pre="rxscRm-panel"/g, 'data-pre="'+flagId+'"');
        this.bzPaneList.push({
          key: flagId,
          content: html
        })
        this.getBzListInfo(true);
        this.$nextTick(() => {
          initLigthItemChange(curElem);
          this.activeTab = flagId + '.001';

          // 兼容导出数据用到的判断
          if(rtStructure) {
            // var rtStructure = window.findCurStructure(curElem[0], window.instanceList)
            if(rtStructure.idAndDomMap['']) {
              delete rtStructure.idAndDomMap[''];
            }
            rtStructure.idAndDomMap[flagId + '.001'] = {
              id: flagId + '.001',
              desc: '病灶',
              name: '病灶title',
              pageId: rtStructure.idAndDomMap['rxscRm-0001'].pageId,
              pid: 'rxscRm-0003',
              pvf: '',
              req: '1',
              rtScPageNo: 1,
              value: '病灶' + (this.bzPaneList.length),
              wt: '',
              vt: ''
            }
            var wCon = $(html).find("[rt-sc]");
            wCon.each(function(wIndex, wItem) {
              var node = $(wItem);
              var id = node.attr('id') || '';
              var pid = node.attr('pid') || '';
              var sc = node.attr('rt-sc');
              var scArr = sc.split(';');
              var scObj = {
                id: id,
                pid: pid,
                rtScPageNo: 1,
                value: ''
              };
              scArr.forEach(function(scItem) {
                var key = scItem.split(':')[0];
                var value = scItem.split(':')[1];
                if(key) {
                  if(key === 'itemList') {
                    scObj[key] = eval(decodeURIComponent(value));
                    scObj['groupId'] = id;
                  } else if(key === 'code') {
                    scObj[key] = value;
                    var findCodeNode = $('[id="'+id+'"] .rt-sr-w');
                    findCodeNode.attr('code', value);
                    if(aiData && aiData.length) {
                      for(var k = 0; k < aiData.length; k++) {
                        var aiItem = aiData[k];
                        if(aiItem['code'] === value) {
                          var setValFlag = false;
                          findCodeNode.each(function(j, dom) {
                            if($(dom).hasClass('rt-sr-r') || $(dom).hasClass('rt-sr-ck')) {
                              if(!setValFlag && ($(dom).val() === aiItem['val'] || 
                                (aiValAndSrMap[aiItem['val']] && aiValAndSrMap[aiItem['val']].indexOf($(dom).val()) > -1))) {
                                rtStructure.setFormItemValue(dom, $(dom).val())
                                scObj.value = aiItem['val'];
                                setValFlag = true;
                              }
                            } else {
                              rtStructure.setFormItemValue(dom, aiItem['val'])
                              scObj.value = aiItem['val'];
                            }
                          })
                          break;
                        }
                      }
                    }
                  }else {
                    var numberList = ['left', 'top', 'wt'];
                    scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
                  }
                }
              })
              if(scObj.itemList) {
                scObj.itemList.forEach(function(itemChild) {
                  rtStructure.idAndDomMap[itemChild.id] = {
                    ...scObj,
                    id: itemChild.id,
                    isItemList: true
                  }
                })
              }
              rtStructure.idAndDomMap[id] = {...scObj};
            })
            rtStructure.init(true);
            rtStructure.initChildDisabled(curElem.find('.rt-pane:eq('+(this.bzList.length-1)+')')[0]);
            curElem.find('.rt-pane:eq('+(this.bzList.length-1)+')').find('.level-select').hide();
            toggleHightBlock(curElem)
            document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollLeft = document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollWidth;
          }
        })
      }
		}
	})
}
function initHtmlScript(ele) {
  curElem = $(ele);
  // 来自报告统计
  if(window.isRptStatistics) {
    let cnotent = curElem.find('#bz-tabs .rt-pane:first-child')[0].innerHTML;
    curElem.find('#bz-tabs .rt-pane:first-child').remove();
    curElem.find('#addBzBtn').remove();
    curElem.find('.el-icon-close').remove();
    window.rxbzTemplate = '<el-tab-pane class="rt-pane" name="rxscRm-0003.001">' + cnotent + '</el-tab-pane>';
  }
  initVueComp();
  vueComp.$nextTick(() => {
    if(window.findCurStructure) {
      rtStructure = window.findCurStructure(ele, window.instanceList)
    }
    window.addBzHandler = vueComp.addBzHandler;
    
    // 模版默认的内容赋值
    initTempPage();
  })
}

// 初始化模板默认内容
function initTempPage() {
  // 未编辑过
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);

  // 双侧单纯性扩展导管切换/双侧腺体组织
  // $('.dc-side-btn').click(function() {
  //   if($(this).is(":checked")) {
  //     // 确保左侧最后点击做选中
  //     $(this).closest('.w-block').find(".dc-side:eq(1)").click();
  //     $(this).closest('.w-block').find(".dc-side:eq(0)").click();
  //   }
  // })

  // 勾选导管时判断该侧是否选择哺乳期
  // $('[id="rxscRm-rt-2-dg"] .rt-sr-w').change(function() {
  //   if($(this).is(':checked')) {
  //     var idMap = {
  //       'rxscRm-rt-2-dg-cItem--1': {lastVal: 'rxscRm-rt-2-13-l-rItem--1', curVal: 'rxscRm-rt-2-20-l-rItem--3'},
  //       'rxscRm-rt-2-dg-cItem--2': {lastVal: 'rxscRm-rt-2-13-r-rItem--1', curVal: 'rxscRm-rt-2-20-r-rItem--3'},
  //       'rxscRm-rt-2-dg-cItem--3': {lastVal: 'rxscRm-rt-2-13-db-rItem--1', curVal: 'rxscRm-rt-2-20-db-rItem--3'},
  //     }
  //     var id = $(this).attr('id');
  //     var lastVal = $(`[id="${idMap[id].lastVal}"]`).is(':checked');
  //     var curVal = $(`[id="${idMap[id].curVal}"]`);
  //     if(lastVal) {
  //       curVal.click();
  //     }
  //   }
  // })

  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
  // if(!curElem.attr('data-edited')) {
    // curElem.find('.def-ck').prop('checked', true);
    curElem.find('.def-ck').click();
    curElem.find('.hide-in').hide();
    $('[id="rxscRm-rt-2-7-rItem--1"]').prop('checked', true);
  } else {
    // 兼容旧模板，写过报告，但是不存在该节点，已勾选过病灶的，默认勾选回“报告显示”
    let showBzInRpt = rtStructure.enterOptions.resultData.filter(item => item.id === "rxscRm-0006");
    if(!showBzInRpt.length && $('#rxscRm-rt-1-cItem--3').is(':checked')) {
      curElem.find('#rxscRm-0006-1-cItem--1').prop('checked', true);
    }
  }

  // 模版类型切换
  toggleMbHandler();
  curElem.find('[data-mtab]').change(function() {
    var mtab = $(this).attr('data-mtab');
    if($(this).is(':checked')) {
      // 甲状腺基本情况 和 甲状腺术后互斥
      if(mtab === 'rxscRm-rt-2') {
        $('[data-mtab="rxscRm-rt-3"]').prop('checked', false);
      }
      if(mtab === 'rxscRm-rt-3') {
        $('[data-mtab="rxscRm-rt-2"]').prop('checked', false);
      }
      if(mtab === 'rxscRm-rt-4') {
        // 勾选上显示报告
        curElem.find("#rxscRm-0006-1-cItem--1").prop('checked', true);
        if(!$('.pane-title').length) {
          vueComp.addBzHandler();
        } else {
          vueComp.getBzListInfo();
        }
      }
      if(mtab === 'rxscRm-rt-5') {
        if(!$('[name="RG-0004.100"]').is(':checked')) {
          $('[id="rxscRm-0004.100-rItem--1"]').click();
        } else {
          if($('[id="rxscRm-0004.100-rItem--2"]').is(':checked')) {
            $('[id="rxscRm-0004.101"] .rt-sr-w').removeClass('rt-hide');
          }
        }
      }
    } else {
      if(mtab === 'rxscRm-rt-4') {
        vueComp.getBzListInfo();
        // 取消勾选上显示报告
        curElem.find("#rxscRm-0006-1-cItem--1").prop('checked', false);
      }
    }
    toggleMbHandler(mtab);
  });

  // v1版本的交互
  toggleBlock();
  // // 初始化浅色和深色块的显示关系
  // toggleHightBlock(curElem);
  // initLigthItemChange(curElem);

  initJbqkTab();  // 初始化基本情况
  initRxshTab();  // 初始化乳腺术后情况
  initRxbzTab();  // 初始化乳腺病灶
  initRxlbjTab();  // 初始化乳腺淋巴结
  initJtTab();  // 初始化假体
  // Bi分级
  toggleBIWrap();

  setWriteTypeBlock();   //手写和结构的回显
  setFillOrNot();  //是否已填的标识
  nextBlockStatus();  //下级的默认状态
  $('.setWriteWrap').on('change', '.rt-sr-w', function() {
    setFillOrNot(this);
  })

  // if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
  // }
  getAllDescAndImp();
  
  window.addEventListener('keydown', function(e) {
    if (e.keyCode == "13") {
      $(e.target).trigger("click");
    }
  })
  

  // 模板类型标签切换
  $(".rxmb-hd-i").click(function() {
    var id = $(this).attr('id');
    $(this).addClass('act').siblings().removeClass('act');
    $('.rxmb-detail[data-pid="'+id+'"]').show().siblings('.rxmb-detail[data-pid]').hide();
  })

  // 切换左右侧的子tab
  $(".cl-tab-i").click(function(e) {
    var cltab = $(this).attr('cltab-target');
    var iCheckbox = $(this).find('input[type="checkbox"]');
    if(!$(e.target).hasClass('rt-sr-ck') && !iCheckbox.is(":checked")) {
      iCheckbox.click();
    }
    $(this).addClass('act').siblings().removeClass('act');
    $(this).parents('.cl-tab').find('.cl-tab-con-i[data-cltab]').hide().removeClass('act');
    $(this).parents('.cl-tab').find('.cl-tab-con-i[data-cltab="'+cltab+'"]').addClass('act');
  })
  
  // 手工和结构化填写的切换
  $('.write-type').on('change', '.write-h .rt-sr-w', function() {
    var writeId = $(this).attr('id');
    $(this).closest('.write-type').find('.write-item').removeClass('act');
    $(this).closest('.write-type').find('.write-item .rt-sr-w').addClass('rt-hide')
    $(this).closest('.write-type').find('.write-item[write-id="'+writeId+'"]').addClass('act');
    $(this).closest('.write-type').find('.write-item[write-id="'+writeId+'"] .rt-sr-w').removeClass('rt-hide')
  })

  // 乳腺术后-切除情况的下级框隐藏/假体内容
  $('.lb-row input[type="radio"]').change(function() {
    if($(this).closest('.w-block').length) {
      $(this).closest('.w-block').find('.next-item').hide();
    }
  })

  // 选择男性乳腺时清空其他选项
  $('.boy-rx-side').change(function() {
    if($(this).is(":checked")) {
      $(this).closest('.hight-item').find('[name="RG-rt-2-10-db"]').prop('checked', false);
    }
  })
  $('[name="RG-rt-2-10-db"]').change(function() {
    if($(this).is(":checked")) {
      $(this).closest('.hight-item').find('.cl-tab .boy-rx').prop('checked', false);
      $(this).closest('.hight-item').find('.cl-tab textarea').val('').attr('disabled', true);
    }
  })
  $('.rxscFjrmP').on('change', '.rt-sr-w', function() {
    getAllDescAndImp()
  })
}

// 手写和结构的回显
function setWriteTypeBlock() {
  $('.write-type').each(function(i, ele) {
    var id = $(ele).find('.write-h .rt-sr-w:checked').attr('id');
    $(ele).find('.write-con .write-item').removeClass('act');
    $(ele).find('.write-con .write-item[write-id="'+id+'"]').addClass('act');
    $(ele).find('.write-con .write-item .rt-sr-w').addClass('rt-hide');
    $(ele).find('.write-con .write-item[write-id="'+id+'"] .rt-sr-w').removeClass('rt-hide');
  })
}

// 乳腺勾选哺乳期导管自动勾选上哺乳期
function setDefBrDg(vm, dgBrId, bi) {
  if($(vm).is(':checked')) {
    if(['l', 'r', 'db'].indexOf(dgBrId) > -1) {
      dgBrId = 'rxscRm-rt-2-20-'+dgBrId+'-rItem--1';
    }
    var dgPid = $('[id="'+dgBrId+'"]').attr('parent-id');
    if(!$('[id="'+dgPid+'"]').is(':checked')) {
      $('[id="'+dgPid+'"]').click();
    }
    if(!$('label[for="'+dgPid+'"]').hasClass('on')) {
      $('.hight-item[contect-id="'+dgPid+'"]').show();
      $('.hight-item[contect-id="'+dgPid+'"]').siblings('.hight-item').hide();
      $('label[for="'+dgPid+'"]').addClass('on');
      $('label[for="'+dgPid+'"]').siblings('label').removeClass('on');
    }
    $('[id="'+dgBrId+'"]').click();
  }
  if(bi) {
    setBiNormal();
  }
}

// BI分级的显示隐藏
function toggleBIWrap() {
  $('.rxmb-detail').each(function(i, block) {
    var innerBi = $(block).find('.hide-main-bi:checked');
    if(innerBi.length) {
      $(block).find('.hide-bi-wrap').hide();
      $(block).find('.hide-bi-wrap .rt-sr-w').addClass('rt-hide');
    } else {
      $(block).find('.hide-bi-wrap').show();
      $(block).find('.hide-bi-wrap .rt-sr-w').removeClass('rt-hide');
    }
  })
}

// 判断是否有病灶或导管扩张
function hasBzOrDg(selectors, onlyBz) {
  var bool = false;
  var bzBool = $('[id="rxscRm-rt-1-cItem--3"]').is(":checked");  //是否有病灶
  if(!onlyBz) {
    for(var i = 0; i < selectors.length; i++) {
      var dgBool = getVal(selectors[i]) && getVal(selectors[i]) !== '未见扩张';  //导管是否扩张
      bool = bzBool || dgBool;
      if(dgBool) {
        break;
      }
    }
  }
  return bool;
}

// 初始化基本情况
function initJbqkTab() {
  // 基本情况-选中双侧/左右侧互斥
  $('.sigleSide').change(function() {
    if($(this).is(':checked')) {
      if($(this).closest('[rt-sc]').find('.dbSide').is(':checked')) {
        $(this).closest('[rt-sc]').find('.dbSide').click();
      }
    }
  })
  $('.dbSide').change(function() {
    if($(this).is(':checked')) {
      $(this).closest('[rt-sc]').find('.sigleSide').each(function(i, sigleCk){
        if($(sigleCk).is(':checked')) {
          $(sigleCk).click();
        }
      })
    }
  })

  // Bi分级
  // 勾选导管内的BI分级，则隐藏外层的BI分级
  $('[data-pid="rxscRm-rt-2"] .dgwrap .rt-sr-w').change(function() {
    toggleBIWrap();
  })
}
// 获取基本情况描述内容/诊断内容
function getJbqkContent() {
  if(!$('#rxscRm-rt-1-cItem--1').is(':checked')) {
    return {};
  }
  var block = $('.rxmb-detail[data-pid="rxscRm-rt-2"]');
  var desList = [];
  var impList = [];
  // 导管对应侧选择器
  var sideSelectorMap = {
    '左侧': {key: 'l', dgSelector: '[name="RG-rt-2-20-l"]:not(.rt-hide):checked', dbKey: ['l'], dcKey: ['l-dc'], xtzjIsPrq: false},
    '右侧': {key: 'r', dgSelector: '[name="RG-rt-2-20-r"]:not(.rt-hide):checked', dbKey: ['r'], dcKey: ['r-dc'], xtzjIsPrq: false},
    '双侧': {key: 'db', dgSelector: '[name="RG-rt-2-20-db"]:not(.rt-hide):checked', dbKey: ['db-l', 'db-r'], dcKey: ['db-dc-l','db-dc-r'], xtzjIsPrq: false},
  }
  var xtzjArr = [];
  var sameQk = [];
  var boyDesc = (sideName) => {
    var dbKey = sideSelectorMap[sideName].dbKey;
    dbKey.forEach(ky => {
      var sexVal = getVal(`[name="RG-rt-2-10-${ky}"]:checked`);
      var sexSide = sideName !== '双侧' ? sideName : (ky==='db-l'?'左侧':'右侧');
      if(sexVal) {
        var sexStr = sexSide + sexVal;
        if(sexVal === '乳房区皮下未见明显腺体样结构') {
          desList.push(sexStr);
          xtzjArr.push(sexStr);
          sameQk.push(sexVal);
        } else {
          // 腺体回声长度
          var xtSize = getVal(`[id="rxscRm-rt-2-15-${ky}"] .rt-sr-w`) || '-';
          desList.push(sexStr + `，最大厚度${xtSize}cm，形态规则，边界较清晰，内部回声分布不均匀，未见钙化灶。CDFI：见少许斑点状血流信号`);
          xtzjArr.push(sexSide + '乳腺发育');
          sameQk.push('乳腺发育');
        }
      }
    })
    if(sameQk.length === 2 && sameQk[0] === sameQk[1]) {
      xtzjArr = ['双侧' + sameQk[0]];
    }
  }
  var norXtList = ['正常腺体', '增生乳腺', '绝经期乳腺'];
  block.find('[name="CG-rt-2-9"]:not(.rt-hide):checked').each(function(i, side){
    var sideName = $(side).val();
    var xtEle = `[name="RG-rt-2-10-${sideSelectorMap[sideName].key}"]:checked`;
    var xtVal = getVal(xtEle);
    var xtzzStr = '';
    if(xtVal) {
      var xtId = $(xtEle).attr('id');
      xtzzStr = sideName + xtVal;
      if(rtStructure.idAndDomMap && rtStructure.idAndDomMap[xtId]) {
        // 正常乳腺/增生乳腺/绝经期乳腺
        if(norXtList.includes(rtStructure.idAndDomMap[xtId].desc)) {
          var zsRx = false;
          if(rtStructure.idAndDomMap[xtId].desc === '增生乳腺') {
            xtzjArr.push(sideName + '乳腺增生声像');
            zsRx = true;
          }
          // 无其他病灶、导管扩张
          var dgSelector = sideName === '双侧' ? [
            '[name="RG-rt-2-20-l"]:not(.rt-hide):checked',
            '[name="RG-rt-2-20-r"]:not(.rt-hide):checked',
            '[name="RG-rt-2-20-db"]:not(.rt-hide):checked'
          ] : [sideSelectorMap[sideName].dgSelector];
          if(!hasBzOrDg(dgSelector)) {
            xtzzStr += '，未见明显异常回声';
            if(!zsRx) {
              xtzjArr.push(sideName + '乳腺未见异常');
            }
          }
          desList.push(xtzzStr);
        } else if(rtStructure.idAndDomMap[xtId].desc === '哺乳期乳腺'){
          // 哺乳期乳腺
          desList.push(xtzzStr);
          xtzjArr.push('哺乳期乳腺');
          sideSelectorMap[sideName].xtzjIsPrq = true;
        } else if(rtStructure.idAndDomMap[xtId].desc === '其他腺体组织'){
          // 其他
          var otherXtVal = getVal(`[id="rxscRm-rt-2-18-${sideSelectorMap[sideName].key}"] .rt-sr-w`);
          if(otherXtVal) {
            desList.push(sideName + otherXtVal);
            xtzjArr.push(sideName + otherXtVal);
          }
        } else{
          // 男性等
          boyDesc(sideName);
        }
      }
    } else {
      // 双侧下的男性乳腺
      boyDesc(sideName);
    }
  })
  if(xtzjArr.length) {
    impList.push(xtzjArr.join('，'));
  }
  
  // 乳腺呈 乳腺组织构成呈
  var rxType = block.find('[name="RG-0005.001"]:checked').val();
  if(rxType) {
    desList.push('双侧乳腺组织构成呈' + rxType);
  }

  // 导管
  block.find('[name="CG-rt-2-dg"]:not(.rt-hide):checked').each(function(i, side){
    var dgSide = $(side).val();
    var subfix = sideSelectorMap[dgSide].key;
    var kzType = getVal(`[name="RG-rt-2-20-${subfix}"]:checked`);  //扩张类型
    if(kzType) {
      if(kzType === '未见扩张') {
        desList.push(dgSide + '乳导管未见扩张');
      } else if(kzType === '哺乳期乳导管') {
        var brdg = getVal(`[name="RG-rt-2-28-${subfix}-dc"]:checked`);
        if(brdg) {
          if(brdg === '其他') {
            var qtdgVal = getVal(`[id="rxscRm-rt-2-29-${subfix}-dc"] .rt-sr-w`);
            if(qtdgVal) {
              desList.push(dgSide + qtdgVal);
            } else {
              desList.push(dgSide + '哺乳期乳导管');
            }
          } else {
            desList.push(dgSide + brdg);
          }
        } else {
          desList.push(dgSide + '哺乳期乳导管');
        }
      } else if(kzType === '单纯扩张') {
        // RG-rt-2-21-db-dc-l  RG-rt-2-21-r-dc
        var dcKey = sideSelectorMap[dgSide].dcKey;
        var dcKzBiImp = [];   //单纯扩张且非哺乳期结论
        var biSameArr = [];  //判断等级是否相同
        dcKey.forEach(ky => {
          var dckzList = [];
          var dcSide = dgSide !== '双侧' ? dgSide : (ky==='db-dc-l'?'左侧':'右侧');  //哪一侧
          var kzArea = getVal(`[name="RG-rt-2-21-${ky}"]:checked`);  //扩张范围
          if(kzArea) {
            dckzList.push(kzArea + '扩张');
          }
          // 乳晕区
          var ryList = [];
          var ryArea = getVal(`[id="rxscRm-rt-2-22-${ky}-cItem--1"]:checked`);
          ryArea && ryList.push(ryArea);
          // 点钟方向
          var dzVal = getVal(`[id="rxscRm-rt-2-23-${ky}"] .rt-sr-w`);
          dzVal && ryList.push(dzVal + '点钟方向');
          if(ryList.length) {
            dckzList.push('较宽处（位于'+ryList.join('，')+'）');
          }
          // 内径
          var njVal = getVal(`[id="rxscRm-rt-2-24-${ky}"] .rt-sr-w`);
          njVal && dckzList.push('内径' + njVal + 'cm');
          // 管腔透声
          var gqtsVal = getVal(`[name="RG-rt-2-25-${ky}"]:checked`);
          gqtsVal && dckzList.push('管腔透声' + gqtsVal);

          if(dckzList.length) {
            desList.push(dcSide + '乳导管' + dckzList.join('，'));
          }
          var dgBi = getVal(`[name="RG-rt-2-26-${ky}"]:checked`);
          if(dgBi) {
            var biStr = '乳导管扩张，BI-RADS：'+dgBi+'类';
            dcKzBiImp.push(dcSide + biStr);
            biSameArr.push(dgBi);
          } else {
            dcKzBiImp.push(dcSide + '乳导管扩张');
          }
        })
        // 不是哺乳期腺体则生成结论
        if(!sideSelectorMap[dgSide].xtzjIsPrq) {
          if(biSameArr.length === 2 && biSameArr[0] === biSameArr[1]) {
            impList.push('双侧乳导管扩张，BI-RADS：'+biSameArr[0]+'类');
          } else {
            if(dcKzBiImp.length) {
              if(dcKzBiImp.length === 2 && dcKzBiImp.every(item => !item.includes('BI-RADS'))) {
                impList.push('双侧乳导管扩张');
              } else {
                impList.push(dcKzBiImp.join('；'));
              }
            } else {
              impList.push(dgSide + '乳导管扩张');
            }
          }
        } else {
          // 哺乳期乳腺+导管单纯扩张，无病灶
          var bzBool = $('[id="rxscRm-rt-1-cItem--3"]').is(":checked");  //是否有病灶
          if(!bzBool) {
            impList.push(`BI-RADS：1类`);
          }
        }
      } else {
        var kzBi = getVal(`[name="RG-rt-2-30-${subfix}-dc"]:checked`);
        impList.push(dgSide + '乳导管' + kzType + (kzBi ? `，BI-RADS：${kzBi}类` : ''))
      }
    }
  })

  // 腺体分类
  // 哺乳期乳腺+导管单纯扩张，无病灶
  var xtBi = getVal(`[name="RG-2-7"]:not(.rt-hide):checked`);
  if(xtBi && !$('[id="rxscRm-rt-1-cItem--3"]').is(":checked")) {
    if(impList.length) {
      impList[impList.length - 1] = impList[impList.length - 1] + `，BI-RADS：${xtBi}类`;
    } else {
      impList.push(`，BI-RADS：${xtBi}类`);
    }
  }

  var imp = impList.length ? impList.join('。\n') + '。' : '';
  imp = imp.replace('左侧乳腺增生声像，右侧乳腺增生声像', '双侧乳腺增生声像');
  imp = imp.replace('哺乳期乳腺，哺乳期乳腺', '哺乳期乳腺');
  return {
    desc: desList.length ? desList.join('；') + '。' : '',
    imp: imp,
  }
}

// 初始化乳腺术后情况
function initRxshTab() {
  // 勾选导管内的BI分级，则隐藏外层的BI分级
  $('[data-pid="rxscRm-rt-3"] .dgwrap .rt-sr-w').change(function() {
    toggleBIWrap();
  })
}

// 术后选择哺乳期默认勾选上哺乳导管
function checkBrDg(vm) {
  if($(vm).is(":checked")) {
    $('[id="rxscRm-rt-3-0-28-rItem--3"]').click();
  }
}

// 术后剩余乳腺的情况
function getOtherSideContent(rxSide) {
  var sideName = rxSide === '左侧' ? '右侧' : '左侧';   //另一侧
  var block = $('.rxmb-detail[data-pid="rxscRm-rt-3"] .other-rx');
  var otherSideDesc = [];
  var otherSideImpList = [];
  var oneSideImp = [];
  // 导管对应侧选择器
  var xtzjIsPrq = false;  //是否为哺乳
  var norXtList = ['正常腺体', '增生乳腺', '绝经期乳腺'];
  // 腺体组织
  var xtEle = `[name="RG-rt-2-10"]:checked`;
  var xtVal = getVal(xtEle);
  var xtzzStr = '';
  if(xtVal) {
    var xtId = $(xtEle).attr('id');
    xtzzStr = sideName + xtVal;
    if(rtStructure.idAndDomMap && rtStructure.idAndDomMap[xtId]) {
      // 正常乳腺/增生乳腺/绝经期乳腺
      if(norXtList.includes(rtStructure.idAndDomMap[xtId].desc)) {
        var zsRx = false;
        if(rtStructure.idAndDomMap[xtId].desc === '增生乳腺') {
          oneSideImp.push(sideName + '乳腺增生声像');
          zsRx = true;
        }
        // 无其他病灶、导管扩张
        if(!hasBzOrDg(['[name="RG-rt-3-0-28"]:checked'])) {
          xtzzStr += '，未见明显异常回声';
          if(!zsRx) {
            oneSideImp.push(sideName + '乳腺未见异常');
          }
        }
        otherSideDesc.push(xtzzStr);
      } else if(rtStructure.idAndDomMap[xtId].desc === '哺乳期乳腺'){
        // 哺乳期乳腺
        otherSideDesc.push(xtzzStr);
        oneSideImp.push('哺乳期乳腺');
        xtzjIsPrq = true;
      } else if(rtStructure.idAndDomMap[xtId].desc === '其他腺体组织'){
        // 其他
        var otherXtVal = getVal(`[id="rxscRm-rt-3-0-18"] .rt-sr-w`);
        if(otherXtVal) {
          otherSideDesc.push(sideName + otherXtVal);
        }
      } else{
        var sexStr = sideName + xtVal;
        if(xtVal === '乳房区皮下未见明显腺体样结构') {
          otherSideDesc.push(sexStr);
          oneSideImp.push(sexStr);
        } else {
          // 腺体回声长度
          var xtSize = getVal(`[id="rxscRm-rt-3-0-15"] .rt-sr-w`) || '-';
          otherSideDesc.push(sexStr + `，最大厚度${xtSize}cm，形态规则，边界较清晰，内部回声分布不均匀，未见钙化灶。CDFI：见少许斑点状血流信号`);
          oneSideImp.push(sideName + '乳腺发育');
        }
      }
    }
    if(oneSideImp.length) {
      otherSideImpList.push(oneSideImp.join('，'));
    }
  } 
  
  // 乳腺呈 乳腺组织构成呈
  var rxType = block.find('[name="RG-rt-3-0-20"]:checked').val();
  if(rxType) {
    otherSideDesc.push('乳腺组织构成呈' + rxType);
  }

  // 导管
  var kzType = getVal(`[name="RG-rt-3-0-28"]:checked`);  //扩张类型
  if(kzType) {
    if(kzType === '未见扩张') {
      otherSideDesc.push(sideName + '乳导管未见扩张');
    } else if(kzType === '哺乳期乳导管') {
      var brdg = getVal(`[name="RG-rt-3-0-36-dc"]:checked`);
      if(brdg) {
        if(brdg === '其他') {
          var qtdgVal = getVal(`[id="rxscRm-rt-3-0-37-dc"] .rt-sr-w`);
          if(qtdgVal) {
            otherSideDesc.push(sideName + qtdgVal);
          } else {
            otherSideDesc.push(sideName + '哺乳期乳导管');
          }
        } else {
          otherSideDesc.push(sideName + brdg);
        }
      } else {
        otherSideDesc.push(sideName + '哺乳期乳导管');
      }
    } else if(kzType === '单纯扩张') {
      var dckzList = [];
      var kzArea = getVal(`[name="RG-rt-3-0-29-dc"]:checked`);  //扩张范围
      if(kzArea) {
        dckzList.push(kzArea + '扩张');
      }
      // 乳晕区
      var ryList = [];
      var ryArea = getVal(`[id="rxscRm-rt-3-0-30-dc-cItem--1"]:checked`);
      ryArea && ryList.push(ryArea);
      // 点钟方向
      var dzVal = getVal(`[id="rxscRm-rt-3-0-31-dc"] .rt-sr-w`);
      dzVal && ryList.push(dzVal + '点钟方向');
      if(ryList.length) {
        dckzList.push('较宽处（位于'+ryList.join('，')+'）');
      }
      // 内径
      var njVal = getVal(`[id="rxscRm-rt-3-0-32-dc"] .rt-sr-w`);
      njVal && dckzList.push('内径' + njVal + 'cm');
      // 管腔透声
      var gqtsVal = getVal(`[name="RG-rt-3-0-33-dc"]:checked`);
      gqtsVal && dckzList.push('管腔透声' + gqtsVal);

      if(dckzList.length) {
        otherSideDesc.push(sideName + '乳导管' + dckzList.join('，'));
      }
      // 不是哺乳期腺体则生成结论
      if(!xtzjIsPrq) {
        var biVal = getVal('[name="RG-rt-3-0-34-dc"]:checked') || '';
        otherSideImpList.push(sideName + '乳导管扩张' + (biVal?`，BI-RADS：${biVal}类`:''));
      } else {
        // 哺乳期乳腺+导管单纯扩张，无病灶
        var bzBool = $('[id="rxscRm-rt-1-cItem--3"]').is(":checked");  //是否有病灶
        if(!bzBool) {
          otherSideImpList.push(`BI-RADS：1类`);
        }
      }
    } else {
      var kzBi = getVal(`[name="RG-rt-3-0-38-dc"]:checked`);
      otherSideImpList.push(sideName + '乳导管' + kzType + (kzBi ? `，BI-RADS：${kzBi}类` : ''))
    }
  }
  // 腺体分类
  // 哺乳期乳腺+导管单纯扩张，无病灶
  var xtBi = getVal(`[name="RG-rt-3-0-22"]:not(.rt-hide):checked`);
  if(xtBi && !$('[id="rxscRm-rt-1-cItem--3"]').is(":checked")) {
    if(!otherSideImpList.length) {
      otherSideImpList.push(sideName + '乳腺未见异常')
    }
    if(otherSideImpList.length) {
      otherSideImpList[otherSideImpList.length - 1] = otherSideImpList[otherSideImpList.length - 1] + `，BI-RADS：${xtBi}类`;
    }
  }
  return {
    otherDesc: otherSideDesc.length ? otherSideDesc.join('；') : '',
    otherImp: otherSideImpList.length ? otherSideImpList.join('。\n') : '',
  }
}
// 获取乳腺术后描述内容/诊断内容
function getRxshContent() {
  shqcType = {   //术后类型，影响假体的结论推导
    '左侧': '',
    '右侧': '',
    '双侧': '',
  }
  if(!$('#rxscRm-rt-1-cItem--2').is(':checked')) {
    return {};
  }
  var block = $('.rxmb-detail[data-pid="rxscRm-rt-3"]');
  var desList = [];
  var impList = [];
  var allConDesc = [];
  var allConImp = [];
  var rxSide = block.find('[name="RG-rxscRm-rt-3-5"]:checked').val();
  if(rxSide) {
    if(rxSide !== '双侧') {
      block.find('#rxscRm-rt-3-0 .rt-sr-w').text(rxSide === '左侧' ? '右侧乳腺' : '左侧乳腺');
      block.find('.other-rx').show();
      block.find('.other-rx .rt-sr-w').each(function(r, rEle) {
        if(!$(rEle).closest('.hide-bi-wrap').length) {
          $(rEle).removeClass('rt-hide');
        }
      })
    } else {
      block.find('.other-rx').hide();
      block.find('.other-rx .rt-sr-w').addClass('rt-hide');
    }
    var subfix = sideMapKy[rxSide];
    // 全切 切除类型
    var qcType = block.find(`[name="RG-rt-3-6-${subfix}"]:checked`).val();
    if(qcType) {
      shqcType[rxSide] = qcType;
      if(qcType === '全切/全切+假体植入/全切+皮下扩张器植入') {
        var allCutType = block.find(`[name="RG-rt-3-7-${subfix}"]:checked`).val() || '';
        var xbStatus = block.find(`[name="RG-rt-3-8-${subfix}"]:checked`).val() || '';   //胸壁情况
        if(allCutType) {
          shqcType[rxSide] = allCutType;
          allCutType = allCutType.replace('全切', '切除');
          var str = `${rxSide}乳腺${allCutType}术后，${rxSide}乳腺缺如`;
          var impStr = `${rxSide}乳腺${allCutType}术后`;
          if(xbStatus) {
            str += `，${rxSide}胸壁${xbStatus}`;
            if(xbStatus === '未见明显异常') {
              impStr += `，${rxSide}胸壁${xbStatus}`;
            }
          }
          desList.push(str);
          impList.push(impStr);
        }
      } else {
        var cutType = block.find(`[name="RG-rt-3-9-${subfix}"]:checked`).val() || '';
        if(cutType) {
          var str = `${rxSide}乳腺${cutType}术后`;
          // 残余腺体情况
          var cyxtStatus = block.find(`[name="RG-rt-3-11-${subfix}"]:checked`).val();
          if(cyxtStatus) {
            var id = block.find(`[name="RG-rt-3-11-${subfix}"]:checked`).attr('id');
            var impIds = {
              'rxscRm-rt-3-11-l-rItem--1': '正常',
              'rxscRm-rt-3-11-r-rItem--1': '正常',
              'rxscRm-rt-3-11-db-rItem--1': '正常',
              'rxscRm-rt-3-12-l-rItem--1': '增生',
              'rxscRm-rt-3-12-r-rItem--1': '增生',
              'rxscRm-rt-3-12-db-rItem--1': '增生',
              'rxscRm-rt-3-13-l-rItem--1': '绝经期',
              'rxscRm-rt-3-13-r-rItem--1': '绝经期',
              'rxscRm-rt-3-13-db-rItem--1': '绝经期',
              'rxscRm-rt-3-14-l-rItem--1': '哺乳期',
              'rxscRm-rt-3-14-r-rItem--1': '哺乳期',
              'rxscRm-rt-3-14-db-rItem--1': '哺乳期',
            }
            if(impIds[id]) {
              impList.push(str + '，残余腺体' + impIds[id]);
            }
            if(cyxtStatus === '其他') {
              cyxtStatus = block.find(`[id="rxscRm-rt-3-19-${subfix}"] .rt-sr-w`).val() || '';
            }
            if(cyxtStatus) {
              str += `，残余腺体${cyxtStatus}`;
            }
          }
          desList.push(str);
        }
      }
    }

    desList.length && allConDesc.push(desList.join('；'));
    impList.length && allConImp.push(impList.join('；'));

    if(rxSide !== '双侧') {
      var otherRx = getOtherSideContent(rxSide);
      otherRx.otherDesc && allConDesc.push(otherRx.otherDesc);
      otherRx.otherImp && allConImp.push(otherRx.otherImp);
    }
  }

  return {
    desc: allConDesc.length ? allConDesc.join('。\n') + '。' : '',
    imp: allConImp.length ? allConImp.join('。\n') + '。' : '',
  }
}
// 初始化乳腺病灶
function initRxbzTab() {
  // 初始化病灶
  if($('[id="rxscRm-rt-1-cItem--3"]').is(':checked')) {
    initBzTemplate();
  }
  // 病灶位置控制
  // toggleBzSide();
}
// 初始化乳腺淋巴结
function initRxlbjTab() {
  // 淋巴结的高亮
  newLightBlockHandler();
  showLbDataWrap();

  // 初始化淋巴结-副乳腺的显示
  switchLbFrxWrap();
  $('.frxR').change(function() {
    var pid = $(this).parents('.w-con').attr('id');
    var value = $(this).val();
    if(value === '有') {
      $('.frx-wrap[id="'+pid+'"]').show();
    } else {
      $('.frx-wrap[id="'+pid+'"]').hide();
    }
  })
  curElem.find(".lbj-wrap").on('change', '.rt-sr-w', function() {
    switchLbFrxWrap();
  })
}
// 获取淋巴结描述内容/诊断内容
function getLbjContent() {
  var desc = getLbDescHtml('0004', '.lbj-wrap')
  var imp = getLbImpHtml('0004');
  return {
    desc: desc,
    imp: imp
  }
}

// 下级的默认显示隐藏
function nextBlockStatus() {
  $('.next-item').each(function(i, dom) {
    var id = $(dom).attr('id');
    if($('[id="'+id+'"]').is(':checked')) {
      $(dom).show();
    }
  })
}
// 初始化假体
function initJtTab() {
  // 假体情况内容切换
  $('.next-radio').change(function() {
    var id = $(this).attr('id');
    $(this).closest('.w-con').siblings('.next-wrap').find('.next-item').hide();
    if($(this).is(":checked")) {
      $(this).closest('.w-con').siblings('.next-wrap').find('.next-item[id="'+id+'"]').show();
      $(this).closest('.w-con').siblings('.next-wrap').find('.next-item[id="'+id+'"]').find('.def-ck').click();
    }
  })
}
// 获取假体描述内容/诊断内容
function getJtContent() {
  if(!$('#rxscRm-rt-1-cItem--5').is(':checked')) {
    return {};
  }
  var block = $('.rxmb-detail[data-pid="rxscRm-rt-6"]');
  var desList = [];
  var impList = [];
  var lastDesc = '', lastImp = '';  //判断两侧是否相同
  block.find('[name="CG-rt-6-2"]:not(.rt-hide):checked').each(function(i, side){
    var oneJt = [];
    var oneJtImp = [];
    var jtSide = $(side).val();
    var subfix = sideMapKy[jtSide];
    if(jtSide) {
      var jtType = block.find(`[name="RG-rt-6-3-${subfix}"]:checked`).val() || '';   //假体方式
      var jtPos = block.find(`[name="RG-rt-6-4-${subfix}"]:checked`).val() || '';   //假体位置
      var jtStatus = block.find(`[name="RG-rt-6-5-${subfix}"]:checked`).val() || '';   //假体情况
      if(['全切+假体植入', '全切+皮下扩张器植入'].indexOf(shqcType[jtSide]) === -1) {
        oneJtImp.push('乳房假体囊'+jtType+'术后')
      }
      if(jtType) {
        oneJt.push('乳房假体囊'+jtType+'术后');
      }
      if(jtPos) {
        oneJt.push(jtPos+'见假体囊');
      }
      if(jtStatus) {
        if(jtStatus === '假体正常') {
          // 正常
          var zcVal = block.find(`[name="RG-rt-6-5-1-${subfix}"]:checked`).val();
          if(zcVal) {
            if(zcVal !== '其他') {
              oneJt.push(zcVal);
            } else {
              var otherZcVal = block.find(`[id="rxscRm-rt-6-5-2-${subfix}"] .rt-sr-w`).val();
              if(otherZcVal) {
                oneJt.push(otherZcVal);
              }
            }
          }
          oneJtImp.push('假体囊未见明显异常');
        } else if(jtStatus === '假体包膜挛缩') {
          // 假体包膜挛缩
          var bmlsVal = block.find(`[name="RG-rt-6-5-3-${subfix}"]:checked`).val();
          if(bmlsVal) {
            if(bmlsVal !== '其他') {
              oneJt.push(bmlsVal);
            } else {
              var otherBbmlsVal = block.find(`[id="rxscRm-rt-6-5-4-${subfix}"] .rt-sr-w`).val();
              if(otherBbmlsVal) {
                oneJt.push(otherBbmlsVal);
              }
            }
          }
          oneJtImp.push('假体包膜挛缩');
        } else if(jtStatus === '假体渗漏') {
          //  假体渗漏
          var jtsl = [];
          var slValType = block.find(`[name="RG-rt-6-5-5-${subfix}"]:checked`).val();
          if(slValType === '手工填写') {
            var slByHandVal = block.find(`[id="rxscRm-rt-6-5-6-${subfix}"] .rt-sr-w`).val();
            if(slByHandVal) {
              jtsl.push(slByHandVal);
            }
          } else {
            // 渗漏位置
            var slPosArr = [];
            for(var j = 1; j <= 6; j++) {
              var val = block.find(`[id="rxscRm-rt-6-5-7-${subfix}-cItem--${j}"]:checked`).val();
              val && slPosArr.push(val);
            }
            var otherSlPos = block.find(`[id="rxscRm-rt-6-5-8-${subfix}"] .rt-sr-w`).val();
            otherSlPos && slPosArr.push(otherSlPos);
            if(slPosArr.length) {
              jtsl.push(slPosArr.join('、'));
            }
            jtsl.push('假体囊壁厚度变薄，轮廓改变');
            // 假体囊壁
            var jtNbVal = block.find(`[name="RG-rt-6-5-9-${subfix}"]:checked`).val();
            if(jtNbVal) {
              jtsl.push('假体囊壁' + jtNbVal);
            }
            // 无回声区大小
            var hsqSizeArr = [];
            for(var j = 10; j <= 11; j++) {
              var val = block.find(`[id="rxscRm-rt-6-5-${j}-${subfix}"] .rt-sr-w`).val();
              val && hsqSizeArr.push(val + 'cm');
            }
            if(hsqSizeArr.length) {
              jtsl.push('囊壁外出现不规则无回声区，范围' + hsqSizeArr.join('x'));
            }
            // 假体边界
            var jtBjVal = block.find(`[name="RG-rt-6-5-12-${subfix}"]:checked`).val();
            if(jtBjVal) {
              jtsl.push('边界' + jtBjVal);
            }
    
            jtsl.push('胸肌纹理清楚，较薄');
          }
          if(jtsl.length) {
            oneJt.push(jtSide + '乳房' + jtsl.join('，'));
          }
          oneJtImp.push('假体渗漏');
        } else if(jtStatus === '假体散在分布') {
          //  假体散在分布
          var jtfs = [];
          var fsValType = block.find(`[name="RG-rt-6-5-13-${subfix}"]:checked`).val();
          if(fsValType === '手工填写') {
            var fsByHandVal = block.find(`[id="rxscRm-rt-6-5-19-${subfix}"] .rt-sr-w`).val();
            if(fsByHandVal) {
              jtfs.push(fsByHandVal);
            }
          } else {
            var jtxt = block.find(`[id="rxscRm-rt-6-5-15-${subfix}-cItem--1"]:checked`).val();
            if(jtxt) {
              jtfs.push(jtxt);
            }
            // 散在分布
            var fsPosArr = [];
            for(var j = 1; j <= 5; j++) {
              var val = block.find(`[id="rxscRm-rt-6-5-14-${subfix}-cItem--${j}"]:checked`).val();
              val && fsPosArr.push(val);
            }
            if(fsPosArr.length) {
              jtfs.push(fsPosArr.join('、') + '可见团块状低回声');
              oneJtImp.push(fsPosArr.join('、') + '内散在假体');
            }
            // 较大大小
            var sizeArr = [];
            for(var j = 16; j <= 18; j++) {
              if(j===17) {
                continue;
              }
              var val = block.find(`[id="rxscRm-rt-6-5-${j}-${subfix}"] .rt-sr-w`).val();
              val && sizeArr.push(val + 'cm');
            }
            if(sizeArr.length) {
              jtfs.push('较大' + sizeArr.join('x'));
            }
            if(fsPosArr.length) {
              jtfs.push('肿块形态不规则');
            }
    
            // 是否与假体相同
            var isXtVal = block.find(`[name="RG-rt-6-5-17-${subfix}"]:checked`).val();
            if(isXtVal) {
              jtfs.push(`${isXtVal === '否' ? '不' : '直接或间接' }与${jtPos}假体相通`);
            }
          }
          if(jtfs.length) {
            oneJt.push(jtfs.join('，'));
          }
        }
      }
    }
    if(oneJtImp.length) {
      if(lastImp && lastImp === oneJtImp.join('，')) {
        impList = ['双侧' + lastImp];
      } else {
        lastImp = oneJtImp.join('，');
        impList.push(jtSide + lastImp);
      }
    }
    if(oneJt.length) {
      if(lastDesc) {
        lastDesc = lastDesc.replace(/右侧|左侧/ig, '双侧');
        var tempJt = oneJt.join('，');
        tempJt = tempJt.replace(/右侧|左侧/ig, '双侧');
        if(lastDesc === tempJt) {
          desList = ['双侧' + lastDesc];
        } else {
          desList.push(jtSide + oneJt.join('，'));
        }
      } else {
        lastDesc = oneJt.join('，');
        desList.push(jtSide + lastDesc);
      }
    }
  })
  

  return {
    desc: desList.length ? desList.join('；') + '。' : '',
    imp: impList.length ? impList.join('，') + '。' : '',
  }
}

// 模版类型显示/隐藏
function toggleMbHandler(tab) {
  var actTab = '';
  var curActIsHide = false;
  curElem.find('[data-mtab]').each(function(i, dom) {
    var mtab = $(dom).attr('data-mtab');
    if($(dom).is(':checked')) {
      if(!actTab) {
        actTab = mtab;
      }
      $('.rxmb-hd-i[id="'+mtab+'"]').removeClass('rt-hide').show();
      if(tab === mtab) {
        $('.rxmb-hd-i[id="'+mtab+'"]').addClass('act').siblings('.rxmb-hd-i').removeClass('act');
        $('.rxmb-detail[data-pid="'+mtab+'"]').show().siblings('.rxmb-detail').hide();
      }
      if(tab) {
        actTab = mtab;
        // $('.rxmb-detail[data-pid="'+mtab+'"]').find('.def-ck').click();
      }
      $('.rxmb-detail[data-pid="'+mtab+'"]').find('.rt-sr-w').each(function(i, rtW){
        if($(rtW).parents('[data-name]').length === 0 
          && $(rtW).parents('.no-toggle').length === 0 
          && $(rtW).parents('.other-rx').length === 0 
          && $(rtW).parents('.write-item').length === 0) {
          $(rtW).removeClass('rt-hide'); 
        }
      })
    } else {
      if(tab === mtab && $('.rxmb-hd-i.act').attr('id') === tab) {
        curActIsHide = true;
      }
      $('.rxmb-hd-i[id="'+mtab+'"]').addClass('rt-hide').hide();
      $('.rxmb-hd-i[id="'+mtab+'"]').removeClass('act')
      $('.rxmb-detail[data-pid="'+mtab+'"]').hide();
      $('.rxmb-detail[data-pid="'+mtab+'"]').find('.rt-sr-w').addClass('rt-hide');  //带rt-hide的节点不回保存数据
    }
  })
  if((!tab && actTab) || (tab && curActIsHide)) {
    $('.rxmb-hd-i[id="'+actTab+'"]').removeClass('rt-hide').show();
    $('.rxmb-hd-i[id="'+actTab+'"]').addClass('act').siblings('.rxmb-hd-i').removeClass('act');
    $('.rxmb-detail[data-pid="'+actTab+'"]').show().siblings('.rxmb-detail').hide();
  }
  if(curElem.find('[data-mtab]:checked').length === 1) {
    curElem.find('[data-mtab]').attr('disabled', false)
    curElem.find('[data-mtab]:checked').attr('disabled', true)
  } else {
    curElem.find('[data-mtab]').attr('disabled', false)
  }
}

// 淋巴结-副乳腺的切换
function switchLbFrxWrap() {
  $('.frx-wrap').hide();
  $('.frxR:checked').each(function(i, rd) {
    var value = $(rd).val();
    var pid = $(rd).parents('.w-con').attr('id');
    if(value === '有') {
      $('.frx-wrap[id="'+pid+'"]').show();
    } else {
      $('.frx-wrap[id="'+pid+'"]').hide();
    }
  })
}

// 病灶的描述和诊断内容
function bzDetailHandler() {
  over2Level = false;
  var isCheckBz = curElem.find('[id="rxscRm-rt-1-cItem--3"]:checked').val();
  var isView = curElem.find(".t-pg").attr('isView');
  var description = [];
  var impression = '';
  if(isCheckBz) {
    var allBz = {};
    var pane = curElem.find('.rt-pane');
    var over2LevelList = ['3', '4A', '4B', '4C', '5', '6'];
    pane.each(function(i, dom) {
      var prefix = $(dom).attr('id').split('.')[0].replace('pane-', '');
      var bzItem = getBzDetail($(dom), prefix);
      var html = '';
      var side = bzItem.pos;
      // 勾选了哪侧及等级才进行描述
      if(side && bzItem.levelText) {
        var moreType = bzItem.propText || '';  //性质
        var level = bzItem.levelText || '';
        var type = bzItem.bzCount;  //单多发
        if(!over2Level && over2LevelList.includes(level)) {
          over2Level = true;
        }
        var xzText = $(dom).find('.selBz').val();
        var obj = {
          template: xzText,  //模板
          moreType: moreType, //性质
          type: type, //单多发
          level: level, //等级
          // side: side === '左侧' && lSide > 0 ? 'left' : (side === '右侧' && rSide > 0 ? 'right' : side),
          side: side,
          impText: html,
          sort: i + 1,
          doubleSide: '',
          ...bzItem
        };
        description.push(obj);  //用于描述
        if(allBz[bzItem.levelText]) {
          var sameLevelIndex = allBz[bzItem.levelText].findIndex(item => !item.sameFlag && item.moreType === moreType && item.side === side);
          if(sameLevelIndex > -1) {
            obj.sameFlag = true;  //表已存在相同级别同性质的病灶，后续不需要单独描述
            allBz[bzItem.levelText][sameLevelIndex].moreFlag = true;  //描述为多发
          }
          allBz[bzItem.levelText].push(obj);
        } else {
          allBz[bzItem.levelText] = [obj];
        }
      }
    })
    var impTextArr = [];
    if(JSON.stringify(allBz) !== '{}') {
      var order = ['6', '5', '4C', '4B', '4A', '3', '2', '0'];
      var yTxt = '';  //余字
      var has4Level = false;
      for(var i = 0; i < order.length; i++) {
        var key = order[i];
        if(allBz[key]) {
          for(var j = 0; j < allBz[key].length; j++) {
            var item = allBz[key][j];
            if(item.sameFlag || item.diffSideFlag) {
              continue;
            }
            if(['0', '2', '3'].indexOf(item.levelText) === -1) {
              var html = item.side + '乳腺';
              if(item.type || item.moreType) {
                var xxPos = item.posWay === '象限定位法' ? (item.xxPos || '') : (item.clockPos ? item.clockPos + '点' : '');
                html += (!item.moreFlag ? xxPos : '') +(item.moreFlag || item.type === '多发' ? '多发' : '')+item.moreType;
              }
              html += '结节，';
              html += 'BI-RADS：'+item.level+'类';
              impTextArr.push(html);
              has4Level = true;
            }else {
              var html = '';
              if(has4Level && !yTxt) {
                yTxt = '余';
                html += yTxt;
              }
              // 不同侧同级
              var diffLevelIndex = allBz[key].findIndex(all => !all.sameFlag && !all.diffSideFlag && all.moreType === item.moreType && all.side !== item.side);
              if(diffLevelIndex > -1) {
                var diffSideItem = allBz[key][diffLevelIndex];
                allBz[key][diffLevelIndex]['diffSideFlag'] = true;
                item.diffSide = diffSideItem.side;
                item.hasDiffBz = true;   //存在不同侧同级的数据
                if(diffSideItem.type === '多发' || diffSideItem.moreFlag) {
                  item.diffCount = '多发';
                }
              }
              var diffSideText = '';
              html += (item.hasDiffBz ? '双侧' : item.side) + '乳腺';
              if(item.diffCount || item.type || item.moreType) {
                var bzTypeCount = item.moreFlag || item.type === '多发' ? '多发' : '';  //单多发
                var curItemCount = bzTypeCount;
                if(item.hasDiffBz) {
                  bzTypeCount = '';
                  if(item.diffCount === curItemCount) {
                    bzTypeCount = item.diffCount || '';
                  } else {
                    if(item.diffCount === '多发') {
                      diffSideText = '('+item.diffSide + '乳腺' + item.diffCount+')';
                    } 
                    
                    if(curItemCount === '多发') {
                      diffSideText = '('+item.side + '乳腺' + curItemCount+')';
                    }
                  }
                }
                html += bzTypeCount+item.moreType;
              }
              html += '结节'+diffSideText+'，';
              html += 'BI-RADS：'+item.level+'类';
              impTextArr.push(html);

            }
          }
        }
      }
    }
    if(impTextArr.length) {
      impression += impTextArr.join('。\n') + '。'
    }
    //结论 --end
  }
  // 淋巴情况
  // getLbDescHtml(isNormal === '可见异常' ? '0004' : '0010', '.lbj-wrap')
  // getLbDescHtml('0004', '.lbj-wrap')
  // impression = impression.replace(/<ty><\/ty>/ig, '');
  // if(rtStructure) {
  //   rtStructure.impression = impression;
  // }
  
  if(rtStructure) {
    rtStructure.description = description;
  }
  vueComp.bzDescription = description;
  return {imp: impression};
}
// 获取淋巴结结论
function getLbImpHtml(lbKey) {
  if(!$('[id="rxscRm-rt-1-cItem--4"]:checked').val()) {
    return '';
  }
  var writeType = $('[name="RG-0004.100"]:checked').val();
  if(writeType === '手工填写') {
    return '';
  }
  var lbMap = {
    '0004': '0100',
    '0010': '0101',
  }
  var arr = [];
  var lbAllStatus = {
    bcwList: [],   //转移不除外
    knxdList: [],  //可能性大
    otherList: [], //其他情况
    ywfrxList: []
  }
  for(var i = 1; i < 5; i++) {
    if($('[id="rxscRm-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').is(":checked")) {
      var lbName = $('[id="rxscRm-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').val();
      for(var j = 1; j < 3; j++) {
        var onlyCheckFr = false;  //勾选了副乳
        var str = '';
        var lbSide = j==1?'左侧':'右侧';
        if(getVal(`[name="RG-${lbKey}.00${i}.0${j}.30"]:checked`) === '有') {
          onlyCheckFr = true;
          if(lbAllStatus.ywfrxList.length === 1) {
            lbAllStatus.ywfrxList = ['双侧腋窝'];
          } else {
            lbAllStatus.ywfrxList.push(lbSide + '腋窝');
          }
        }
        if($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'-cItem--1"]').is(":checked")) {
          var lbVal = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.01"]:checked').val() || '';
          var key = 'otherList';
          if(lbVal && lbVal !== '其他') {
            key = lbVal === '不除外' ? 'bcwList' : 'knxdList';
          }
          if(!onlyCheckFr || lbVal) {
            var sameIndex = lbAllStatus[key].findIndex(item => item.indexOf(lbName) > -1);
            if(sameIndex > -1) {
              lbAllStatus[key].splice(sameIndex, 1, '双侧' + lbName);
            } else {
              lbAllStatus[key].push(lbSide + lbName);
            }
          }
        }
      }
    }
  }
  var arrKeys = [
    {key: 'bcwList', desc: '淋巴结转移不除外'},
    {key: 'knxdList', desc: '淋巴结转移可能性大'},
    {key: 'ywfrxList', desc: '副乳腺可能'},
    {key: 'otherList', desc: '可见异常淋巴结'},
  ];
  for(var item of arrKeys) {
    var key = item.key;
    var desc = item.desc;
    if(lbAllStatus[key].length) {
      arr.push(lbAllStatus[key].join('、') + desc);
    }
  }
  return arr.length ? arr.join('；') + '。' : '';
}
// 获取淋巴结的描述内容
function getLbDescHtml(lbKey, domClass) {
  if(!$('[id="rxscRm-rt-1-cItem--4"]:checked').val()) {
    return '';
  }
  var writeType = $('[name="RG-0004.100"]:checked').val();
  if(writeType === '手工填写') {
    var str = $('[id="rxscRm-0004.101"] .rt-sr-w').val();
    return str ? '淋巴结：' + str : '';
  }
  var lbMap = {
    '0004': '0100',
    '0010': '0101',
  }
  var arr = [];   
  var otherAttrKeys = ['04', '05', '09', '06', '07'];
  var otherAttrObj = {};
  var otherAttrArr = [];
  var allLbStatus = {
    isNormal: [],   //未见异常的淋巴结
    isUnNormal: [],   //可见异常的淋巴结，但是没有具体勾选其他内容的
  }
  var frDesc = [];
  for(var i = 1; i < 5; i++) {
    if($('[id="rxscRm-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').is(":checked")) {
      var isNormal = $('[name="RG-'+lbKey+'.00'+i+'"]:checked').val() || '';  //是否异常
      var lbName = $('[id="rxscRm-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').val();
      if(isNormal === '未见异常') {
        // arr.push('双侧' + lbName + '淋巴结未见异常。');
        allLbStatus.isNormal.push('双侧' + lbName);
      } else {
        var sideChecked = false;
        var otherLbStatus = [];
        for(var j = 1; j < 3; j++) {
          var side = j==1?'左侧':'右侧';
          if($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'-cItem--1"]').is(":checked")) {
            sideChecked = true;
            var lb = side + lbName;
            
            var checkFrFlag = false;  //是否勾选副乳
            // 副乳name="RG-0004.001.02.31" name="RG-0004.001.01.31"
            var frType = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.31"]:checked').val();
            if(frType) {
              if(frType === '腋窝皮下脂肪梭形增厚，内见腺体样稍强回') {
                var l = getVal(`[id="rxscRm-${lbKey}.00${i}.0${j}.33"] .rt-sr-w`) || '-';
                var w = getVal(`[id="rxscRm-${lbKey}.00${i}.0${j}.34"] .rt-sr-w`) || '-';
                var h = getVal(`[id="rxscRm-${lbKey}.00${i}.0${j}.35"] .rt-sr-w`) || '-';
                var dxOrHd = getVal(`[name="RG-${lbKey}.00${i}.0${j}.32"]:checked`);
                if(dxOrHd === '厚度') {
                  frType += '厚度约' + h + 'cm';
                } else {
                  frType += `大小约${l}cm×${w}cm`;
                }
                frType += '，形态规则，边界清晰。';
              } else if(frType === '腋窝可见类乳腺实质的片状高回声区') {
                var h2 = getVal(`[id="rxscRm-${lbKey}.00${i}.0${j}.36"] .rt-sr-w`) || '-';
                frType += `，厚度约${h2}cm，其内未见明显异常声像。`;
              } else if(frType === '其他') {
                var ofr = getVal(`[id="rxscRm-${lbKey}.00${i}.0${j}.37"] .rt-sr-w`) || '';
                frType = ofr;
              }
              if(frType) {
                var frSide = lb.replace('腋窝', '');
                frDesc.push(frSide + frType);
              }
            }

            var strArr = [];
            var lbVal = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.01"]:checked').val() || '';  //转移
            var count = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.10"]:checked').val() || '';  //单发/多发
            if(!checkFrFlag || count) {
              strArr.push(`可见${count==='多发'?count:''}淋巴结`);
            }
            // if(!lbVal || lbVal === '其他') {
            // } else {
            //   strArr.push(`可见${count==='多发'?count:''}淋巴结转移${lbVal}`);
            // }
            var side = [];  //大小
            if($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'.02"] .rt-sr-w').val()) {
              side.push($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'.02"] .rt-sr-w').val() + 'cm');
            }
            if($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'.03"] .rt-sr-w').val()) {
              side.push($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'.03"] .rt-sr-w').val() + 'cm');
            }
            if($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'.08"] .rt-sr-w').val()) {
              side.push($('[id="rxscRm-'+lbKey+'.00'+i+'.0'+j+'.08"] .rt-sr-w').val() + 'cm');
            }
            if(side.length) {
              strArr.push(`${count==='多发'?'大者':'大小'}${side.join(' × ')}`);
            }
            var str = strArr.join('，');
            var otherTar = [];
            for(var k = 0; k < otherAttrKeys.length; k++) {
              var tar = otherAttrKeys[k];
              var value = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.'+tar+'"]:checked').val() || '';
              if(!value || (value === '无' && tar === '05')) {
                continue;
              }
              if(['09', '06'].indexOf(tar) > -1) {
                otherTar.push(`${tar=='09'?'边缘':'淋巴门'}` + value);
              } else {
                otherTar.push(value);
              }
            }
            if(otherTar.length) {
              var tarStr = otherTar.join('，');
              if(otherAttrArr.indexOf(tarStr) === -1) {
                otherAttrArr.push(tarStr)
              }
              if(str) {
                if (otherAttrObj[tarStr]) {
                  var sameIndex = otherAttrObj[tarStr].findIndex((othItem) => othItem.includes(lbName));
                  if (sameIndex > -1) {
                    var temp = otherAttrObj[tarStr][sameIndex];
                    temp = temp.replace(/右侧|左侧/gi, "双侧");
                    otherAttrObj[tarStr].splice(sameIndex, 1, temp);
                  } else {
                    otherAttrObj[tarStr].push(lb + str);
                  }
                } else {
                  otherAttrObj[tarStr] = [lb + str];
                }
              }
            } else {
              str && arr.push(lb + str + '。');
            }
            
          } else {
            otherLbStatus.push(side + lbName);
          }
        }
        if(!sideChecked) {
          // arr.push(lbName + '淋巴结可见异常。');
          allLbStatus.isUnNormal.push('双侧' + lbName);
        } else {
          if(otherLbStatus.length) {
            allLbStatus.isNormal = [...allLbStatus.isNormal, ...otherLbStatus];
          }
        }
      }
    }
  }
  for(var key in otherAttrObj) {
    if(otherAttrObj[key].length > 1) {
      arr.push(otherAttrObj[key].join('，') + `，上述结节` + key + '。');
    } else {
      arr.push(otherAttrObj[key] + '，' + key + '。')
    }
  }
  if(frDesc.length) {
    arr.push(frDesc.join(''));
  }
  // 可见异常淋巴的集合
  if(allLbStatus.isUnNormal.length) {
    arr.push(allLbStatus.isUnNormal.join('、') + '可见异常淋巴结。');
  }
  // 未见异常淋巴的集合
  if(allLbStatus.isNormal.length) {
    arr.push(allLbStatus.isNormal.join('、') + '淋巴结未见异常。');
  }
  var lbStr = arr.join('');
  return lbStr;
}
// 获取所有病灶的字段
function getBzDetail(dom, prefix) {
  var byTypes = [];
  for(var i = 1; i < 5; i++) {
    if(dom.find(`[id="${prefix}.001.06.01.01-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      byTypes.push(dom.find(`[id="${prefix}.001.06.01.01-cItem--${i}"]:not(.rt-hide)`).attr('value'));
    }
  }
  var obj = {
    propText: dom.find(`[name="${prefix}.001.13.01"]:checked:not(.rt-hide)`).val() || '',  //性质
    pos: dom.find(`[name="${prefix}.001.02.01"]:checked:not(.rt-hide)`).val(),
    bzCount: dom.find(`[name="${prefix}.001.15.01"]:checked:not(.rt-hide)`).val(),  //多发/单发
    levelText: dom.find(`[name="${prefix}.001.14.01"]:checked:not(.rt-hide)`).val() || dom.find(`[id="${prefix}.001.14.02"] .rt-sr-w:not(.rt-hide)`).val() || '',  //等级
    clockPos: dom.find(`[id="${prefix}.001.03.02"] .rt-sr-w:not(.rt-hide)`).val() || '',  //位于几点方向
    diffRt: dom.find(`[id="${prefix}.001.03.03"] .rt-sr-w:not(.rt-hide)`).val() || '',  //距乳头距离
    posOtherDesc: dom.find(`[id="${prefix}.001.03.07"] .rt-sr-w:not(.rt-hide)`).val() || '',  //位于-其他情况
    bzSide: '',
    // bzSide: dom.find(`[id="${prefix}.001.03.04"] .rt-sr-w:not(.rt-hide)`).val() + 'mm x ' + dom.find(`[id="${prefix}.001.03.05"] .rt-sr-w`).val() + 'mm x ' + dom.find(`[id="${prefix}.001.03.06"] .rt-sr-w`).val() + 'mm',  //病灶大小
    shape: dom.find(`[name="${prefix}.001.04.01"]:checked:not(.rt-hide)`).val() || '',  //形状
    fx: dom.find(`[name="${prefix}.001.05.01"]:checked:not(.rt-hide)`).val() || '',  //方向
    by: dom.find(`[name="${prefix}.001.06.01"]:checked:not(.rt-hide)`).val() || '',  //边缘
    byType: byTypes.length ? byTypes.join('、') : '',  //边缘情况
    hsType: dom.find(`[name="${prefix}.001.07.01"]:checked:not(.rt-hide)`).val() || '',  //回声类型
    hsBjVal: dom.find(`[name="${prefix}.001.07.01.01"]:checked:not(.rt-hide)`).val() || dom.find(`[name="${prefix}.001.07.01.03"]:checked:not(.rt-hide)`).val() || '',  //回声类型-边界
    hsTsVal: dom.find(`[name="${prefix}.001.07.01.02"]:checked:not(.rt-hide)`).val() || '',  //回声类型-透声
    hfhs: dom.find(`[name="${prefix}.001.08.01"]:checked:not(.rt-hide)`).val() || '',  //后方回声
    isGh: dom.find(`[name="${prefix}.001.09.01"]:checked:not(.rt-hide)`).val() || '',  //是否钙化
    ghCount: dom.find(`[name="${prefix}.001.09.01.01"]:checked:not(.rt-hide)`).val() || '',  //钙化数量
    isXl: dom.find(`[name="${prefix}.001.10.01"]:checked:not(.rt-hide)`).val() || '',  //是否有血流
    xlCount: dom.find(`[name="${prefix}.001.10.01.01"]:checked:not(.rt-hide)`).val() || '',  //血流量
    // specialText: dom.find(`[id="${prefix}.001.12.01"] .rt-sr-w:not(.rt-hide)`).val() || '',  //特殊情况
    otherDesc: dom.find(`[id="${prefix}.001.16.01"] .rt-sr-w:not(.rt-hide)`).val() || '',  //其他征象
    posWay: dom.find(`[name="${prefix}.001.03.00"]:checked:not(.rt-hide)`).val(),  //定位法
    // xxPos: dom.find(`[name="${prefix}.001.03.10"]:checked:not(.rt-hide)`).val(),  //象限位置,改多选
    xxPos: getVal(dom.find(`[name="${prefix}.001.03.10"]:checked:not(.rt-hide)`)),  //象限位置文本,改多选
  }
  obj.xxPosArr = obj.xxPos.split('、') || []; //象限位置数组,改多选
  // 大小
  let sizeTxt = [];
  let sizeNum = [];
  var decimLen = 0;
  var startIdx = obj.posWay === '象限定位法' ? 11 : 4;
  var endIdx = obj.posWay === '象限定位法' ? 14 : 7;
  for(var bIdx = startIdx; bIdx < endIdx; bIdx++) {
    var bI = bIdx < 9 ? `0${bIdx}` : bIdx;
    var sizeVal = dom.find(`[id="${prefix}.001.03.${bI}"] .rt-sr-w:not(.rt-hide)`).val();
    if(sizeVal) {
      var [num, decim = ''] = sizeVal.split('.');
      if(decim && decimLen < decim.length) {
        decimLen = decim.length;
      }
    }
  }
  for(var bIdx = startIdx; bIdx < endIdx; bIdx++) {
    var bI = bIdx < 9 ? `0${bIdx}` : bIdx;
    var sizeVal = dom.find(`[id="${prefix}.001.03.${bI}"] .rt-sr-w:not(.rt-hide)`).val();
    if(sizeVal) {
      var value = sizeVal;
      if(decimLen > 0) {
        var [num, decim = ''] = sizeVal.split('.');
        var len = decimLen - decim.length;
        if(len > 0) {
          var fillText = new Array(len).fill('0').join('');
          value = num + '.' + decim + fillText;
        }
      }
      sizeNum.push(Number(value));
      sizeTxt.push(`${value}cm`);
    }
  }
  if(sizeTxt.length) {
    obj.bzSide = sizeTxt.join(' × ');
    obj.maxSize = Math.max.apply(null, sizeNum);
  }
  // 相关特征
  var relateDescList = [];
  if(dom.find(`[name="${prefix}.001.11.01"]:checked:not(.rt-hide)`).val() === '有') {
    relateDescList.push('结构扭曲');
  }
  if(dom.find(`[name="${prefix}.001.11.02"]:checked:not(.rt-hide)`).val() === '有') {
    if(dom.find(`[id="${prefix}.001.11.02.01"] .rt-sr-w:not(.rt-hide)`).val()) {
      relateDescList.push('导管改变(' + dom.find(`[id="${prefix}.001.11.02.01"] .rt-sr-w`).val() + ')');
    } else {
      relateDescList.push('导管改变');
    }
  }
  if(dom.find(`[name="${prefix}.001.11.03"]:checked:not(.rt-hide)`).val() === '有') {
    if(dom.find(`[id="${prefix}.001.11.03.01-cItem--1"]`).is(":checked") || dom.find(`[id="${prefix}.001.11.03.01-cItem--2"]`).is(":checked")) {
      var pf = [];
      if(dom.find(`[id="${prefix}.001.11.03.01-cItem--1"]`).is(":checked")) {
        pf.push('皮肤增厚');
      }
      if(dom.find(`[id="${prefix}.001.11.03.01-cItem--2"]`).is(":checked")) {
        pf.push('皮肤回缩');
      }
      relateDescList.push(pf.join('、'))
    } else {
      relateDescList.push('皮肤改变');
    }
  }
  if(dom.find(`[name="${prefix}.001.11.04"]:checked:not(.rt-hide)`).val() === '有') {
    relateDescList.push('水肿');
  }
  if(dom.find(`[name="${prefix}.001.11.05"]:checked:not(.rt-hide)`).val() || dom.find(`[id="${prefix}.001.11.06"] .rt-sr-w:not(.rt-hide)`).val()) {
    var str = '应变弹性评估：';
    var arr = [];
    if(dom.find(`[name="${prefix}.001.11.05"]:checked`).val()) {
      arr.push(dom.find(`[name="${prefix}.001.11.05"]:checked`).val() + '分');
    }
    if(dom.find(`[id="${prefix}.001.11.06"] .rt-sr-w`).val()) {
      arr.push('应变比：' + dom.find(`[id="${prefix}.001.11.06"] .rt-sr-w`).val());
    }
    relateDescList.push(str + arr.join('，'));
  }
  if(dom.find(`[name="${prefix}.001.11.07"]:checked:not(.rt-hide)`).val()) {
    relateDescList.push('剪切波弹性成像：' + dom.find(`[name="${prefix}.001.11.07"]:checked`).val());
  }
  obj.relateText = relateDescList.join('；');  //相关特征

  // 钙化位置
  var ghPos = [];
  for(var i = 1; i < 4; i++) {
    if(dom.find(`[id="${prefix}.001.09.01.02-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      ghPos.push(dom.find(`[id="${prefix}.001.09.01.02-cItem--${i}"]`).attr('value'));
    }
  }
  obj.ghPos = ghPos.join('、');

  // 钙化形态
  var ghType = [];
  for(var i = 1; i < 5; i++) {
    if(dom.find(`[id="${prefix}.001.09.01.03-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      ghType.push(dom.find(`[id="${prefix}.001.09.01.03-cItem--${i}"]`).attr('value'));
    }
  }
  obj.ghType = ghType.join('、');

  // 血流位置
  var xlPos = [];
  for(var i = 1; i < 4; i++) {
    if(dom.find(`[id="${prefix}.001.10.01.02-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      xlPos.push(dom.find(`[id="${prefix}.001.10.01.02-cItem--${i}"]`).attr('value'));
    }
  }
  if(dom.find(`[id="${prefix}.001.10.01.03"] .rt-sr-w`).text()) {
    xlPos.push(dom.find(`[id="${prefix}.001.10.01.03"] .rt-sr-w`).text())
  }
  obj.xlPos = xlPos.join('、');
  return obj;
}

// 相关内容的显示/隐藏
function toggleBlock(fromEdit) {
  var csPerform = $('[name="RG-0001.001"]:checked').val();  //超声表现
  if(!csPerform) {
    $('[id="rxscRm-0001.001-rItem--2"]').attr('checked', true);
  }
  // if(csPerform === '未见异常') {
  // } else {
  //   $(".abnormalArea").show();
  //   // $(".abnormalArea").find('.rt-sr-w').removeClass('rt-hide');
  // }
  if(curElem.find('.t-pg').attr('hasEdited')) {
    vueComp.getBzListInfo();
  }
  // if(fromEdit === true) {
  //   bzDetailHandler();
  // }
}

// 初始化病灶模版的内容
function initBzTemplate() {
  curElem.find('.rt-pane .selBz').each(function(i, el) {
    bzTempChange(el, true)
  })
  bzDetailHandler();
}

// 切换模板
function bzTempChange(node, isFirst) {
  var value = $(node).val();
  var type = $(node).find('option[value="'+value+'"]').attr('data-type');  //simple complex RADS3  vicious
  var noShowTemp = $(node).closest('.rt-pane').find('[data-temp]');  //不显示的模块
  var parentNode = $(node).closest(".t-content");
  noShowTemp.each(function(i, temp) {
    $(temp).show();
    $(temp).find('.rt-sr-w').removeClass('rt-hide');
    var tempStr = $(temp).attr('data-temp');
    if(tempStr.indexOf(type) > -1 || (!type && !tempStr)) {
      $(temp).hide();
      $(temp).find('.rt-sr-w').addClass('rt-hide');
    }
  })
  // 手动切换则赋相关表单对应值
  if(!isFirst) {
    parentNode.find(".level-select .rt-sr-w").val('');
    if(['vicious'].includes(type)) {
      parentNode.find('[name$=".001.04.01"][value="不规则形"]').prop("checked", true);
    } else {
      parentNode.find('[name$=".001.04.01"][value="椭圆形"]').prop("checked", true);
    }
    if(['vicious'].includes(type)) {
      parentNode.find('[name$=".001.05.01"][value="垂直位"]').prop("checked", true);
      parentNode.find('[name$=".001.06.01"][value="不光整"]').prop("checked", true);
      parentNode.find('[parent-id$=".001.06.01-rItem--2"]').removeAttr('disabled');
    } else {
      parentNode.find('[name$=".001.05.01"][value="水平位"]').prop("checked", true);
      parentNode.find('[name$=".001.06.01"][value="光整"]').prop("checked", true);
      parentNode.find('[parent-id$=".001.06.01-rItem--2"]').attr('disabled', true).prop("checked", false);
    }
    // 回声类型
    if(['vicious', 'RADS3'].includes(type)) {
      parentNode.find('[name$=".001.07.01"][value="低回声"]').click();
    } else if(['complex'].includes(type)) {
      parentNode.find('[name$=".001.07.01"][value="混合回声"]').click();
    } else {
      parentNode.find('[name$=".001.07.01"][value="无回声"]').click();
    }
    // 后方回声
    if(['vicious'].includes(type)) {
      parentNode.find('[name$=".001.08.01"][value="衰减"]').prop("checked", true);
    } else if(['RADS3'].includes(type)) {
      parentNode.find('[name$=".001.08.01"][value="无改变"]').prop("checked", true);
    } else {
      parentNode.find('[name$=".001.08.01"][value="增强"]').prop("checked", true);
    }
    // 性质
    if(['complex'].includes(type)) {
      parentNode.find('[name$=".001.13.01"][value="囊实性"]').prop("checked", true);
      parentNode.find('[name$=".001.13.01.02"]').removeAttr('disabled');
      parentNode.find('[name$=".001.13.01.01"]').attr('disabled', true).prop("checked", false);
    } else if(['simple'].includes(type)) {
      parentNode.find('[name$=".001.13.01"][value="囊性"]').prop("checked", true);
      parentNode.find('[name$=".001.13.01.01"]').removeAttr('disabled');
      parentNode.find('[name$=".001.13.01.02"]').attr('disabled', true).prop("checked", false);
    } else {
      parentNode.find('[name$=".001.13.01"][value="实性"]').prop("checked", true);
      parentNode.find('[name$=".001.13.01.02"]').attr('disabled', true).prop("checked", false);
      parentNode.find('[name$=".001.13.01.01"]').attr('disabled', true).prop("checked", false);
    }
    // BI-RADS分类
    if(['complex', 'RADS3'].includes(type)) {
      parentNode.find('[name$=".001.14.01"][value="3"]').prop("checked", true);
    } else if(['simple'].includes(type)) {
      parentNode.find('[name$=".001.14.01"][value="2"]').prop("checked", true);
    } else {
      parentNode.find('[name$=".001.14.01"]').prop("checked", false);
    }
    // 有无钙化和血流
    parentNode.find('[id$=".001.09.01.03-cItem--1"]').prop('checked', false);
    if(['complex', 'vicious'].includes(type)) {
      parentNode.find('[id$=".001.09.01-rItem--2"]').click();
      parentNode.find('[id$=".001.10.01-rItem--2"]').click();
      if(['vicious'].includes(type)) {
        parentNode.find('[id$=".001.09.01.03-cItem--1"]').prop('checked', true);
      }
    } else if(['RADS3'].includes(type)) {
      parentNode.find('[id$=".001.09.01-rItem--1"]').click();
      parentNode.find('[id$=".001.10.01-rItem--1"]').click();
    }
    
    // 相关特征
    if(['vicious'].includes(type)) {
      parentNode.find('.relat-box [type="radio"][value="有"]').click();
      parentNode.find('.relat-box [id$=".001.11.03.01-cItem--2"]').prop("checked", true);
    } else {
      parentNode.find('.relat-box [type="radio"][value="无"]').click();
    }
  }
}

// 调整病灶情况
function updateBzInfo(node, type) {
  if(type === "otherType" || type === "levelType") {
    var noShowTemp = $(node).closest('.rt-pane')
    if(type === "otherType") {
      var selectVal = noShowTemp.find(".level-select .rt-sr-w").val();
      if(selectVal) {
        noShowTemp.find('.level-radio [type="radio"]').prop("checked", false);
      } else {
        var type = noShowTemp.find('.selBz option:selected').attr('data-type');
        if(['complex', 'RADS3'].includes(type)) {
          noShowTemp.find('[name$=".001.14.01"][value="3"]').prop("checked", true);
        } else if(['simple'].includes(type)) {
          noShowTemp.find('[name$=".001.14.01"][value="2"]').prop("checked", true);
        } else {
          noShowTemp.find('[name$=".001.14.01"]').prop("checked", false);
        }
      }
    } else if(type === "levelType") {
      noShowTemp.find(".level-select .rt-sr-w").val('');
    }
  } 
  vueComp.$nextTick(() => {
    vueComp.getBzListInfo(true);
  })
}

function changeXlVal(vm, radioName) {
  if($(vm).is(":checked")) {
    // $('[name="'+radioName+'"][value="可见血流"]').prop("checked", true);
    if(['RG-0004.001.01.07', 'RG-0004.001.02.07'].indexOf(radioName) > -1) {
      var frRadio = radioName === 'RG-0004.001.01.07' ? 'RG-0004.001.01.30' : 'RG-0004.001.02.30';
      $('[name="'+frRadio+'"][value="无"]').prop("checked", true);
    }
  }
  // else {
  //   if(['RG-0004.001.01.07', 'RG-0004.001.02.07'].indexOf(radioName) > -1) {
  //     $('.frx-wrap').hide();
  //   }
  // }
}

// 特殊处理距离乳头的距离
function inpJLRTNumber(vm) {
  var numReg = /^(\d+)\.?(\d*)$/ig;
  var inpVal = $(vm).val().trim();
  if(inpVal === '') {
    return;
  }
  if(inpVal[inpVal.length - 1] === '.') {
    $(vm).val(inpVal.replace('.', ''));
  }
  var value = Number($(vm).val().trim());
  var min = $(vm).attr('min');
  var max = $(vm).attr('max');
  if(!numReg.test(value)) {
    $(vm).val(min || '');
  } else {
    if(value !== 99) {
      if(max !== undefined && value > Number(max)) {
        $(vm).val(max);
      }
      if(min !== undefined && value < Number(min)) {
        $(vm).val(min);
      }
    }
  }
}
var isFillInStatus = {};
function newLightBlockHandler() {
  $('.lb-new input').click(function(e) {
    var id = $(this).attr('id');
    if(isFillInStatus[id]) {
      $(this).prop('checked', false);
    }
    if(!$(this).is(':checked')) {
      delete isFillInStatus[id];
      $(this).parents().find('[contect-id="'+id+'"]').find('.hight-block').hide();
    }
  })
  $('.lb-new').click(function(e) {
    var target = $(this).find('input');
    var id = target.attr('id');
    if(isFillInStatus[id]) {
      e.preventDefault();
    }
    $(this).addClass('lb-new-act');
    $(this).siblings().removeClass('lb-new-act');
    var pid = target.attr("parent-id");
    var wblock = target.parents('.w-block');
    var hightBlock = wblock.find('.new-block[light-id="'+pid+'"]');
    var hightItem = wblock.find('.new-item[contect-id="'+id+'"]');
    hightBlock.show();
    if(!isFillInStatus[id] && target.is(':checked')) {
      $(hightItem).find('.def-ck').click();
    }
    if(target.is(':checked')) {
      isFillInStatus[id] = '1';
    }
    $(this).parents(".lbj-wrap").find('.new-item').hide();
    hightItem.show();
  })
}

// 回显淋巴结的数据
function showLbDataWrap() {
  var lbjWrap = $(".lbj-wrap");
  var hightCk = lbjWrap.find('.lb-new .rt-sr-w');
  var hightCked = lbjWrap.find('.lb-new .rt-sr-w:checked');
  if(!hightCked || hightCked.length===0) {
    // $(".ywck").click();
    // var id = $(".ywck").attr('id');
    // isFillInStatus[id] = '1';
  } else {
    hightCked.each(function(i, dom) {
      if($(dom).is(":checked")) {
        var id = $(dom).attr('id');
        isFillInStatus[id] = '1';
        if(i === 0) {
          $(dom).parents('.lb-new').click();
        }
      }
    })
  }
}

// 获取所有诊断和描述
function getAllDescAndImp() {
  // 只取诊断，因为诊断在这里处理，描述在rxscPage.vue处理
  var impressionList = [];
  // 基本情况
  var jbpkImp = contentHandler('jbqk');
  jbpkImp && impressionList.push(jbpkImp);

  // 乳腺术后
  var rxshImp = contentHandler('rxsh');
  rxshImp && impressionList.push(rxshImp);

  // 病灶
  var rxbzImp = contentHandler('rxbz');
  rxbzImp && impressionList.push(rxbzImp);

  // 淋巴
  var lbImp = contentHandler('lb');
  lbImp && impressionList.push(lbImp);
  
  // 假体
  var jtImp = contentHandler('jt');
  jtImp && impressionList.push(jtImp);

  // 出现2类以上的诊断，超声诊断最后需要加⼀句：建议结合其他检查
  // if(over2Level) {
  //   impressionList.push('建议结合其他检查。');
  // }
  if(rtStructure) {
    if(!firstIn || !rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
      rtStructure.impression = impressionList.join('\n') ||  '';
    } else {
      firstIn = false;
      var result = rtStructure.enterOptions.impression.replace(/\n\n(.*)/, "");
      rtStructure.impression = result || '';
    }
  }
}

function contentHandler(flag) {
  var funList = {
    'jbqk': getJbqkContent,
    'rxsh': getRxshContent,
    'rxbz': bzDetailHandler,
    'lb': getLbjContent,
    'jt': getJtContent,
  }
  var con = funList[flag]();
  var desc = con.desc || '';
  var imp = con.imp || '';
  if($(`.${flag}-desc`).length) {
    $(`.${flag}-desc`).html(desc);
  }
  if($(`.${flag}-imp`).length) {
    $(`.${flag}-imp`).html(imp);
  }
  return imp;
}

// 基本情况默认正常都勾上bi=1
function setBiNormal() {
  var noBz = !$('[id="rxscRm-rt-1-cItem--3"]').is(':checked');  //无病灶
  var normalXt = $('[id="rxscRm-rt-2-10-db-rItem--1"]').is(':checked');  //双侧正常腺体
  var noDg = $('[id="rxscRm-rt-2-20-db-rItem--1"]').is(':checked');  //双侧无导管
  if(noBz && normalXt && noDg) {
    $('[id="rxscRm-rt-2-7-rItem--1"]').prop('checked', true);
  }
}

// 判断是否已填的标识
function setFillOrNot(vm) {
  var ctab = $('[data-cltab]');
  if(vm) {
    ctab = $(vm).closest('[data-cltab]');
  }
  if(!ctab.length && vm) {
    var tabI = $(vm).closest('.cl-tab-i');
    if(tabI.length) {
      var tabITarget = tabI.attr('cltab-target');
      ctab = $('[data-cltab="'+tabITarget+'"]');
    }else {
      $(vm).closest('.setWriteWrap').find('.un-write').show();
      $(vm).closest('.setWriteWrap').find('.has-write').hide();
      // 单独点击男性双侧乳腺的左右复选框时，选中的情况下点击取消选中下级
      $(vm).closest('.setWriteWrap').find('.cl-tab-i input[type="checkbox"]').each(function(i, ck) {
        if($(ck).is(':checked')) {
          $(ck).click();
        }
      })
      return;
    }
  }
  ctab.each(function(i, tab) {
    var bool = false;
    var childArr = $(tab).find('.rt-sr-w');
    for(var i = 0; i < childArr.length; i++) {
      var ele = childArr[i];
      var id = $(ele).closest('[data-cltab]').attr('data-cltab');
      if($(ele).hasClass('rt-sr-t')) {
        if($(ele).val()) {
          bool = true;
        }
      } else {
        bool = $(ele).is(':checked');
      }
      if(bool) {
        $('[cltab-target="'+id+'"] .un-write').hide();
        $('[cltab-target="'+id+'"] .has-write').show();
        if(!vm) {
          $('[cltab-target="'+id+'"]').addClass('act');
          $('[cltab-target="'+id+'"]').siblings('[cltab-target]').removeClass('act');
          $('[data-cltab="'+id+'"]').addClass('act');
          $('[data-cltab="'+id+'"]').siblings('[data-cltab]').removeClass('act');
        }
        break;
      }
      $('[cltab-target="'+id+'"] .un-write').show();
      $('[cltab-target="'+id+'"] .has-write').hide();
    }
  })
}