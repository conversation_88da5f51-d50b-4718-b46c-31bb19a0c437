<template>
  <div class="jzxEditReport main-page">
    <div class="dis-rp-wrap fjrmJzx-wrap">
      <!-- 编辑页 -->
      <defaultjzxPage 
        key="defaultjzxPage"
        :resultFormData="resultFormData"
        :patientInfo="patientInfo"
        :getDataFinished="getDataFinished"
        :docId="docId"
        :jzxHtmlUrl="jzxHtmlUrl"
        :resImpress="resImpress"
        @htmlLoaded="defaultPageGetData"
        domId="defaultjzxPage"
        ref="fjrmJzxPage"
      >
      </defaultjzxPage>
    </div>
  </div>
</template>

<script>
import '@/assets/css/diseaseReport.scss'
import XaJzxPage from './xaJzxPage.vue';
import diseaseReportMixin from '@/mixins/newDiseaseReport.js'
import '../../../public/template-lib/utils/structFun2.0.js'
export default {
  name: "xaJzxEditReport",
  components: {
    defaultjzxPage: XaJzxPage,
  },
  mixins: [diseaseReportMixin],
  data() {
    return {
      patientInfo: {
        sex: ''
      },
      resultFormData: [],
      getDataFinished: false,
      docId: '',   //文档id
      jzxHtmlUrl: process.env.BASE_URL + 'template-lib/views/srTemplate/xaJzxTemplate/xaJzxTemplate.html',
      resImpress: ''
    }
  },
  watch: {
    $route() {
      window.location.reload();
    },
  },
  computed: {
  },
  mounted() {
    this.pageInit();
  },
  methods: {
    async pageInit() {
      await this.getPatientInfo();
    },

    async defaultPageGetData(html) {
      this.resultFormData = await this.getData();
      let docContent = this.allContent.docContent || {};
      this.resImpress = docContent.impression || '';
      let params = { examNo: this.patientInfo.examNo, fileType: 'RptImg' };
      // console.log(JSON.stringify(this.resultFormData));
      console.log('this.isFromYunPacs',this.isFromYunPacs);
      await this.getReportDoc();
      this.$refs.fjrmJzxPage.getEntryPeopleList();
      await this.$store.dispatch('report/getReportImageList', { params, vm: this });
      setTimeout(async () => {
        this.getDataFinished = true;
      }, 200)
    },

  }
}
</script>

<style lang="scss"></style>