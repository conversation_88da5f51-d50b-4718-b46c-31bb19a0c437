<template>
  <div class="fjrmJzx-form" v-loading="!getDataFinished">
    <disease-layout :localQueryParams="localQueryParams" 
      :patientInfo="patientInfo" 
      :halfWrap="showHistoyByHalf" 
      :readonly="isHistory" 
      :pageTitle="pageTitle"
      footTitle="超声提示"
      :showOther="rptImageList.length>0"
      otherDesTitle="图像"
    >
      <template slot="inner-header-right">
        <div class="lr-user">
          <span>录入员：</span>
          <el-select 
            :popper-append-to-body="false"
            size="mini" 
            filterable
            collapse-tags
            placeholder="请选择"
            :disabled="trace"
            v-model="entryPeopleUserId"
            @change="entryChangeHandler"
          >
            <el-option label="请选择" value=""></el-option>
            <el-option 
              v-for="(item, i) in entryPeopleList"
              :key="i"
              :label="item.name" 
              :value="item.userId"
            ></el-option>
          </el-select>
        </div>
      </template>
      <div slot="header-right">
        <slot name="h-right-btn"></slot>
      </div>
      <div slot="body" style="height: 100%">
        <div :id="domId" style="height: 100%">
          <!-- 主html -->
          <v-html-panel 
            :htmlUrl.sync="jzxHtmlUrl" 
            @loaded="htmlLoadedHandler"
          ></v-html-panel>
        </div>
      </div>
      <div slot="other-descript">
        <div class="jzx-img-box" id="rxscImgBox">
          <div class="img-item" v-for="(img, i) in rptImageList">
            <img :src="img.src" alt="">
            <div class="del-wrap" @click="deleteImg(img)">
              <i class="el-icon-delete" style="margin-right: 5px;"></i>移除
            </div>
          </div>
        </div>
      </div>
      <div slot="footer-descript">
        <div class="descript-content">
          <el-input type="textarea" v-model="selfImpression" :rows="5" resize="none"></el-input>
        </div>
      </div>
      <div slot="footer-btn" v-if="!isFromYunPacs && !sourceType">
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue"
          icon="el-icon-position"
          :disabled="submitFlag"
          v-if="!isFromYunPacs || ypacsRptInfo.examStatus < 60 || ypacsRptInfo.examStatus == 65"
          @click="submitForm({isSubmit: '1'})"
        >提交</el-button>
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-green"
          icon="el-icon-tickets"
          :disabled="submitFlag"
          @click="submitForm({isSubmit: '0'})"
        >保存</el-button>
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue"
          icon="el-icon-position"
          :disabled="submitFlag"
          v-if="!isFromYunPacs || ypacsRptInfo.examStatus <= 70"
          @click="submitForm({isSubmit: '2'})"
        >确认</el-button>
      </div>
    </disease-layout>
  </div>
</template>

<script>
import '@/assets/css/diseaseReport.scss'
import diseaseLayout from '@/components/diseaseLayout.vue';
import vHtmlPanel from '@/components/vHtmlPanel.vue';
import diseaseReportMixin from '@/mixins/newDiseaseReport.js'
import api from '@/config/api.js';
import { request } from '@/utils/common.js';
import * as constant from '@/config/constant.js'
import '../../../public/template-lib/utils/structFun2.0.js'
import dayjs from 'dayjs';
import $ from 'jquery';
export default {
  name: "xaJzxPage",
  components: {diseaseLayout, vHtmlPanel},
  mixins: [diseaseReportMixin],
  props: {
    showHistoyByHalf: {  //展示历史，即分两边显示
      type: Boolean,
      default: false,
    },
    isHistory: {   //是否是历史记录
      type: Boolean,
      default: false,
    },
    resultFormData: {   //已保存的结果集合
      type: Array,
      default() {return []}
    },
    patientInfo: {  //患者信息
      type: Object,
      default() {return {}}
    },
    pageTitle: {   //页面标题
      type: String,
      default: ''
    },
    newestCheckResult: {  //最近一次检查
      type: Object,
      default() {return {}}
    },
    domId: {  //存放静态页面的最外层id名
      type: String,
      default: 'fjrmJzxDefaultPage',
      require: true
    },
    getDataFinished: {   //完成加载接口数据？
      type: Boolean,
      default: false,
    },
    docId: {   //文档id
      type: String,
      default: '',
    },
    jzxHtmlUrl: {   //文档id
      type: String,
      default: '',
    },
    resImpress: {   //保存的诊断内容
      type: String,
      default: '',
    },
  },
  data() {
    return {
      submitFlag: false,
      isDevelopment: process.env.NODE_ENV === 'development',
      selfImpression: '',
      entryPeopleUserId: '',  //录入员Id
      entryPeopleUserName: '',  //录入员name
      entryPeopleList: [],
      originEntryPeopleList: [],
    }
  },
  watch: {
    getDataFinished: {
      handler(newVal) {
        if(newVal) {
          this.allContent = {
            docContent: {
              impression: this.resImpress
            }
          }
          this.$nextTick(() => {
            let param = {
              type: 'edit', 
              data: this.resultFormData, 
              mainId: this.isHistory ? '.readonly' : '.editabled', 
              srcUrl: !this.isDevelopment ? '/sreport' : ''
            }
            this.loadedHandler(param);
          })
        }
      },
      deep: true,
      immediate: true
    },
    impression: {
      handler(newVal) {
        this.selfImpression = newVal;
      },
      immediate: true
    },
    // token: {
    //   handler(newVal) {
    //     if(newVal && !this.trace) {
    //       this.getEntryPeopleList();
    //     }
    //   },
    //   immediate: true,
    // },
  },
  computed: {
    impression() {
      return this.rtStructure && this.rtStructure.impression ? this.rtStructure.impression : ''
    },
    token() {
      return this.$store.state.token;
    },
    rptImageList() {
      return this.$store.state.report.rptImageList;
    },
    isFromYunPacs() {
      return this.$store.state.isFromYunPacs || this.localQueryParams.fromYunPacs==='1' || this.$route.query.fromYunPacs==='1';
    },
    ypacsRptInfo() {
      return this.$store.state.report.ypacsRptInfo;
    },
  },
  mounted() {
  },
  methods: {
    // 静态html引用成功
    htmlLoadedHandler(html) {
      if(window.loadTemplateScriptAndCss) {
        window.loadTemplateScriptAndCss(html);
      }
      this.$emit('htmlLoaded', html);
    },

    // isSubmit 确认2则进入预览报告页面进行打印pdf,0保存直接进入预览报告页
    async submitForm(subParams) {
      let {isSubmit, newMode, toRedis} = subParams;
      // this.$store.commit('report/setRptImage', [...list]);
      this.createResultData(toRedis);
      this.submitFlag = true;
      let totalImpression = this.selfImpression;
      this.resultData.push({
        desc: "录入员id和姓名，/连接",
        id:"jzx-lry",
        name: '录入员',
        val: this.entryPeopleUserId + '/' + this.entryPeopleUserName
      })
      // totalImpression += '\n\n（甲状腺分类标准：0类：临床疑似病例但超声无异常发现，需要追加其他检查。1类：阴性，超声检查未见异常声像改变。2类：检查所见为良性病变，但需要临床随访。3类：良性病变可能性大，恶性风险＜5%。4类：恶性风险5%-90%。4A类：恶性风险5%-10%；4B类：恶性风险10%-50%；4C类：恶性风险50%-90%。5类：提示癌的风险性＞90%。6类：细胞学检出癌细胞，确诊为癌。）';
      totalImpression += `\n\n（本报告TI-RADS分类采用C-TIRADS 2020标准）`;
      let data = {
        patternName: '福建人民医院-甲状腺筛查',
        optType: isSubmit,
        description: this.createPacsDescFun(),
        impression: totalImpression,
        docAttr: this.resultData,
        busInfo: {
        },
        mainFlag: '1'
      };

      // console.log(data);
      // this.submitFlag = false;
      // return;

      let postParams = this.saveParams(data);
      if(toRedis) {
        this.submitFlag = false;
        return [postParams];
      }
      // 调云pacs相关报告接口
      let yParams = {
        ...postParams,
        ypacsRptInfo: this.ypacsRptInfo,
      }
      let apiRes = await this.saveDataByApi([postParams]);
      if(!apiRes) {
        this.submitFlag = false;
        return false;
      }

      if(this.sourceType === '1') {
        this.submitFlag = false;
      }

      let pacsRes = true;
      if(Number(this.sourceType) > 1 || this.isFromYunPacs) {
        if(this.entryPeopleUserId) {
          yParams.otherInfo = {
            entryPeople: this.entryPeopleUserName,
            entryPeopleUserId: this.entryPeopleUserId
          }
        }
        pacsRes = await this.saveOrSubmitToYPacs(yParams, isSubmit, !this.docId);
        if(!pacsRes) {
          this.submitFlag = false;
          return false;
        }
      } else {
        pacsRes = await this.$store.dispatch('report/uploadRptImage', {vm: this, params: {examNo: this.patientInfo ? this.patientInfo.examNo : ''}});
      }
      
      if(apiRes && pacsRes) {
        setTimeout(() => {
          if(!newMode) {
            this.srCommonInnerOpt(isSubmit, true);  //2确认时才上传pdf2，其他传1
          }
          this.submitFlag = false;
        }, 1000);
        return apiRes;
      }
    },

    // 生成pacs报告描述
    createPacsDescFun() {
      let description = this.rtStructure && this.rtStructure.description ? this.rtStructure.description : null;
      let strList = [];
      if(description) {
        let descKeyList = ['jzxInfoStr', 'shInfoStr', 'bzDescription', 'lbjDesStr'];
        descKeyList.forEach((key) => {
          if(description[key]) {
            strList.push(description[key])
          }
        })
      }
      // 特殊情况
      if($('#rmjz-rt-70').val()) {
        strList.push($('#rmjz-rt-70').val());
      }
      return strList.join('\n');
    },

    // 生成pacs报告结论
    createImpressionFun() {
      let impression = this.selfImpression;
      // 去掉相关标签及样式
      // impression = impression.replace(/<span class="bold-txt">/ig, '');
      // impression = impression.replace(/<\/span>\s?/ig, '');
      // let reg = /<input.*?>\s?/ig;
      // let matchList = impression.match(reg);
      // if(matchList && matchList.length) {
      //   let valReg = /value\=\".*(?=\")/;  //IE、ipad、iphone不兼容?<=
      //   // let valReg = /(?<=value\=\").*(?=\")/ig;
      //   matchList.forEach((item) => {
      //     let value = item.match(valReg)[0];
      //     value = value.replace('value="', '');
      //     impression = impression.replace(item, value);
      //   })
      // }
      return impression;
    },
    /**
     * 获取报告信息
     * @desc 在 getEnrtyUser 函数没有获取到录入员信息时，会调用此方法获取录入员信息
     */
    async onGetEntryStaff() {
      const params = {
        examNo: this.patientInfo.examNo,
      }
      const headers = {
        token: this.token,
      }
      const response = await request(api.getExamInfoForSR, 'post', params, headers);
      if (!response) return this.$message.error('获取录入员信息失败');

      const { status, message, result } = response;
      if (status !== '0') return this.$message.error(message);

      const { enterStaff, enterStaffNo } = result;
      enterStaff ? this.entryPeopleUserName = enterStaff : '';
      enterStaffNo ? this.entryPeopleUserId = enterStaffNo : '';
    },
    // 提取填过的录入员
    getEnrtyUser() {
      let lryStr = this.findValByIdCommon(this.resultFormData, 'jzx-lry');
      if(lryStr) {
        let [id, name] = lryStr.split('/');
        this.entryPeopleUserId = id;
        this.entryPeopleUserName = name;
      } else {
        this.entryPeopleUserId = this.ypacsRptInfo.otherInfo ? this.ypacsRptInfo.otherInfo.entryPeopleUserId : '';
        this.entryPeopleUserName = this.ypacsRptInfo.otherInfo ? this.ypacsRptInfo.otherInfo.entryPeople : '';
      }
      if (this.entryPeopleUserId) return;
      this.onGetEntryStaff();
    },

    // 获取录入员列表
    async getEntryPeopleList() {
      this.getEnrtyUser();
      this.originEntryPeopleList = [];
      this.entryPeopleList = [];
      let res = await request(api.getEntryPeopleList, 'post', {
        hospitalCode: this.userInfo.hospitalCode,
        deptCode: this.userInfo.deptCode,
      },
      { 
        headers: {token: this.token},
      });
      if(!res || res.status !== '0') {
        return;
      }
      this.entryPeopleList = res.result || [];
      this.originEntryPeopleList = JSON.parse(JSON.stringify(this.entryPeopleList));
      if(this.entryPeopleList.length) {
        let srEntryUserId = JSON.parse(window.localStorage.getItem(constant.DEFAULT_ENTRY_PEOPLE) || '""');
        if(!this.entryPeopleUserId && srEntryUserId) {
          let arr = this.entryPeopleList.filter(item => item.userId === srEntryUserId);
          if(arr.length) {
            this.entryPeopleUserId = srEntryUserId;
            this.entryPeopleUserName = arr[0].name;
          }
        }
      }
    },

    // 修改录入员
    entryChangeHandler(val) {
      if(!val) {
        window.localStorage.removeItem(constant.DEFAULT_ENTRY_PEOPLE);
        this.entryPeopleUserName = '';
        return;
      }
      let arr = this.entryPeopleList.filter(item => item.userId === val);
      if(arr.length) {
        this.entryPeopleUserName = arr[0].name;
        window.localStorage.setItem(constant.DEFAULT_ENTRY_PEOPLE, JSON.stringify(val));
      }
    },

    // 删除图像
    deleteImg(img) {
      this.$confirm('确认移除该图像？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        let image = this.rptImageList;
        image = image.filter(item => item !== img);
        this.$store.commit('report/setRptImage', [...image]);
        window.top.postMessage({
          message: 'updateImage',
          data: img
        }, '*');
      })
    },
  }
}
</script>

<style lang="scss">
.fjrmJzx-form {
  width: 100%;
  height: 100%;
  .jzx-img-box {
    display: flex;
    flex-wrap: wrap;
    .img-item {
      width: 200px;
      height: 140px;
      margin-right: 8px;
      position: relative;
      cursor: pointer;
      background: #000;
      margin-bottom: 8px;
      img {
        width: 100%;
        height: 100%;
      }
      .del-wrap {
        display: none;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: rgba(0,0,0,.6);
        color: #fff;
      }
      &:hover {
        .del-wrap {
          display: block;
        }
      }
    }
  }
  .disease-body {
    padding: 0;
  }
  .disease-body {
    .widthAndAuto {
      width: 100%;
      margin: 0;
    }
  }
  .descript-content {
    word-break: break-all;
    white-space: pre-wrap;
    margin-right: 8px;
    .el-textarea__inner {
      color: #303133;
    }
  }
  .bold-txt {
    font-size: 18px;
    color: #000;
    font-weight: bold;
  }
  input[readonly]:focus-visible {
    outline: none;
  }
  .disease-other {
    margin-top: 0 !important;
    border-top: none;
    & > .widthAndAuto {
      width: 960px;
      margin: 0 auto;
      .title {
        width: 110px;
        padding-right: 27px;
        margin-right: 0;
        text-align: right;
      }
    }
  }
}
.lr-user {
  position: absolute;
  right: 10px;
  top: 0;
  font-size: 14px;
  font-weight: 500;
  .popper__arrow {
    left: 50% !important;
  }
}
</style>