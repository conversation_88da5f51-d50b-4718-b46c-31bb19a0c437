$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#fzblyzdd1', 'edit'); // 测试
  // initHtmlScript('#fzblyzdd1', 'view'); // 测试
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var vueVm; // vue实例
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele, dev) {
  initVueVomp(); // vue实例化必须比结构化处理早
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
      dignosisImgs: [],
      asyncAjax: true
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type === 'view') {
      vueVm.initData('view');
    } else {
      vueVm.initData('edit');
    }
  }
  dev && vueVm.initData(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initVueVomp() {
  new Vue({
    el: '#data-wrap',
    data() {
      return {
        isEdit: false,
        dataType: 1, // 1=中国香港 2=NICHD 亚裔
        resultFormHeight1: 186,
        resultFormHeight2: 222,
        sd2Label1: '-2SD/+2SD', // 中国香港类型时sd2的表头
        sd2Label2: '范围', // NICHD类型时sd2的表头

        chartNameList: ['BPD', 'HC', 'AC', 'FL', 'EFW'],
        paramCodeList: [],
        formulaIdList: [
          {formulaId: '10001', name: '双顶径(BPD)'},
          {formulaId: '10002', name: '头围(HC)'},
          {formulaId: '10003', name: '腹围(AC)'},
          {formulaId: '10004', name: '股骨长(FL)'},
          {formulaId: '10005', name: '体重(EFW)'},
        ], // 参数id
        publicInfo: {}, // { patLocalId, examNo, ... }
        currDataList: [], // 当前检查数据列表
        currData: {},
        pregnancyWeeks: '', // 当前孕周
        searchType: 'sickId', // 查询类型 sickId identityCard
        searchNameArr: [], // 查询参数名称
        searchParams: {}, // 查询入参记录
        historyDataList: [], // 查询历史结果
        chartMap: {},
        checkedList: [], // 已选图表
        $save: null,
        chartSample: {
          1: { // 中国香港
            title: '',
            list: [
              {type: 'line', dataKey: '', name: '-2SD', color: '#945FB9'},
              {type: 'line', dataKey: '', name: 'Mean', color: '#C0C4CC'},
              {type: 'line', dataKey: '', name: '+2SD', color: '#945FB9'},
              {type: 'point', dataKey: '', name: '测量值', color: 'rgb(238,102,102)'},
            ],
          },
          2: { // 亚裔
            title: '',
            list: [
              {type: 'line', dataKey: '', name: '10%', color: 'rgb(24, 114, 255)'},
              {type: 'line', dataKey: '', name: '50%', color: 'rgb(234, 124, 204)'},
              {type: 'line', dataKey: '', name: '90%', color: 'rgb(249, 181, 12)'},
              {type: 'point', dataKey: '', name: '测量值', color: 'rgb(238,102,102)'},
            ],
          },
        }, // 图例
        editableKeys: ['paramValue'], // 可双击编辑的列
        isShowEditDialog: false, // 是否显示修改孕周弹窗
        editForm: {
          dateType: '1', // 1=末次月经 2=NT检查
          lastDate: '',
          weeks: 0,
          days: 0,
          resultWeeks: '',
        },
        lastMensesDate: '', // 记录填写的末次月经日期
      }
    },
    created() { 
      vueVm = this;
    },
    mounted() {
      document.addEventListener('pointerdown', this.cancelEdit);
    },
    destroyed() {
      document.removeEventListener('pointerdown', this.cancelEdit);
    },
    methods: {
      initData(type) {
        this.isEdit = type === 'edit';
        this.publicInfo = publicInfo;

        // 测试
        // this.publicInfo.examNo = '0003363700';

        this.getParamCodeList();
        this.$save = document.getElementById('saveData');
        
        $('.check-handle').on('change', function() {
          let val = this.value;
          let idx = vueVm.checkedList.indexOf(val);
          if (this.checked) {
            if (idx === -1) {
              vueVm.checkedList.push(val);
            }
          } else {
            if (idx !== -1) {
              vueVm.checkedList.splice(idx, 1);
            }
          }
          if (vueVm.checkedList.length === (vueVm.dataType === 1 ? 4 : 5)) {
            $('.check-handle-all')[0].checked = true;
          } else {
            $('.check-handle-all')[0].checked = false;
          }
          vueVm.saveParams();
        });
        $('.check-handle-all').on('change', function() {
          if (this.checked) {
            vueVm.checkedList = vueVm.dataType === 1 ? vueVm.chartNameList.slice(0, 4) : vueVm.chartNameList.slice(0, 5);
            $('.check-handle').prop('checked', true);
          } else {
            vueVm.checkedList = [];
            $('.check-handle').prop('checked', false);
          }
          vueVm.saveParams();
        });

        this.previewData();

        this.$nextTick(() => {
          this.getCurData();
          this.searchHisParamsReq();
          if (this.isEdit) {

          } else {
            this.addDataToChart();
            this.getChartImgs();
            this.setPacsData();
          }
          window.postMessage({
            message: 'finishAsyncAjax',
            data: {}
          }, '*');
        });
      },
      // 获取图表，保存到结构化字段
      getChartImgs() {
        let dignosisImgs = [];
        this.chartNameList.forEach(k => {
          if (!this.checkedList.includes(k)) {
            return;
          }
          dignosisImgs.push({
            imgName: this.chartMap[k].name + '.jpg',
            imgContent: this.chartMap[k].chart.getDataURL({pixelRatio: 2, backgroundColor: '#fff'})
          });
        });
        if (rtStructure) {
          rtStructure.diffConfig.dignosisImgs = dignosisImgs; // 传给PACS
        }
      },
      // 把测量数据保存到rtStructure，传给PACS
      setPacsData() {
        if (rtStructure) {
          rtStructure.diffConfig.pregnancyWeeks = this.currDataList[0] ? this.currDataList[0].pregnancyWeeks : '';
          rtStructure.diffConfig.areaType = this.dataType; // 1=中国香港 2=NICHD 亚裔
          rtStructure.diffConfig.memo = `注：胎儿生长曲线采用${ this.dataType === 1 ? '中国胎儿' : '亚裔' }生长数据${ this.dataType === 1 ? '（香港中文大学所发表文献）' : '' }`, // 底部注释，要与html的保持一致
          rtStructure.diffConfig.lastMensesDate = this.lastMensesDate;
          rtStructure.diffConfig.measureParam = this.currDataList.map(item => {
            let {paramCode, paramName, paramValue, unit, range} = item;
            return {
              paramCode,
              paramName,
              paramValue,
              unit,
              paramRange: range,
            };
          });
        }
      },
      // 从隐藏节点回显参数
      previewData() {
        let params = this.$save.innerText;
        if (params) {
          params = JSON.parse(params);
          this.checkedList = params.checkedList || [];
          this.searchParams = params.searchParams || {};
          this.searchType = params.searchType || 'sickId';
          this.searchNameArr = params.searchNameArr || [];
          this.dataType = params.dataType || 1;
          this.lastMensesDate = params.lastMensesDate || '';
          if (params.editForm) {
            this.editForm = params.editForm;
          }
        }
      },
      // 保存参数到隐藏节点
      saveParams() {
        this.$save.innerText = JSON.stringify({
          searchType: this.searchType,
          searchNameArr: this.searchNameArr,
          searchParams: this.searchParams,
          checkedList: this.checkedList,
          dataType: this.dataType,
          lastMensesDate: this.lastMensesDate,
          editForm: this.editForm,
        });
      },
      // 切换类型
      changeDataType() {
        $('.check-handle').prop('checked', false);
        $('.check-handle-all').prop('checked', false);
        this.checkedList = [];
        this.getCurData();
        this.searchHisParamsReq();
        this.saveParams();
      },
      // 获取当前数据
      getCurData() {
        if (!this.publicInfo.examNo) {
          this.$message.error('没有检查号');
          return;
        }
        let list = this.getExamMeasureParamList({examNo: this.publicInfo.examNo});

        // 测试
        // list = [];
        // list.push({
        //   paramName: 'BPD',
        //   paramValue: '50',
        //   unit: 'mm',
        //   formulaId: '10001',
        //   pregnancyWeeks: '12周4天',
        // });

        if (list.length === 0) {
          return;
        }

        // 测试
        // list.forEach(row => {
        //   row.pregnancyWeeks = '28周6天';
        // });

        if (this.dataType === 2) { // 亚裔的如果没EFW数据，添加空的，用于手输保存
          if (!list.some(row => row.formulaId === '10005')) {
            list.push({
              paramName: '体重(EFW)',
              unit: 'g',
              formulaId: '10005',
              pregnancyWeeks: list[0].pregnancyWeeks || '',
            });
          }
        }

        list = dealListData(list);
        this.currDataList = list;
        this.currData = list[0];
        this.isEdit && this.addDataToChart();
      },
      // 获取历史数据
      searchHisParams() {
        if (!this.publicInfo.examNo) {
          this.$message.error('没有检查号');
          return;
        }
        let params = {
          currentExamNo: this.publicInfo.examNo,
          paramCodes: this.searchNameArr.join(','),
        };
        if (this.searchType === 'sickId') {
          params.sickId = this.publicInfo.sickId || '';
          if (!params.sickId) {
            params.sickIdType = '0';
          }
        }
        if (this.searchType === 'identityCard') {
          params.identityCard = this.publicInfo.identityCard || '';
          if (!params.identityCard) {
            params.identityCardTypy = '0';
          }
        }
        this.searchParams = params;
        this.saveParams();
        this.searchHisParamsReq();
      },
      searchHisParamsReq() {
        if (!this.publicInfo.examNo) {
          this.$message.error('没有检查号');
          return;
        }
        if (!this.searchParams.currentExamNo) {
          return;
        }
        let list = this.getExamMeasureParamList(this.searchParams);
        if (list.length === 0) {
          this.$message.error('查询不到该患者的历史数据');
        }
        list = dealListData(list);
        this.historyDataList = list;
        this.isEdit && this.addDataToChart();
      },
      getExamMeasureParamList(params) {
        return window.getExamMeasureParamList(params);
      },
      getParamCodeList() {
        let list = window.getParamCodeList({examNo: this.publicInfo.examNo});
        this.paramCodeList = list;
      },
      // 将实际数据添加到列表
      addDataToChart() {
        let list = [...this.currDataList, ...this.historyDataList];
        let BPD_Data = [];
        let HC_Data = [];
        let AC_Data = [];
        let FL_Data = [];
        let EFW_Data = [];
        list.forEach(({formulaId, chartX, chartY}) => {
          switch (formulaId) {
            case '10001':
              BPD_Data.push([chartX, chartY]);
              break;
            case '10002':
              HC_Data.push([chartX, chartY]);
              break;
            case '10003':
              AC_Data.push([chartX, chartY]);
              break;
            case '10004':
              FL_Data.push([chartX, chartY]);
              break;
            case '10005':
              EFW_Data.push([chartX, chartY]);
              break;
          }
        });

        if (this.dataType === 1) {
          this.chartMap['BPD'] = {name: '双顶径(BPD)', chart: makeBPDChart(BPD_Data)};
          this.chartMap['HC'] = {name: '头围(HC)', chart: makeHCChart(HC_Data)};
          this.chartMap['AC'] = {name: '腹围(AC)', chart: makeACChart(AC_Data)};
          this.chartMap['FL'] = {name: '股骨长(FL)', chart: makeFLChart(FL_Data)};
        }
        if (this.dataType === 2) {
          this.chartMap['BPD'] = {name: '双顶径(BPD)', chart: makeBPDChart2(BPD_Data)};
          this.chartMap['HC'] = {name: '头围(HC)', chart: makeHCChart2(HC_Data)};
          this.chartMap['AC'] = {name: '腹围(AC)', chart: makeACChart2(AC_Data)};
          this.chartMap['FL'] = {name: '股骨长(FL)', chart: makeFLChart2(FL_Data)};
          this.chartMap['EFW'] = {name: '体重(EFW)', chart: makeEFWChart(EFW_Data)};
        }
      },
      // 保存体重
      async saveParam(row) {
        let num = parseFloat(row.editParamValue);
        if (isNaN(num)) {
          row.editParamValue = '';
          return;
        }
        row.editParamValue = num;
        let target = this.paramCodeList.find(p => p.formulaId === row.formulaId);
        updateExamNoAndParamCode({
          examNo: this.publicInfo.examNo,
          paramCode: target.paramCode,
          paramValue: num
        });
        this.$message.success('保存' + target.paramName + '成功');
        this.getCurData();
      },
      // 点击图例
      clickSampleItem(item) {

      },
      // 双击单元格编辑
      handleCellClick(row, column, cell, event) {
        if (!this.isEdit || !this.editableKeys.includes(column.property)) {
          return;
        }
        row.isEdit = column.property;
        this.$nextTick(() => {
          cell.querySelector('input').focus();
        });
      },
      // 取消编辑
      cancelEdit(e) {
        for (let row of this.currDataList) {
          row.isEdit = '';
        }
      },
      // 修改孕周
      editPregnancyWeeks() {
        this.isShowEditDialog = true;
      },
      // 重新计算孕周
      calculateWeeks() {
        this.$nextTick(() => {
          let {dateType, lastDate, weeks, days, resultWeeks} = this.editForm;
          if (!lastDate) {
            return;
          }
          let allDays; // 总天数
          if (dateType === '1') {
            let dayDiff = window.dayjs().diff(lastDate, 'day'); // 相差天数
            if (dayDiff < 0) {
              return;
            }
            allDays = dayDiff;
          }
          if (dateType === '2') {
            let dayDiff = window.dayjs().diff(lastDate, 'day'); // 相差天数
            if (dayDiff < 0) {
              return;
            }
            let weeksFormat = parseInt(weeks) || 0;
            let daysFormat =  parseInt(days) || 0;
            let weeksNum = weeksFormat || 0;
            let daysNum = daysFormat || 0;
            if (weeksNum < 0 || daysNum < 0) {
              return;
            }
            let lastDays = weeksNum * 7 + daysNum; // 上次孕周天数
            allDays = dayDiff + lastDays;
          }
          let newWeeks = Math.floor(allDays / 7);
          let newDays = allDays % 7;
          this.editForm.resultWeeks = `${newWeeks}周${newDays}天`;
        });
      },
      // 更新孕周
      updateExams() {
        if (!this.editForm.resultWeeks) {
          this.$message.error('请填写数据');
          return;
        }
        var params = {
          examNo: this.publicInfo.examNo,
          pregnancyWeeks: this.editForm.resultWeeks
        };
        fetchAjax({
          url: api.updateExams,
          data: JSON.stringify(params),
          async: false,
          successFn: function (res) {
            if (res.status == '0') {
              vueVm.$message.success('保存孕周成功');
              vueVm.getCurData();
              vueVm.isShowEditDialog = false;
              if (vueVm.editForm.dateType === '1') {
                vueVm.lastMensesDate = vueVm.editForm.lastDate;
              } else {
                vueVm.lastMensesDate = '';
              }
              vueVm.saveParams();
            }
          }
        });
      },
    },
  });
}

// 处理列表数据
function dealListData(list) {
  if (vueVm.dataType === 1) {
    list = list.filter(row => row.formulaId !== '10005'); // 香港的去掉EFW数据
  }
  list.forEach(row => {
    row.examDateTime = (row.examDate || '') + ' ' + (row.examTime || '');
    let pregnancyWeeks = row.pregnancyWeeks || '';
    if (!pregnancyWeeks) {
      return;
    }
    let wPattern = pregnancyWeeks.match(/^\d+w?/); // 纯数字当作周
    if (!wPattern) {
      wPattern = pregnancyWeeks.match(/\d+周/);
    }
    let dPattern = pregnancyWeeks.match(/\d+d/);
    if (!dPattern) {
      dPattern = pregnancyWeeks.match(/\d+天/);
    }
    let week, day;
    if (wPattern) {
      week = wPattern[0].replace(/[w周]/, '');
    }
    if (dPattern) {
      day = dPattern[0].replace(/[d天]/, '');
    }
    if (!week && !day) {
      return;
    }
    week = week ? Number(week) : 0;
    day = day ? Number(day) : 0;
    let weekNum = (week + day / 7).toFixed(5);
    weekNum = Number(weekNum);
    let chartX = weekNum;
    let chartY = row.paramValue || 0;

    // 中国香港
    if (vueVm.dataType === 1) {
      let val, sd;
      switch (row.formulaId) {
        case '10001': // 图表纵坐标为mm
          val = getBPD(weekNum);
          sd = getBPD_SD(weekNum);
          if (row.unit === 'cm') {
            chartY = chartY * 10;
          }
          break;
        case '10002': // 图表纵坐标为cm
          val = getHC(weekNum);
          sd = getHC_SD(weekNum);
          if (row.unit === 'mm') {
            chartY = chartY / 10;
          }
          break;
        case '10003': // 图表纵坐标为cm
          val = getAC(weekNum);
          sd = getAC_SD(weekNum);
          if (row.unit === 'mm') {
            chartY = chartY / 10;
          }
          break;
        case '10004': // 图表纵坐标为mm
          val = getFL(weekNum);
          sd = getFL_SD(weekNum);
          if (row.unit === 'cm') {
            chartY = chartY * 10;
          }
          break;
      }
      if (sd) {
        if (row.unit === 'mm') { // 计算出来的是cm
          val = val * 10;
          sd = sd * 10;
        }
        let sd2m = val - sd * 2;
        let sd2p = val + sd * 2;
        if (row.paramValue < sd2m) {
          row.valMark = '↓';
        }
        if (row.paramValue > sd2p) {
          row.valMark = '↑';
        }
        row.range = sd2m.toFixed(1) + ' - ' + sd2p.toFixed(1);
      }
    }

    // NICHD 亚裔
    if (vueVm.dataType === 2) {
      // 周龄向下取整
      weekNum = Math.floor(weekNum);
      chartX = weekNum;

      let sd2m, sd2p;
      switch (row.formulaId) {
        case '10001':
          sd2m = getDataType2('BPD', '3%', weekNum);
          sd2p = getDataType2('BPD', '97%', weekNum);
          break;
        case '10002':
          sd2m = getDataType2('HC', '3%', weekNum);
          sd2p = getDataType2('HC', '97%', weekNum);
          break;
        case '10003':
          sd2m = getDataType2('AC', '3%', weekNum);
          sd2p = getDataType2('AC', '97%', weekNum);
          break;
        case '10004':
          sd2m = getDataType2('FL', '3%', weekNum);
          sd2p = getDataType2('FL', '97%', weekNum);
          break;
        case '10005':
          sd2m = getDataType2('EFW', '3%', weekNum);
          sd2p = getDataType2('EFW', '97%', weekNum);
          break;
      }
      if (row.unit === 'cm') { // 图表纵坐标为mm
        chartY = chartY * 10;
      }
      if (row.unit === 'cm') { // 查表的值单位都是mm
        sd2m = sd2m / 10;
        sd2p = sd2p / 10;
      }
      if (row.paramValue < sd2m) {
        row.valMark = '↓';
      }
      if (row.paramValue > sd2p) {
        row.valMark = '↑';
      }
      if (sd2m && sd2p) {
        row.range = sd2m.toFixed(1) + ' - ' + sd2p.toFixed(1);
      }
    }

    row.chartX = chartX;
    row.chartY = chartY;
    row.pregnancyWeeksText = week + '周' + day + '天';
    row.editParamValue = row.paramValue;
    row.isEdit = '';
  });
  return list;
}

// 公式计算的单位都是cm
// 获取孕周对应标准值
function getBPD(GA) {
  return -1.295192 + 0.197042 * GA + 0.008247 * GA * GA - 0.000163 * GA * GA * GA;
}
function getHC(GA) {
  return -5.530556 + 0.766353 * GA + 0.030694 * GA * GA - 0.000643 * GA * GA * GA;
}
function getAC(GA) {
  return -6.181539 + 0.884154 * GA + 0.013282 * GA * GA - 0.000255 * GA * GA * GA;
}
function getFL(GA) {
  return -4.445082 + 0.492073 * GA - 0.0067 * GA * GA + 0.000042 * GA * GA * GA;
}
// 获取孕周对应标准值范围
function getBPD_SD(GA) {
  return 1.253 * (0.176837 + 0.002714 * GA);
}
function getHC_SD(GA) {
  return 1.253 * (0.539913 + 0.007092 * GA);
}
function getAC_SD(GA) {
  return 1.253 * (0.056796 + 0.031209 * GA);
}
function getFL_SD(GA) {
  return 1.253 * (0.103184 + 0.002539 * GA);
}

// 获取亚裔值
function getDataType2(name, percent, weekNum) {
  let d = window.NICHD_Data[name];
  if (!d) {
    return;
  }
  let arr = d[percent];
  if (!arr) {
    return;
  }
  // 数据表周龄从10开始
  let idx = weekNum - 10;
  return arr[idx];
}

// 绘制BPD图表
function makeBPDChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 12; GA <= 40; GA++) {
    let v = getBPD(GA);
    let sd = getBPD_SD(GA);
    v = v * 10;
    sd = sd * 10;
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-bpd',
    xName: '胎龄(w)',
    yName: 'BPD(mm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 110,
    intervalY: 10,
    intervalYsub: 2,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '+2SD',
    sd0Name: 'Mean',
    sd2mName: '-2SD',
    sd2pLineColor: '#945FB9',
    sd0LineColor: '#C0C4CC',
    sd2mLineColor: '#945FB9',
    sd2pLableColor: '#945FB9',
    sd0LableColor: '#945FB9',
    sd2mLableColor: '#945FB9',
    trueData
  });
}
// 绘制HC图表
function makeHCChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 15; GA <= 42; GA++) {
    let v = getHC(GA);
    let sd = getHC_SD(GA);
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-hc',
    xName: '胎龄(w)',
    yName: 'HC(cm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 45,
    intervalY: 5,
    intervalYsub: 1,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '+2SD',
    sd0Name: 'Mean',
    sd2mName: '-2SD',
    sd2pLineColor: '#945FB9',
    sd0LineColor: '#C0C4CC',
    sd2mLineColor: '#945FB9',
    sd2pLableColor: '#945FB9',
    sd0LableColor: '#945FB9',
    sd2mLableColor: '#945FB9',
    trueData
  });
}
// 绘制AC图表
function makeACChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 15; GA <= 42; GA++) {
    let v = getAC(GA);
    let sd = getAC_SD(GA);
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-ac',
    xName: '胎龄(w)',
    yName: 'AC(cm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 45,
    intervalY: 5,
    intervalYsub: 1,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '+2SD',
    sd0Name: 'Mean',
    sd2mName: '-2SD',
    sd2pLineColor: '#945FB9',
    sd0LineColor: '#C0C4CC',
    sd2mLineColor: '#945FB9',
    sd2pLableColor: '#945FB9',
    sd0LableColor: '#945FB9',
    sd2mLableColor: '#945FB9',
    trueData
  });
}
// 绘制FL图表
function makeFLChart(trueData) {
  let sd2p = []; // +2SD
  let sd0 = []; // 标准
  let sd2m = []; // -2SD
  for (let GA = 15; GA <= 42; GA++) {
    let v = getFL(GA);
    let sd = getFL_SD(GA);
    v = v * 10;
    sd = sd * 10;
    sd2p.push([GA, v + sd * 2]);
    sd0.push([GA, v]);
    sd2m.push([GA, v - sd * 2]);
  }
  return drawChart({
    id: 'chart-fl',
    xName: '胎龄(w)',
    yName: 'FL(mm)',
    minGA: 10,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 90,
    intervalY: 10,
    intervalYsub: 2,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '+2SD',
    sd0Name: 'Mean',
    sd2mName: '-2SD',
    sd2pLineColor: '#945FB9',
    sd0LineColor: '#C0C4CC',
    sd2mLineColor: '#945FB9',
    sd2pLableColor: '#945FB9',
    sd0LableColor: '#945FB9',
    sd2mLableColor: '#945FB9',
    trueData
  });
}

// 绘制BPD图表 亚裔
function makeBPDChart2(trueData) {
  let sd2p = []; // 90%
  let sd0 = []; // 50%
  let sd2m = []; // 10%
  for (let GA = 10; GA <= 40; GA++) {
    sd2p.push([GA, getDataType2('BPD', '90%', GA)]);
    sd0.push([GA, getDataType2('BPD', '50%', GA)]);
    sd2m.push([GA, getDataType2('BPD', '10%', GA)]);
  }
  return drawChart({
    id: 'chart-bpd',
    xName: '胎龄(w)',
    yName: 'BPD(mm)',
    minGA: 5,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 110,
    intervalY: 10,
    intervalYsub: 2,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '90%',
    sd0Name: '',
    sd2mName: '10%',
    sd2pLineColor: 'rgb(249, 181, 12)',
    sd0LineColor: 'rgb(234, 124, 204)',
    sd2mLineColor: 'rgb(24, 114, 255)',
    sd2pLableColor: 'rgb(249, 181, 12)',
    sd0LableColor: 'rgb(234, 124, 204)',
    sd2mLableColor: 'rgb(24, 114, 255)',
    trueData
  });
}
// 绘制HC图表 亚裔
function makeHCChart2(trueData) {
  let sd2p = []; // 90%
  let sd0 = []; // 50%
  let sd2m = []; // 10%
  for (let GA = 10; GA <= 40; GA++) {
    sd2p.push([GA, getDataType2('HC', '90%', GA)]);
    sd0.push([GA, getDataType2('HC', '50%', GA)]);
    sd2m.push([GA, getDataType2('HC', '10%', GA)]);
  }
  return drawChart({
    id: 'chart-hc',
    xName: '胎龄(w)',
    yName: 'HC(mm)',
    minGA: 5,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 400,
    intervalY: 50,
    intervalYsub: 10,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '90%',
    sd0Name: '',
    sd2mName: '10%',
    sd2pLineColor: 'rgb(249, 181, 12)',
    sd0LineColor: 'rgb(234, 124, 204)',
    sd2mLineColor: 'rgb(24, 114, 255)',
    sd2pLableColor: 'rgb(249, 181, 12)',
    sd0LableColor: 'rgb(234, 124, 204)',
    sd2mLableColor: 'rgb(24, 114, 255)',
    trueData
  });
}
// 绘制AC图表 亚裔
function makeACChart2(trueData) {
  let sd2p = []; // 90%
  let sd0 = []; // 50%
  let sd2m = []; // 10%
  for (let GA = 10; GA <= 40; GA++) {
    sd2p.push([GA, getDataType2('AC', '90%', GA)]);
    sd0.push([GA, getDataType2('AC', '50%', GA)]);
    sd2m.push([GA, getDataType2('AC', '10%', GA)]);
  }
  return drawChart({
    id: 'chart-ac',
    xName: '胎龄(w)',
    yName: 'AC(mm)',
    minGA: 5,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 450,
    intervalY: 50,
    intervalYsub: 10,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '90%',
    sd0Name: '',
    sd2mName: '10%',
    sd2pLineColor: 'rgb(249, 181, 12)',
    sd0LineColor: 'rgb(234, 124, 204)',
    sd2mLineColor: 'rgb(24, 114, 255)',
    sd2pLableColor: 'rgb(249, 181, 12)',
    sd0LableColor: 'rgb(234, 124, 204)',
    sd2mLableColor: 'rgb(24, 114, 255)',
    trueData
  });
}
// 绘制FL图表 亚裔
function makeFLChart2(trueData) {
  let sd2p = []; // 90%
  let sd0 = []; // 50%
  let sd2m = []; // 10%
  for (let GA = 10; GA <= 40; GA++) {
    sd2p.push([GA, getDataType2('FL', '90%', GA)]);
    sd0.push([GA, getDataType2('FL', '50%', GA)]);
    sd2m.push([GA, getDataType2('FL', '10%', GA)]);
  }
  return drawChart({
    id: 'chart-fl',
    xName: '胎龄(w)',
    yName: 'FL(mm)',
    minGA: 5,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 90,
    intervalY: 10,
    intervalYsub: 2,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '90%',
    sd0Name: '',
    sd2mName: '10%',
    sd2pLineColor: 'rgb(249, 181, 12)',
    sd0LineColor: 'rgb(234, 124, 204)',
    sd2mLineColor: 'rgb(24, 114, 255)',
    sd2pLableColor: 'rgb(249, 181, 12)',
    sd0LableColor: 'rgb(234, 124, 204)',
    sd2mLableColor: 'rgb(24, 114, 255)',
    trueData
  });
}
// 绘制EFW图表 亚裔
function makeEFWChart(trueData) {
  let sd2p = []; // 90%
  let sd0 = []; // 50%
  let sd2m = []; // 10%
  for (let GA = 10; GA <= 40; GA++) {
    sd2p.push([GA, getDataType2('EFW', '90%', GA)]);
    sd0.push([GA, getDataType2('EFW', '50%', GA)]);
    sd2m.push([GA, getDataType2('EFW', '10%', GA)]);
  }
  return drawChart({
    id: 'chart-efw',
    xName: '胎龄(w)',
    yName: 'EFW(g)',
    minGA: 5,
    maxGA: 45,
    intervalX: 5,
    minY: 0,
    maxY: 4800,
    intervalY: 400,
    intervalYsub: 80,
    sd2p,
    sd0,
    sd2m,
    sd2pName: '90%',
    sd0Name: '',
    sd2mName: '10%',
    sd2pLineColor: 'rgb(249, 181, 12)',
    sd0LineColor: 'rgb(234, 124, 204)',
    sd2mLineColor: 'rgb(24, 114, 255)',
    sd2pLableColor: 'rgb(249, 181, 12)',
    sd0LableColor: 'rgb(234, 124, 204)',
    sd2mLableColor: 'rgb(24, 114, 255)',
    trueData
  });
}

function drawChart(setting) {
  let myChart = echarts.init(document.getElementById(setting.id));
  
  // 指定图表的配置项和数据
  let options = {
    animation: false,
    grid: {
      left: 60,
      bottom: 44,
      right: 10,
      top: 15
    },
    xAxis: [
      {
        zlevel: 1,
        name: setting.xName,
        nameLocation: 'middle',
        nameTextStyle: {
          color: '#000'
        },
        nameGap: 26,
        min: setting.minGA,
        max: setting.maxGA,
        interval: setting.intervalX,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#1885F2',
          showMinLabel: true,
          showMaxLabel: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#76B0F9'
          }
        },
      },
      {
        zlevel: 0,
        min: setting.minGA,
        max: setting.maxGA,
        interval: 1,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#D3EEF9'
          }
        }
      }
    ],
    yAxis: [
      {
        zlevel: 1,
        name: setting.yName,
        nameLocation: 'middle',
        nameTextStyle: {
          color: '#000'
        },
        nameGap: 40,
        min: setting.minY,
        max: setting.maxY,
        interval: setting.intervalY,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#1885F2',
          showMinLabel: true,
          showMaxLabel: true
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#76B0F9'
          }
        }
      },
      {
        zlevel: 0,
        min: setting.minY,
        max: setting.maxY,
        interval: setting.intervalYsub,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#D3EEF9'
          }
        }
      }
    ],
    series: [
      {data: setting.sd2p, name: setting.sd2pName, lineStyle: {color: setting.sd2pLineColor, width: 1}, endLabel: {color: setting.sd2pLableColor, show: true, formatter: '{a}', distance: 1}, silent: true, type: 'line', smooth: true, symbol: 'none', zlevel: 2},
      {data: setting.sd0, name: setting.sd0Name, lineStyle: {color: setting.sd0LineColor, width: 1}, endLabel: {color: setting.sd0LableColor, show: true, formatter: '{a}', distance: 1}, silent: true, type: 'line', smooth: true, symbol: 'none', zlevel: 2},
      {data: setting.sd2m, name: setting.sd2mName, lineStyle: {color: setting.sd2mLineColor, width: 1}, endLabel: {color: setting.sd2mLableColor, show: true, formatter: '{a}', distance: 1}, silent: true, type: 'line', smooth: true, symbol: 'none', zlevel: 2},
      {data: setting.trueData || [], symbolSize: 6, silent: true, name: 'trueData', type: 'scatter', itemStyle: {opacity: 1, color: setting.curValColor}, zlevel: 3}
    ]
  };
  myChart.setOption(options);
  return myChart;
}