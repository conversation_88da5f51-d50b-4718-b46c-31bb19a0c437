<template>
  <div class="rxscRm-form" v-loading="!getDataFinished">
    <disease-layout :localQueryParams="localQueryParams" 
      :patientInfo="patientInfo" 
      :halfWrap="showHistoyByHalf" 
      :readonly="isHistory" 
      :pageTitle="pageTitle"
      :showOther="imgList.length > 0"
      otherDesTitle="图像"
      footTitle="超声提示"
    >
      <template slot="inner-header-right">
        <div class="lr-user">
          <span>录入员：</span>
          <el-select 
            :popper-append-to-body="false"
            size="mini" 
            filterable
            collapse-tags
            placeholder="请选择"
            :disabled="trace"
            v-model="entryPeopleUserId"
            @change="entryChangeHandler"
          >
            <el-option label="请选择" value=""></el-option>
            <el-option 
              v-for="(item, i) in entryPeopleList"
              :key="i"
              :label="item.name" 
              :value="item.userId"
            ></el-option>
          </el-select>
        </div>
      </template>
      <div slot="header-right">
        <slot name="h-right-btn"></slot>
      </div>
      <div :id="domId" slot="body">
        <!-- 主html -->
        <v-html-panel 
          :htmlUrl.sync="htmlUrl" 
          @loaded="htmlLoadedHandler"
        ></v-html-panel>
      </div>
      <div slot="other-descript">
        <div class="rx-img-box" id="rxscImgBox">
          <div class="img-item" v-for="(img, i) in imgList">
            <img :src="img.src" alt="">
            <div class="del-wrap" @click="deleteImg(img)">
              <i class="el-icon-delete" style="margin-right: 5px;"></i>移除
            </div>
          </div>
        </div>
      </div>
      <div slot="footer-descript">
        <div class="descript-content">
          <el-input type="textarea" v-model="selfImpression" :rows="5" resize="none"></el-input>
        </div>
      </div>
      <div slot="footer-btn" v-if="!isFromYunPacs && !sourceType">
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue"
          icon="el-icon-position"
          :disabled="submitFlag"
          @click="submitForm({isSubmit:'1'})"
          v-if="!isFromYunPacs || ypacsRptInfo.examStatus < 60 || ypacsRptInfo.examStatus == 65"
        >提交</el-button>
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-green"
          icon="el-icon-tickets"
          :disabled="submitFlag"
          @click="submitForm({isSubmit:'0'})"
        >保存</el-button>
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue"
          icon="el-icon-position"
          :disabled="submitFlag"
          @click="submitForm({isSubmit:'2'})"
          v-if="!isFromYunPacs || ypacsRptInfo.examStatus <= 70"
        >确认</el-button>
      </div>
    </disease-layout>
  </div>
</template>

<script>
import '@/assets/css/diseaseReport.scss'
import diseaseLayout from '@/components/diseaseLayout.vue';
import vHtmlPanel from '@/components/vHtmlPanel.vue';
import diseaseReportMixin from '@/mixins/newDiseaseReport.js'
import api from '@/config/api.js';
import { request } from '@/utils/common.js';
import '../../../public/template-lib/utils/structFun.js'
import html2canvas from "html2canvas";
import * as constant from '@/config/constant.js'
import dayjs from 'dayjs';
import $ from 'jquery';
export default {
  name: "rxscXaPage",
  components: {diseaseLayout, vHtmlPanel},
  mixins: [diseaseReportMixin],
  props: {
    showHistoyByHalf: {  //展示历史，即分两边显示
      type: Boolean,
      default: false,
    },
    isHistory: {   //是否是历史记录
      type: Boolean,
      default: false,
    },
    resultFormData: {   //已保存的结果集合
      type: Array,
      default() {return []}
    },
    patientInfo: {  //患者信息
      type: Object,
      default() {return {}}
    },
    pageTitle: {   //页面标题
      type: String,
      default: ''
    },
    newestCheckResult: {  //最近一次检查
      type: Object,
      default() {return {}}
    },
    domId: {  //存放静态页面的最外层id名
      type: String,
      default: 'kgjDefaultPage',
      require: true
    },
    getDataFinished: {   //完成加载接口数据？
      type: Boolean,
      default: false,
    },
    docId: {   //文档id
      type: String,
      default: '',
    },
    htmlUrl: {   //文档id
      type: String,
      default: '',
    },
    resImpress: {   //保存的诊断内容
      type: String,
      default: '',
    },
  },
  data() {
    return {
      prefixName: "rxscRm-",
      patDocId: "1026",
      submitFlag: false,
      imgList: [],
      // impression: '',
      winRpt: "",
      entryPeopleUserId: '',  //录入员Id
      entryPeopleUserName: '',  //录入员name
      entryPeopleList: [],
      originEntryPeopleList: [],
      selfImpression: '',
    }
  },
  watch: {
    getDataFinished: {
      handler(newVal) {
        if(newVal) {
          this.allContent = {
            docContent: {
              impression: this.resImpress
            }
          }
          this.$nextTick(async () => {
            let param = {
              type: 'edit', 
              data: this.resultFormData, 
              mainId: '.editabled', 
              oldDataText: true
            }
            await this.loadedHandler(param);
          })
        }
      },
      deep: true,
      immediate: true
    },
    rptImageList: {
      handler(newVal) {
        this.imgList = newVal.filter(img => !img.fileName.includes('_bz'));
      },
      immediate: true,
      deep: true
    },
    // token: {
    //   handler(newVal) {
    //     if(newVal && !this.trace) {
    //       this.getEntryPeopleList();
    //     }
    //   },
    //   immediate: true,
    // },
    impression: {
      handler(newVal) {
        this.selfImpression = newVal;
      },
      immediate: true
    }
  },
  computed: {
    impression() {
      if(!this.rtStructure) {
        return '';
      }
      return this.rtStructure.impression || '';
    },
    token() {
      return this.$store.state.token;
    },
    rptImageList() {
      return this.$store.state.report.rptImageList;
    },
    isFromYunPacs() {
      return this.$store.state.isFromYunPacs || this.localQueryParams.fromYunPacs==='1' || this.$route.query.fromYunPacs==='1';
    },
    ypacsRptInfo() {
      return this.$store.state.report.ypacsRptInfo;
    },
  },
  methods: {
    /**
     * 获取报告信息
     * @desc 在 getEnrtyUser 函数没有获取到录入员信息时，会调用此方法获取录入员信息
     */
    async onGetEntryStaff() {
      const params = {
        examNo: this.patientInfo.examNo,
      }
      const headers = {
        token: this.token,
      }
      const response = await request(api.getExamInfoForSR, 'post', params, headers);
      if (!response) return this.$message.error('获取录入员信息失败');

      const { status, message, result } = response;
      if (status !== '0') return this.$message.error(message);

      const { enterStaff, enterStaffNo } = result;
      enterStaff ? this.entryPeopleUserName = enterStaff : '';
      enterStaffNo ? this.entryPeopleUserId = enterStaffNo : '';
    },
    // 提取填过的录入员
    getEnrtyUser() {
      let lryStr = this.findValByIdCommon(this.resultFormData, 'rxrm-lry');
      if(lryStr) {
        let [id, name] = lryStr.split('/');
        this.entryPeopleUserId = id;
        this.entryPeopleUserName = name;
      } else {
        this.entryPeopleUserId = this.ypacsRptInfo.otherInfo ? this.ypacsRptInfo.otherInfo.entryPeopleUserId : '';
        this.entryPeopleUserName = this.ypacsRptInfo.otherInfo ? this.ypacsRptInfo.otherInfo.entryPeople : '';
      }
      if (this.entryPeopleUserId) return;
      this.onGetEntryStaff();
    },

    // 获取录入员列表
    async getEntryPeopleList() {
      this.getEnrtyUser();
      this.originEntryPeopleList = [];
      this.entryPeopleList = [];
      let res = await request(api.getEntryPeopleList, 'post', {
        hospitalCode: this.userInfo.hospitalCode,
        deptCode: this.userInfo.deptCode,
      },
      { 
        headers: {token: this.token},
      });
      if(!res || res.status !== '0') {
        return;
      }
      this.entryPeopleList = res.result || [];
      this.originEntryPeopleList = JSON.parse(JSON.stringify(this.entryPeopleList));
      if(this.entryPeopleList.length) {
        let srEntryUserId = JSON.parse(window.localStorage.getItem(constant.DEFAULT_ENTRY_PEOPLE) || '""');
        if(!this.entryPeopleUserId && srEntryUserId) {
          let arr = this.entryPeopleList.filter(item => item.userId === srEntryUserId);
          if(arr.length) {
            this.entryPeopleUserId = srEntryUserId;
            this.entryPeopleUserName = arr[0].name;
          }
        }
      }
    },

    // 修改录入员
    entryChangeHandler(val) {
      if(!val) {
        window.localStorage.removeItem(constant.DEFAULT_ENTRY_PEOPLE);
        this.entryPeopleUserName = '';
        return;
      }
      let arr = this.entryPeopleList.filter(item => item.userId === val);
      if(arr.length) {
        this.entryPeopleUserName = arr[0].name;
        window.localStorage.setItem(constant.DEFAULT_ENTRY_PEOPLE, JSON.stringify(val));
      }
    },

    // 搜索录入员
    // filterHandler(val) {
    //   if(!val) {
    //     this.entryPeopleList = JSON.parse(JSON.stringify(this.originEntryPeopleList));
    //     return;
    //   }
    //   this.entryPeopleList = this.originEntryPeopleList.filter(item => item.name.includes(val) || item.inputCode.toLowerCase().includes(val.toLowerCase()));
    // },
    // 删除图像
    deleteImg(img) {
      this.$confirm('确认移除该图像？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        let image = this.rptImageList;
        image = image.filter(item => item !== img);
        this.$store.commit('report/setRptImage', [...image]);
        window.top.postMessage({
          message: 'updateImage',
          data: img
        }, '*');
      })
    },
    // 静态html引用成功
    htmlLoadedHandler(html) {
      this.$emit('htmlLoaded', html);
    },
    // 将病灶示意图转成canvas
    async imgToCanvas(isSubmit, type, newMode) {
      this.$nextTick(() => {
        let imgWrap = $(".sketch-map")[0];
        $('.rxscRmEditReport').parents("#app").scrollLeft(0);
        html2canvas(imgWrap, {
          allowTaint: true,
          useCORS: true,
          backgroundColor: '#fff',
          width: imgWrap.offsetWidth,
          height: 140,
          dpi: window.devicePixelRatio * 2,
          scrollY: 0, 
          scrollX: 0,
        }).then((canvas)=> {
          canvas.toBlob((blobObj) => {
            let imgSrc = window.URL.createObjectURL(blobObj);
            let curImage = {
              blob: blobObj, 
              specialTxt: '_bz', 
              imageUid: Date.now(),
              src: imgSrc,
            }
            $("#bz-canvas").attr("src", imgSrc);
            // window.top.open(imgSrc)
            curImage.fileName = 'RptImg_1_1_bz' + curImage.imageUid;
            let list = this.rptImageList.filter(image => !image.fileName.includes('_bz'));
            this.$store.commit('report/setRptImage', [curImage, ...list]);
            this.submitForm({isSubmit, type, finishCanvas: true, newMode});
          })
        })
      })
    },

    // isSubmit 确认2则进入预览报告页面进行打印pdf,0保存直接进入预览报告页
    async submitForm(subParams) {
      let {isSubmit, type, finishCanvas = false, newMode, toRedis} = subParams;
      this.submitFlag = true;
      this.createResultData(toRedis);
      // // 将病灶示意图转成canvas
      // if(this.getValById('rxscRm-0001.001') === '可见异常' && !finishCanvas) {
      //   this.submitFlag = true;
      //   await this.imgToCanvas(isSubmit, type, newMode);
      //   return 'stop';
      // }
      if(this.getValById('rxscRm-0001.001') === '未见异常') {
        let list = this.rptImageList.filter(image => !image.fileName.includes('_bz'));
        this.$store.commit('report/setRptImage', [...list]);
      }
      let totalImpression = this.selfImpression;
      this.resultData.push({
        desc: "录入员id和姓名，/连接",
        id:"rxrm-lry",
        name: '录入员',
        val: this.entryPeopleUserId + '/' + this.entryPeopleUserName
      })
      // totalImpression += '\n\n（乳腺分类标准：0类：超声检查不能全面评估病变，需要其他影像学检查进一步评估。1类：阴性，超声检查未见异常声像改变，每年定期超声复查。2类：良性病变，基本上可以排除恶性病变可能。3类：良性病变可能性大，恶性危险性＜2%，建议3-6个月超声定期复查或进一步检查。4类：可能是恶性病变，恶性危险性3%-94%。4A类：恶性危险性3%-30%。4B类：恶性危险性31%-60%。4 C类：恶性危险性61%-94%。5类：高度可疑恶性病变，恶性危险性＞95%。6类：已活检证实为恶性病变。）';

      totalImpression += `\n\n（本报告BI-RADS分类采用ACR BI-RADS 2013标准）`;

      let data = {
        patternName: '福建人民医院-乳腺筛查',
        optType: isSubmit,
        description: this.createPacsDescFun(),
        impression: totalImpression,
        docAttr: this.resultData,
        busInfo: {
        },
        mainFlag: '1'
      };

      let postParams = this.saveParams(data);
      if(toRedis) {
        this.submitFlag = false;
        return [postParams];
      }
      // 调云pacs相关报告接口
      let yParams = {
        ...postParams,
        ypacsRptInfo: this.ypacsRptInfo,
      }
      let apiRes = await this.saveDataByApi([postParams]);
      if(!apiRes) {
        this.submitFlag = false;
        return false;
      }

      if(this.sourceType === '1') {
        this.submitFlag = false;
        // return apiRes;
      }

      let pacsRes = true;
      if(Number(this.sourceType) > 1 || this.isFromYunPacs) {
        if(this.entryPeopleUserId) {
          yParams.otherInfo = {
            entryPeople: this.entryPeopleUserName,
            entryPeopleUserId: this.entryPeopleUserId
          }
        }
        pacsRes = await this.saveOrSubmitToYPacs(yParams, isSubmit, !this.docId);
        if(!pacsRes) {
          this.submitFlag = false;
          return false;
        }
      } else {
        pacsRes = await this.$store.dispatch('report/uploadRptImage', {vm: this, params: {examNo: this.patientInfo ? this.patientInfo.examNo : ''}});
      }
      
      if(apiRes && pacsRes) {
        setTimeout(() => {
          if(!newMode) {
            this.srCommonInnerOpt(isSubmit, true);  //2确认时才上传pdf2，其他传1
          }
          this.submitFlag = false;
        }, 1000);
        return apiRes;
      }
    },

    // 生成pacs报告描述
    createPacsDescFun() {
      let idMapResult = this.idMapResult;
      let rtStructure = this.rtStructure;
      let allDesc = [];
      // 基本情况
      var jbqkStr = $(".jbqk-desc").html();
      jbqkStr && allDesc.push(jbqkStr);
      // 乳腺术后
      var rxshStr = $(".rxsh-desc").html();
      rxshStr && allDesc.push(rxshStr);
      // 假体
      var jtStr = $('.jt-desc').html();
      jtStr && allDesc.push(jtStr);

      let str = '';
      let bzDetailList = this.rtStructure.description;
      if(bzDetailList && bzDetailList.length) {
        let hasExitObj = {}
        let bzStrList = [];
        bzDetailList.forEach((bzDetail, i) => {
          let bzStr = '';
          if(['左侧', '右侧'].includes(bzDetail.side)) {
            if(hasExitObj[bzDetail.side] === undefined) {
              bzStr += (bzDetail.side === '左侧' ? '左乳' : '右乳') + '：\n';
            }
          }
          let posText = `点/距乳头(mm)：${bzDetail.clockPos || '-'}/${bzDetail.diffRt || '-'}`;
          if(bzDetail.posWay === '象限定位法') {
            posText = `处于：${bzDetail.xxPos || '-'}`;
          }
          bzStr += `病灶${i+1}：${posText}${bzDetail.bzSide?'，大小约：'+bzDetail.bzSide:''}；BI-RADS分级：${bzDetail.level}级\n`;
          let tempList = [], tempStr = '';
          if(bzDetail.posOtherDesc) {
            tempList.push(bzDetail.posOtherDesc); //其他情况
          }
          tempStr = `${bzDetail.hsType}病灶`;
          // 边界
          let hsDetail = [];
          if(bzDetail.hsBjVal) {
            hsDetail.push(bzDetail.hsBjVal);
          }
          if(bzDetail.hsTsVal) {
            hsDetail.push(bzDetail.hsTsVal);
          }
          if(hsDetail.length) {
            tempStr += `(${hsDetail.join('、')})`;
          }
          tempList.push(tempStr);  //回声类型

          tempList.push(`呈${bzDetail.shape}`);  //形状

          tempStr = `边缘${bzDetail.by}`;
          if(bzDetail.byType) {
            tempStr += `(${bzDetail.byType})`;
          }
          tempList.push(tempStr);  //边缘
          tempList.push(bzDetail.fx);   //与皮肤平行

          // 钙化
          if(bzDetail.isGh) {
            if(bzDetail.isGh === '有钙化') {
              tempList.push('可见钙化');
              if(bzDetail.ghPos) {
                tempList.push('呈' + bzDetail.ghPos);
              }
              if(bzDetail.ghType) {
                tempList.push((bzDetail.ghCount || '') + bzDetail.ghType);
              }
            } else {
              tempList.push(bzDetail.isGh + '灶');
            }
          }

          // 后方回声
          if(bzDetail.hfhs) {
            tempStr = '后方回声' + (bzDetail.hfhs !== '无改变' ? '伴' : '') + bzDetail.hfhs;
            tempList.push(tempStr);
          }
          if(tempList.length) {
            bzStr += tempList.join('，') + '。';
          } 

          if(bzDetail.isXl) {
            bzStr += 'CDFI:';
            if(bzDetail.isXl === '未见血流') {
              bzStr += '结节内未见异常血流信号';
            } else {
              if(bzDetail.xlCount || bzDetail.xlPos) {
                bzStr += '结节内见可见血流信号，呈' + bzDetail.xlCount + (bzDetail.xlPos ? bzDetail.xlPos : '血流');
              } else {
                bzStr += '结节内可见血流信号';
              }
            }
            bzStr += '。'
          }
          if(bzDetail.relateText) {  //相关特征
            bzStr += '相关特征：' + bzDetail.relateText + '。';
          }
          // if(bzDetail.specialText) {  //特殊情况
          //   bzStr += '特殊情况：' + bzDetail.specialText + '。';
          // }
          if(bzDetail.otherDesc) {  //其他征象
            bzStr += '其他征象：' + bzDetail.otherDesc + '。';
          }

          if(hasExitObj[bzDetail.side] !== undefined) {
            bzStrList.splice(hasExitObj[bzDetail.side] + 1, 0, bzStr);
          } else {
            bzStrList.push(bzStr);
          }
          hasExitObj[bzDetail.side] = i;
        })

        str += bzStrList.join('\n');
        if(str) {
          allDesc.push(str);
        }
      }

      // 特殊情况
      let specialText = curElem.find(`[id="rxscRm-0014.01"] .rt-sr-w:not(.rt-hide)`).val()
      specialText && allDesc.push(specialText);

      // 淋巴结
      var lbStr = $('.lb-desc').html();
      lbStr && allDesc.push(lbStr);
      return allDesc.join('\n');
    },
    
    // 生成pacs报告结论
    createImpressionFun() {
      let impression = this.impression;
      // 去掉相关标签及样式
      impression = impression.replace(/<i><\/i>；/ig, '。\n');
      impression = impression.replace(/<i><\/i>/ig, '。');
      impression = impression.replace(/<span class="bold-txt">/ig, '');
      impression = impression.replace(/<\/span>\s?/ig, '');
      let reg = /<input.*?>\s?/ig;
      let matchList = impression.match(reg);
      if(matchList && matchList.length) {
        let valReg = /value\=\".*(?=\")/;  //IE、ipad、iphone不兼容?<=
        // let valReg = /(?<=value\=\").*(?=\")/ig;
        matchList.forEach((item) => {
          let value = item.match(valReg)[0];
          value = value.replace('value="', '');
          impression = impression.replace(item, value);
        })
      }
      return impression;
    },

    // 引用AI数据
    quoteAiDataHandler(docContent) {
      // abnormal 1、阳性 0、阴性
      let {docAttr = [], abnormal = '0'} = docContent;
      if(abnormal === '0') {
        $('[name="RG-0001.001"][value="未见异常"]').click();
      } else {
        $('[name="RG-0001.001"][value="可见异常"]').click();
        if(docAttr && docAttr.length) {
          docAttr.forEach((item, i) => {
            setTimeout(() => {
              window.addBzHandler(item);
            }, i*50)
          })
        }
      }
    }
  }
}
</script>

<style lang="scss">
.rxscRm-form {
  width: 100%;
  height: 100%;
  #defaultPage {
    height: 100%;
  }
  .disease-body {
    padding: 0;
  }
  .disease-other {
    margin-top: 0 !important;
  }
  .rx-img-box {
    display: flex;
    flex-wrap: wrap;
    .img-item {
      width: 200px;
      height: 140px;
      margin-right: 8px;
      position: relative;
      cursor: pointer;
      background: #000;
      margin-bottom: 8px;
      img {
        width: 100%;
        height: 100%;
      }
      .del-wrap {
        display: none;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: rgba(0,0,0,.6);
        color: #fff;
      }
      &:hover {
        .del-wrap {
          display: block;
        }
      }
    }
  }
  .descript-content {
    white-space: pre-wrap;
    margin-right: 8px;
    .el-textarea__inner {
      color: #303133;
    }
  }
  .bold-txt {
    font-weight: bold;
    font-size: 18px;
  }
}
.lr-user {
  position: absolute;
  right: 10px;
  top: 0;
  font-size: 14px;
  font-weight: 500;
  .popper__arrow {
    left: 50% !important;
  }
}
</style>