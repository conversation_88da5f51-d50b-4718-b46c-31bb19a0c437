$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var staffType16List = []; // 检查者列表
var staffType0List = []; // 审核医师
var userInfo = null; // 当前登录用户
var reqPhysician; // 审核医师名字
var vngSelData = [
  { 
    id: ['gdfyvng-rt-110'],//随机扫视
    width: '208',
    optionList: [
      {id: '正常(无欠冲、过冲、慢化)',title: '正常(无欠冲、过冲、慢化)'},
      {id: '异常(欠冲)',title: '异常(欠冲)'},
      {id: '异常(过冲)',title: '异常(过冲)'},
      {id: '异常(慢化)',title: '异常(慢化)'},
    ]
  },
  { 
    id: ['gdfyvng-rt-112','gdfyvng-rt-113','gdfyvng-rt-114'],//平滑跟踪试验
    width: '133',
    optionList: [
      {id: 'Ⅰ型曲线',title: 'Ⅰ型曲线'},
      {id: 'Ⅱ型曲线',title: 'Ⅱ型曲线'},
      {id: 'Ⅲ型曲线',title: 'Ⅲ型曲线'},
      {id: 'Ⅳ型曲线',title: 'Ⅳ型曲线'},
    ]
  },
  { 
    id: ['gdfyvng-rt-115','gdfyvng-rt-116'],//视动试验
    width: '133',
    optionList: [
      {id: '双侧左右对称',title: '双侧左右对称'},
      {id: '双侧左右不对称',title: '双侧左右不对称'},
    ]
  },
  { 
    id: ['gdfyvng-rt-117'],//凝视试验
    width: '171',
    optionList: [
      {id: '出现',title: '出现'},
      {id: '未出现',title: '未出现'},
    ]
  },
  { 
    id: ['gdfyvng-rt-128'],//自发性眼震
    width: '171',
    optionList: [
      {id: '(+)',title: '(+)'},
      {id: '(-)',title: '(-)'},
      {id: '(±)',title: '(±)'},
    ]
  },
  { 
    id: ['gdfyvng-rt-130'],//位置试验——Dix-Hallplke
    width: '171',
    optionList: [
      {id: 'Dix-Hallplke：(-)',title: 'Dix-Hallplke：(-)'},
      {id: 'Dix-Hallplke：(+)',title: 'Dix-Hallplke：(+)'},
      {id: 'Dix-Hallplke：(±)',title: 'Dix-Hallplke：(±)'},
    ]
  },
  { 
    id: ['gdfyvng-rt-132'],//位置试验——Roll-Test
    width: '171',
    optionList: [
      {id: 'Roll-Test：(-)',title: 'Roll-Test：(-)'},
      {id: 'Roll-Test：(+)',title: 'Roll-Test：(+)'},
      {id: 'Roll-Test：(±)',title: 'Roll-Test：(±)'},
    ]
  },
  { 
    id: ['gdfyvng-rt-134','gdfyvng-rt-135'],//位置试验——Romber 's Test
    width: '133',
    optionList: [
      {id: '(+)',title: '(+)'},
      {id: '(±)',title: '(±)'},
      {id: '(-)',title: '(-)'},
    ]
  },
  { 
    id: ['gdfyvng-rt-151'],//建议
    width: '240',
    optionList: [
      {id: '结合病史随诊。',title: '结合病史随诊。'},
      {id: '可试行前庭康复操。',title: '可试行前庭康复操。'},
      {id: '结合病史进一步检查。',title: '结合病史进一步检查。'},
      {id: '结合病史进一步排除中枢病变。',title: '结合病史进一步排除中枢病变。'},
      {id: '结合病史随诊，可试行前庭康复操。',title: '结合病史随诊，可试行前庭康复操。'},
    ]
  },
]; // vng所有下拉项信息
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('gdfyvng1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    userInfo = publicInfo.userInfo;
    reqPhysician = rtStructure.examInfo.reqPhysician;
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon();
      showCaImg();
    } else {
      initPage();
    }
  }
}
// 初始化页面
function initPage() {
  staffType16List = getDictUsersBaseList({staffType: '1,6'});
  staffType16List = transformOptList(staffType16List, 'name');
  staffType0List = getDictUsersBaseList({staffType: '0'});
  staffType0List = transformOptList(staffType0List, 'name');
  initDropdown();
  initSelectData();
  initDatePicker();
  setDefaultVal();
}

// 未填写过的报告，设置默认值
function setDefaultVal() {
  if(isNewRpt()) {
    // 凝视试验
    // $('#gdfyvng-rt-117').val('未出现');
    // setNssyDefaultVal();

    $('#gdfyvng-rt-128').val('(-)');

    // 位置试验
    // $('#gdfyvng-rt-130').val('Dix-Hallplke：(-)');
    // setDHDefaultVal();
    // $('#gdfyvng-rt-132').val('Roll-Test：(-)');
    // setRollTestDefaultVal();

    // $('#gdfyvng-rt-134').val('(-)');
    // $('#gdfyvng-rt-135').val('(-)');

    $('#gdfyvng-rt-149').val('1、视动中枢试验大致正常     2、位置试验见上述描述   \n3、冷热试验提示双侧前庭功能大致对称');
    $('#gdfyvng-rt-151').val('结合病史随诊。');
    setCurrUser();
  }
}

// 是否新报告
function isNewRpt() {
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  return resultData.length === 0;
}

// 设置凝视试验输入框默认值
function setNssyDefaultVal() {
  !$('#gdfyvng-rt-119').val().trim() && $('#gdfyvng-rt-119').val('无病理性眼震');
  !$('#gdfyvng-rt-121').val().trim() && $('#gdfyvng-rt-121').val('无病理性眼震');
  !$('#gdfyvng-rt-123').val().trim() && $('#gdfyvng-rt-123').val('无病理性眼震');
  !$('#gdfyvng-rt-125').val().trim() && $('#gdfyvng-rt-125').val('无病理性眼震');
  !$('#gdfyvng-rt-127').val().trim() && $('#gdfyvng-rt-127').val('无病理性眼震');
}
// 设置Dix-HallpikeTest输入框默认值
function setDHDefaultVal() {
  !$('#gdfyvng-rt-131').val().trim() && $('#gdfyvng-rt-131').val('双侧悬头位及坐位未见明显眼震及晕感');
}
// 设置RollTest输入框默认值
function setRollTestDefaultVal() {
  !$('#gdfyvng-rt-133').val().trim() && $('#gdfyvng-rt-133').val('双转头位未见明显眼震及晕感');
}

// 默认当前登录人
function setCurrUser() {
  // 检查者
  if (userInfo && !$('.staffType16').val()) {
    $('.staffType16').val(userInfo.name);
    $('.staffType16StaffNo').text(userInfo.staffNo);
  }

  // 审核医师，默认读取开单医生
  var reqPhysicianStaffNo;
  if (reqPhysician) {
    for (var i = 0; i < staffType0List.length; i++) {
      if (staffType0List[i].name === reqPhysician) {
        reqPhysicianStaffNo = staffType0List[i].staffNo;
        break;
      }
    }
  }
  if (reqPhysician && reqPhysicianStaffNo && !$('.staffType0').val()) {
    $('.staffType0').val(reqPhysician);
    $('.staffType0StaffNo').text(reqPhysicianStaffNo);
  }
}

// 初始化检查者和审核医生下拉
function initDropdown() {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: '.staffType16',
    data: staffType16List,
    className: 'laySelLab',
    click: function(obj) {
      this.elem.val(obj.title);
      $('.staffType16StaffNo').text(obj.staffNo);
    },
  });
  dropdown.render({
    elem: '.staffType0',
    data: staffType0List,
    className: 'laySelLab',
    click: function(obj) {
      this.elem.val(obj.title);
      $('.staffType0StaffNo').text(obj.staffNo);
    },
  });
}

// 初始化所有输入下拉框
function initSelectData() {
  vngSelData.forEach(item => {
    let { id=[] } = item;
    id.forEach(subId => {
      initInpAndSel(subId, item.optionList, item.width);
    })
  });
}

// 输入下拉框方法
function initInpAndSel(idList, optionList,lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click:function(obj){
      if (idList === 'gdfyvng-rt-117') {
        setNssyDefaultVal();
      }
      // 位置试验DH默认值
      if (idList === 'gdfyvng-rt-130') {
        setDHDefaultVal();
      }
      // 位置试验RollTest默认值
      if (idList === 'gdfyvng-rt-132') {
        setRollTestDefaultVal();
      }
      this.elem.val(obj.title);
    },
    style: lenVal !== 0 ? `width: ${lenVal}px;` : 'width: calc(100% - 146px)',
  })
}

// 转换下拉数据格式
function transformOptList(list, titleName) {
  let arr = [];
  list.forEach((item,index) => {
    if(titleName) {
      arr.push({ ...item,title:item[titleName],id:index,templet: `<span title='${item[titleName]}'>${item[titleName]}</span>` });
    }else {
      arr.push({ title:item,id:index,templet: `<span title='${item}'>${item}</span>` });
    }
  })
  return arr;
}

// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0];
  layui.config({dir: path + 'template-lib/plugins/layui/'});
  $('.date-wrap01').val(layui.util.toDateString(new Date(), 'yyyy-MM-dd'));// 默认当前时间
  layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
      elem: '.date-wrap01',
      type: 'date',
      trigger: 'click',
      format: 'yyyy-MM-dd',
    });
  });
}

// 打印时显示ca签名图像
function showCaImg() {
  $('.staffType16').hide();
  $('.staffType16Ca').attr('src', api.getExamRptSignImage + '?staffNo=' + $('.staffType16StaffNo').text()).show();
  $('.staffType0').hide();
  $('.staffType0Ca').attr('src', api.getExamRptSignImage + '?staffNo=' + $('.staffType0StaffNo').text()).show();
}

// 回显预览内容,隐藏未填写的节点
function initViewCon() {
  var groupList = [
    'sssy-block', // 扫视试验
    'phgzsy-block', // 平滑跟踪试验（按Benita分型）
    'sdsy-block', // 视动试验（20°/sec）
    'nssy-block', // 凝视试验
    'zfxyz-block', // 自发性眼震
    'wzsy-block', // 位置试验
    'romber-block', // Romber
    'lrsy-block', // 冷热试验

    'nssy-center',
    'nssy-right',
    'nssy-left',
    'nssy-up',
    'nssy-down',

    'wzsy-dh',
    'wzsy-rt'
  ];
  groupList.forEach(function(g) {
    var isFilled;
    var curr = $('.' + g);
    curr.each(function() {
      var txt = $(this).find('input');
      txt.each(function() { if (this.value.trim()) { isFilled = true; } });
    });
    if (!isFilled) {
      curr.hide();
    }
  });

  // 没选眼震，凝视试验整块隐藏
  if (!$('#gdfyvng-rt-117').val().trim()) {
    $('.nssy-block').hide();
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = $('#gdfyvng-rt-149').val();
  rtStructure.recommendation = $('#gdfyvng-rt-151').val();
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}