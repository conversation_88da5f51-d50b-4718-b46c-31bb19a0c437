
$(function() {
  window.initHtmlScript = initHtmlScript;
})

var menuList = [
  { name: "病人信息", value: 0 },
  { name: "注射信息", value: 1 },
  { name: "饮水信息", value: 2 },
  { name: "显像信息", value: 3 },
  { name: "其他信息", value: 4 },
]
var userList = [];  //技师人员列表
var drinkTypeList = [];  //饮水类型列表
var imagingPartList = [];  //采集部位列表
var mateListFlag = false;  //是否显示药品列表
var waterConClone = '';  //饮水信息模板具体内容
var conventionConClone = '';  //常规显像模板具体内容
var delayConClone = '';  //延迟显像模板具体内容
var enhanceConClone = '';  //增强显像模板具体内容

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      
    } else {
      pageInit();
    }
  }
}

function pageInit() {
  console.log('publicInfo',publicInfo)
  initMenu()
  initBzCloneEle();
  getLocalConfig();
  getIrritabilityName();
  getInjectPartList();
  getStainPartList();
  getNmDrinkTypeList();
  getImagingPartList();
  getDeptList(publicInfo.optId);
  getMateApplyInfo();
  getAgentListPage();
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    addWater();
    addConvention();
    layui.use(['laydate'], function(){
      var laydate = layui.laydate;
      laydate.render({
        elem: '#zsxx-rt-5-001',//指定元素
        type: 'datetime',
        value: ''
      });
      laydate.render({
        elem: '#zsxx-rt-2-006',//指定元素
        type: 'datetime',
        value: ''
      });
    })
  } else {
    displayWaterContent();
    displayConventionContent();
    displayDelayContent();
    displayEnhanceContent();
  }
}

//获取护理配置内容
function getLocalConfig(){
  fetchAjax({
    url: api.getLocalConfig,
    type: 'GET',
    data: '',
    async: false,
    successFn: function (res) {
      if (res.status == '0'&&res.result) {
        mateListFlag = res.result.mateListFlag === "1" ? true : false;  //是否显示药品列表
      }
    },
  })
}
//获取过敏记录列表
function getIrritabilityName(){
  fetchAjax({
    url: api.getCommonUseList,
    data: JSON.stringify({name: "nurse_irritability"}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        let irritabList = [];
        list = res.result || [];
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            irritabList.push({ title: list[t].value, id: list[t].code })
          }
        }
        initInpAndSel('zsxx-rt-1-004-003', irritabList);
      }
    },
  })
}
//获取注射部位列表
function getInjectPartList(){
  fetchAjax({
    url: api.getCommonUseList,
    data: JSON.stringify({name: "inject_part",examClass: ""}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        let irritabList = [];
        list = res.result || [];
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            irritabList.push({ title: list[t].value, id: list[t].value })
          }
        }
        initInpAndSel('zsxx-rt-2-004', irritabList);
      }
    },
  })
}
//获取污染部位列表
function getStainPartList(){
  fetchAjax({
    url: api.getCommonUseList,
    data: JSON.stringify({name: "stain_part",examClass: ""}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        let irritabList = [];
        list = res.result || [];
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            irritabList.push({ title: list[t].value, id: list[t].value })
          }
        }
        initInpAndSel('zsxx-rt-2-0012-003', irritabList);
      }
    },
  })
}
//获取饮水类型列表
function getNmDrinkTypeList(){
  fetchAjax({
    url: api.getCommonUseList,
    data: JSON.stringify({name: "nm_drink_type",examClass: ""}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        drinkTypeList = [];
        list = res.result || [];
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            drinkTypeList.push({ title: list[t].value, id: list[t].value })
          }
        }
      }
    },
  })
}
//获取采集部位列表
function getImagingPartList(){
  fetchAjax({
    url: api.getCommonUseList,
    data: JSON.stringify({name: "imaging_part",examClass: ""}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        imagingPartList = [];
        list = res.result || [];
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            imagingPartList.push({ title: list[t].value, id: list[t].value })
          }
        }
      }
    },
  })
}
//获取技师人员科室
function getDeptList(userId){
  var list = [];
  fetchAjax({
    url: api.getUsersList,
    data: JSON.stringify({userId: userId}),
    async: false,
    successFn: function (res) {
      if (res.status == '0'&& res.result && res.result.length) {
        getUsersList(res.result[0].deptCode);
      }
    },
  })

}
//获取技师人员列表
function getUsersList(deptCode){
  var list = [];
  fetchAjax({
    url: api.getUsersList,
    data: JSON.stringify({deptCode: deptCode}),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var list = res.result || [];
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            userList.push({ title: list[t].name, id: list[t].userId })
          }
        }
        initInpAndSel('zsxx-rt-2-001', userList);
        initInpAndSel('zsxx-rt-2-002', userList);
        initInpAndSel('zsxx-rt-3-001', userList);
        initInpAndSel('zsxx-rt-4-001', userList);
      }
    },
  })

}
//获取当前被使用的药品列表
function getMateList(params) {
  var list = [];
  fetchAjax({
    url: api.getMateList,
    data: JSON.stringify({}),
    async: false,
    successFn: function (res) {
      list = res.result || [];
    },
  })
  return list;
}
//获取造影剂列表
function getAgentListPage() {
  var list = [];
  fetchAjax({
    url: api.getAgentListPage,
    data: JSON.stringify({pageNo: 1, pageSize: 1000}),
    async: false,
    successFn: function (res) {
      list = res.result || [];
    },
  })
  return list;
}
//获取当前被使用的药品列表
function getMateApplyInfo(){
  var list = [];
  fetchAjax({
    url: api.getMateList,
    data: JSON.stringify({applyNo: publicInfo.applyNo}),
    async: false,
    successFn: function (res) {
      if (res.status == '0'&&res.result&& res.result.length) {
        $('#zsxx-rt-2-003').val(res.result[0].mateName);
      }
    },
  })
}
//获取当前被使用的药品的活度
function calculateActivity(){
  var list = [];
  fetchAjax({
    url: api.calculateActivity,
    data: JSON.stringify({mateId: mateId}),
    async: false,
    successFn: function (res) {
      if (res.status == '0'&&res.result&& res.result.length) {
        // $('#zsxx-rt-2-003').val(res.result[0].mateName);
      }
    },
  })
}

function displayWaterContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'zsxx-rt-3')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      var waterList = bzList.filter(bzItem => bzItem.id === 'zsxx-rtwater-1');
      if(waterList && waterList.length&&waterList[0].child){
        waterList[0].child.forEach((item) => {
          var paneId = item.id.replace('zsxx-rtwater-', '');
          addWater(paneId)
        })
      }
    }
  } else {
    curElem.find('.water-list').hide();
  }
}
function displayConventionContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'zsxx-rt-4')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      var waterList = bzList.filter(bzItem => bzItem.id === 'zsxx-convention-1');
      if(waterList && waterList.length&&waterList[0].child){
        waterList[0].child.forEach((item) => {
          var paneId = item.id.replace('zsxx-convention-', '');
          addConvention(paneId)
        })
      }
    }
  } else {
    curElem.find('.convention-list').hide();
  }
}
function displayDelayContent() {
  $('.delay-list').hide()
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'zsxx-rt-4')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      var waterList = bzList.filter(bzItem => bzItem.id === 'zsxx-delay-1');
      if(waterList && waterList.length&&waterList[0].child){
        waterList[0].child.forEach((item) => {
          var paneId = item.id.replace('zsxx-delay-', '');
          addDelay(paneId)
        })
      }
    }
  } else {
    curElem.find('.delay-list').hide();
  }
}
function displayEnhanceContent() {
  $('.enhance-list').hide()
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'zsxx-rt-4')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      var waterList = bzList.filter(bzItem => bzItem.id === 'zsxx-enhance-1');
      if(waterList && waterList.length&&waterList[0].child){
        waterList[0].child.forEach((item) => {
          var paneId = item.id.replace('zsxx-enhance-', '');
          addEnhance(paneId)
        })
      }
    }
  } else {
    curElem.find('.enhance-list').hide();
  }
}

function initMenu() {
  var menuHtml = '';
  for(var i=0; i<menuList.length; i++) {
    var item = menuList[i];
    if(i===0) {
      menuHtml += '<div class="left-item select-left" data-value="'+item.value+'">'+item.name+'</div>';
    }else {
      menuHtml += '<div class="left-item" data-value="'+item.value+'">'+item.name+'</div>';
    }
  }
  curElem.find('.left-content').html(menuHtml);
  curElem.find('.left-item').click(function() {
    var value = $(this).data('value');
    curElem.find('.left-item').removeClass('select-left');
    $(this).addClass('select-left');
    if(value === 0) {
      document.getElementById('zsxx-rt-1').scrollIntoView()
    }else if(value === 1) {
      document.getElementById('zsxx-rt-2').scrollIntoView()
    }else if(value === 2) {
      document.getElementById('zsxx-rt-3').scrollIntoView()
    }else if(value === 3) {
      document.getElementById('zsxx-rt-4').scrollIntoView()
    }else if(value === 4) {
      document.getElementById('zsxx-rt-5').scrollIntoView()
    }
  })
}

function imageClick (type){
  if(type === '1'){
    $('#zsxx-convention-1').addClass('select-item');
    $('#zsxx-delay-1').removeClass('select-item');
    $('#zsxx-enhance-1').removeClass('select-item');
    $('.convention-list').show();
    $('.delay-list').hide();
    $('.enhance-list').hide();
    $('.convention-btn').show();
    $('.delay-btn').hide();
    $('.enhance-btn').hide();
    $('.convention-num').show();
    $('.delay-num').hide();
    $('.enhance-num').hide();
  }else if(type === '2'){
    $('#zsxx-convention-1').removeClass('select-item');
    $('#zsxx-delay-1').addClass('select-item');
    $('#zsxx-enhance-1').removeClass('select-item');
    $('.convention-list').hide();
    $('.delay-list').show();
    $('.enhance-list').hide();
    $('.convention-btn').hide();
    $('.delay-btn').show();
    $('.enhance-btn').hide();
    $('.convention-num').hide();
    $('.delay-num').show();
    $('.enhance-num').hide();
  }else if(type === '3'){
    $('#zsxx-convention-1').removeClass('select-item');
    $('#zsxx-delay-1').removeClass('select-item');
    $('#zsxx-enhance-1').addClass('select-item');
    $('.convention-list').hide();
    $('.delay-list').hide();
    $('.enhance-list').show();
    $('.convention-btn').hide();
    $('.delay-btn').hide();
    $('.enhance-btn').show();
    $('.convention-num').hide();
    $('.delay-num').hide();
    $('.enhance-num').show();
  }
}

function renderTable(agentList) {
  // console.log('agentList',agentList);
  layui.use('table', function () {
    var table = layui.table;
    table.render({
      text: {
        none: '暂无相关数据' //默认：无数据。
      },
      elem: '#agent-table',
      height: '435px',
      cols: [[ //表头
        { field: 'mateName', title: '药品名称', width:180 },
        { field: 'mateBarcode', title: '库存号', },
        { field: 'batchNo', title: '所属批次', },
        { field: 'agentActivity', title: '当前活度(mCi)', },
        { field: 'madeDate', title: '生产日期',},
        { field: 'affirmDoctorName', title: '关联申请单',templet:function(d){
          const { examApplyList } = d
          let text = ""
          if (examApplyList) {
            examApplyList.forEach((item, index) => {
              if (examApplyList.length == 1) {
                text = item.applyNo + "-" + item.name
              } else {
                text += ` ${index + 1}、${item.applyNo}-${item.name}`
              }
            })
          }
          return text
        } }, 
        { field: 'experience', title: '操作',templet: `<script type="text/html" id="toolEventDemo">
            <a class="layui-btn layui-btn-xs" lay-event="click">选择</a>
          </script>`,
          width:80
        }
      ]],
      page: true,
      loading:false,
      id:'agent-table',
      data: agentList
    })
    table.on('tool(agent-table)',function(obj){
      console.log('obj',obj);
      $('#drugInfo').val(JSON.stringify(obj.data));
      $('#zsxx-rt-2-003').val(obj.data.mateName);
      layer.closeAll()
    })
  })
}
function renderSystemTable(agentList) {
  // console.log('agentList',agentList);
  layui.use('table', function () {
    var table = layui.table;
    table.render({
      text: {
        none: '暂无相关数据' //默认：无数据。
      },
      elem: '#system-table',
      height: '435px',
      cols: [[ //表头
        { field: 'agentName', title: '药品名称', width:180 },
        { field: 'agentCode', title: '药物编码', },
        { field: 'examClass', title: '检查类别',width:120 },
        { field: 'halfLife', title: '半衰期', },
        { field: 'experience', title: '操作',templet: `<script type="text/html" id="toolEventDemo">
            <a class="layui-btn layui-btn-xs" lay-event="click">选择</a>
          </script>`,
          width:80
        }
      ]],
      page: true,
      loading:false,
      id:'system-table',
      data: agentList
    })
    table.on('tool(system-table)',function(obj){
      console.log('obj',obj);
      $('#zsxx-rt-2-003').val(obj.data.agentName);
      layer.closeAll()
    })
  })
}

function reloadTable(list){
  layui.use('table', function () {
    var table = layui.table;
    table.reload('agent-table', {
      data: list
    })
  })
}
function agentSelectOpen() {
  let html = `
    <div style="padding:0 16px;height:100%;">
      <div class="layui-tabs layui-tabs-card">
        <ul class="layui-tabs-header" style="display: flex;align-items: center;">
          <li class="layui-item item-select" id="inventory" data-value="1" onclick="tabsClick('1')">当前库存</li>
          <li class="layui-item" id="system" data-value="2" onclick="tabsClick('2')">系统字典</li>
        </ul>
        <div class="layui-tabs-body">
          <div class="layui-tabs-item" id="inventory-table"><table class="layui-table" id="agent-table" lay-filter="agent-table"></table></div>
          <div class="layui-tabs-item" id="systems-table"><table class="layui-table" id="system-table" lay-filter="system-table"></table></div>
        </div>
      </div>
      
    </div>
    `
  layer.open({
    type: 1,
    content: html,
    area: ['760px', '600px'],
    shadeClose: true,
    btnAlign: 'r',
    btn: ['关闭'],
    title: '选择注射药物',
    yes: function (index, layero) {
      layer.close(index)
    },
    success: async function (layero, index) {
      $('#systems-table').hide();
      window.tabsClick = function(val) {
        if(val === '1'){
          $('#inventory').addClass('item-select');
          $('#system').removeClass('item-select');
          $('#systems-table').hide();
          $('#inventory-table').show();
        }else{
          $('#inventory').removeClass('item-select');
          $('#system').addClass('item-select');
          $('#systems-table').show();
          $('#inventory-table').hide();
          $('#system-table').hide();
        }
      }
      let agentList = await getMateList() || []
      let systemList = await getAgentListPage() || []
      // console.log('agentList1',agentList);
      setTimeout(() => {
        renderTable(agentList)
        renderSystemTable(systemList)
      },800)
    }
  })
}


// 复制暂存的具体模板示例内容，为后续添加做准备
function initBzCloneEle() {
  var waterCon = curElem.find('.water-list .bz-item').clone(true);
  waterConClone = '<div class="bz-item" tab-target="zsxx-rtwater-000">'+waterCon.html()+'</div>';
  // console.log('waterConClone1',waterConClone);
  var conventionCon = curElem.find('.convention-list .convention-item').clone(true);
  conventionConClone = '<div class="convention-item" tab-target="zsxx-convention-000">'+conventionCon.html()+'</div>';
  var delayCon = curElem.find('.delay-list .delay-item').clone(true);
  delayConClone = '<div class="delay-item" tab-target="zsxx-delay-000">'+delayCon.html()+'</div>';
  var enhanceCon = curElem.find('.enhance-list .enhance-item').clone(true);
  enhanceConClone = '<div class="enhance-item" tab-target="zsxx-enhance-000">'+enhanceCon.html()+'</div>';
  curElem.find('.water-list').html('');
  curElem.find('.convention-list').html('');
  curElem.find('.delay-list').html('');
  curElem.find('.enhance-list').html('');
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}

// 处理新增饮水信息的html
function appendWaterHtml(paneId, bzLen,oldPaneId) {
  // console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  // console.log('waterConClone',waterConClone)
  var content = waterConClone.replace(reg, paneId);  
  $('.water-list').append(content);
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#zsxx-rtwater-'+paneId+'-3',//指定元素
      type: 'time',
      value: ''
    });
  })
  initInpAndSel('zsxx-rtwater-'+paneId+'-1', drinkTypeList);
  var newPaneBlock = $('.water-list .bz-item[tab-target="zsxx-rtwater-'+paneId+'"] .drug-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}
// 处理新增常规显像的html
function appendConventionHtml(paneId, bzLen,oldPaneId) {
  $('.convention-num').text(bzLen + 1);
  // console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  var content = conventionConClone.replace(reg, paneId);  
  $('.convention-list').append(content);
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#zsxx-convention-'+paneId+'-1',//指定元素
      type: 'datetime',
      value: ''
    });
  })
  initInpAndSel('zsxx-convention-'+paneId+'-2', imagingPartList);
  var newPaneBlock = $('.convention-list .convention-item[tab-target="zsxx-convention-'+paneId+'"] .drug-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}
// 处理新增延迟显像的html
function appendDelayHtml(paneId, bzLen,oldPaneId) {
  $('.delay-num').text(bzLen + 1);
  // console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  var content = delayConClone.replace(reg, paneId);  
  $('.delay-list').append(content);
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#zsxx-delay-'+paneId+'-1',//指定元素
      type: 'datetime',
      value: ''
    });
  })
  initInpAndSel('zsxx-delay-'+paneId+'-2', imagingPartList);
  var newPaneBlock = $('.delay-list .delay-item[tab-target="zsxx-delay-'+paneId+'"] .drug-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}
// 处理新增增强显像的html
function appendEnhanceHtml(paneId, bzLen,oldPaneId) {
  $('.enhance-num').text(bzLen + 1);
  // console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  var content = enhanceConClone.replace(reg, paneId);  
  $('.enhance-list').append(content);
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#zsxx-enhance-'+paneId+'-1',//指定元素
      type: 'datetime',
      value: ''
    });
  })
  initInpAndSel('zsxx-enhance-'+paneId+'-2', imagingPartList);
  var newPaneBlock = $('.enhance-list .enhance-item[tab-target="zsxx-enhance-'+paneId+'"] .drug-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}

function delWater(vm, paneId){
  $(vm).parent().parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
}
function delConvention(vm, paneId){
  var bzLen = $('.convention-list .convention-item').length;
  // console.log('bzLen',bzLen)
  $('.convention-num').text(bzLen - 1);
  $(vm).parent().parent().parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
}
function delDelay(vm, paneId){
  var bzLen = $('.delay-list .delay-item').length;
  // console.log('bzLen',bzLen)
  $('.delay-num').text(bzLen - 1);
  $(vm).parent().parent().parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
}
function delEnhance(vm, paneId){
  var bzLen = $('.enhance-list .enhance-item').length;
  // console.log('bzLen',bzLen)
  $('.enhance-num').text(bzLen - 1);
  $(vm).parent().parent().parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
}

// 添加饮水信息,oldPaneId已保存过的
function addWater(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.water-list .bz-item').length;
  // console.log('bzLen',bzLen)
  var activeTab = 'zsxx-rtwater-' + paneId;
  var newPaneBlock = appendWaterHtml(paneId, bzLen);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '饮水信息',
      name: '饮水信息',
      pid: 'zsxx-rtwater-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('饮水信息记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.water-list .bz-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.water-list').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
// 添加常规显像,oldPaneId已保存过的
function addConvention(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.convention-list .convention-item').length;
  // console.log('bzLen',bzLen)
  $('.convention-num').text(bzLen + 1);
  var activeTab = 'zsxx-convention-' + paneId;
  var newPaneBlock = appendConventionHtml(paneId, bzLen);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '常规显像',
      name: '常规显像',
      pid: 'zsxx-convention-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('常规显像记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.convention-list .convention-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.convention-list').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
// 添加延迟显像,oldPaneId已保存过的
function addDelay(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.delay-list .delay-item').length;
  // console.log('bzLen',bzLen)
  var activeTab = 'zsxx-delay-' + paneId;
  var newPaneBlock = appendDelayHtml(paneId, bzLen);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '延迟显像',
      name: '延迟显像',
      pid: 'zsxx-delay-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('延迟显像记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.delay-list .delay-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    // $('.delay-list').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
// 添加增强显像,oldPaneId已保存过的
function addEnhance(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.enhance-list .enhance-item').length;
  // console.log('bzLen',bzLen)
  var activeTab = 'zsxx-enhance-' + paneId;
  var newPaneBlock = appendEnhanceHtml(paneId, bzLen);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '增强显像',
      name: '增强显像',
      pid: 'zsxx-enhance-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('增强显像记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.enhance-list .enhance-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    // $('.enhance-list').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    newPaneBlock.find('.def-ck').click();
  }
}


/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}